<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Đây là migration cho MongoDB, chúng ta sẽ sử dụng raw MongoDB operations
        // hoặc có thể để trống vì MongoDB tự động tạo fields khi cần
        
        // Cập nhật tất cả tin tức hiện có để có trạng thái kiểm duyệt mặc định
        $tinTucCollection = app('mongodb')->collection('tin_tuc');
        
        // Cập nhật tất cả tin tức hiện có
        $tinTucCollection->updateMany(
            [], // Tất cả documents
            [
                '$set' => [
                    'TrangThaiKiemDuyet' => 1, // Đã duyệt (cho tin tức cũ)
                    'YeuCauKiemDuyet' => false,
                    'TuDongDuyet' => true,
                    'UserID_NguoiKiemDuyet' => null,
                    'NguoiKiemDuyet' => 'H<PERSON> thống (Migration)',
                    'NgayKiemDuyet' => new \MongoDB\BSON\UTCDateTime(),
                    'GhiChuKiemDuyet' => 'Tự động duyệt khi migration'
                ]
            ]
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Xóa các trường kiểm duyệt
        $tinTucCollection = app('mongodb')->collection('tin_tuc');
        
        $tinTucCollection->updateMany(
            [],
            [
                '$unset' => [
                    'TrangThaiKiemDuyet' => '',
                    'YeuCauKiemDuyet' => '',
                    'TuDongDuyet' => '',
                    'UserID_NguoiKiemDuyet' => '',
                    'NguoiKiemDuyet' => '',
                    'NgayKiemDuyet' => '',
                    'GhiChuKiemDuyet' => ''
                ]
            ]
        );
    }
};
