<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Tạo collection binh_luans trong MongoDB
        // MongoDB sẽ tự động tạo collection khi có document đầu tiên
        // Migration này chỉ để đánh dấu việc tạo collection

        // Có thể thêm index nếu cần
        Schema::connection('mongodb')->table('binh_luans', function ($collection) {
            // Index cho TinTucID để tìm kiếm nhanh bình luận theo tin tức
            $collection->index(['TinTucID' => 1]);

            // Index cho TrangThaiKiemDuyet để lọc bình luận theo trạng thái
            $collection->index(['TrangThaiKiemDuyet' => 1]);

            // Index cho NgayTao để sắp xếp theo thời gian
            $collection->index(['NgayTao' => -1]);

            // Index compound cho TinTucID và TrangThaiKiemDuyet
            $collection->index(['TinTucID' => 1, 'TrangThaiKiemDuyet' => 1]);

            // Index cho Email để tìm kiếm bình luận theo người dùng
            $collection->index(['Email' => 1]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('mongodb')->dropIfExists('binh_luans');
    }
};
