<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\CongThongTin\TinTuc;

class UpdateTinTucKiemDuyetSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Cập nhật tất cả tin tức hiện có để có trạng thái kiểm duyệt mặc định
        $tinTucs = TinTuc::all();
        
        foreach ($tinTucs as $tinTuc) {
            // Chỉ cập nhật nếu chưa có trạng thái kiểm duyệt
            if (!isset($tinTuc->TrangThaiKiemDuyet)) {
                $tinTuc->update([
                    'TrangThaiKiemDuyet' => 1, // Đã duyệt (cho tin tức cũ)
                    'YeuCauKiemDuyet' => false,
                    'TuDongDuyet' => true,
                    'UserID_NguoiKiemDuyet' => null,
                    'NguoiKiemDuyet' => '<PERSON><PERSON> thống (Migration)',
                    'NgayKiemDuyet' => now(),
                    'GhiChuKiemDuyet' => 'Tự động duyệt khi migration'
                ]);
            }
        }
        
        $this->command->info('Đã cập nhật ' . $tinTucs->count() . ' tin tức với trạng thái kiểm duyệt mặc định.');
    }
}
