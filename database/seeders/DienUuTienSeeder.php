<?php

namespace Database\Seeders;

use App\Models\DienUuTien;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DienUuTienSeeder extends Seeder
{
public function run()
    {
        $items = [
            ['MaDienUuTien' => '1', 'TenDienUuTien' => 'Ưu tiên 1', 'TrangThai' => true],
            ['MaDienUuTien' => '2', 'TenDienUuTien' => 'Ưu tiên 2', 'TrangThai' => true],
            ['MaDienUuTien' => '3', 'TenDienUuTien' => 'Ưu tiên 3', 'TrangThai' => true],
        ];

        foreach ($items as $item) {
            DienUuTien::updateOrCreate(
                ['MaDienUuTien' => $item['MaDienUuTien']],
                $item
            );
        }
    }
}