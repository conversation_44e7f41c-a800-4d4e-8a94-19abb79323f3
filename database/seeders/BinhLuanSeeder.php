<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\CongThongTin\BinhLuan;
use App\Models\CongThongTin\TinTuc;
use Carbon\Carbon;

class BinhLuanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Lấy một số tin tức để tạo bình luận mẫu
        $tinTucs = TinTuc::take(3)->get();

        if ($tinTucs->isEmpty()) {
            $this->command->info('Không có tin tức nào để tạo bình luận mẫu.');
            return;
        }

        $binhLuanMau = [
            [
                'HoVaTen' => 'Nguyễn Văn An',
                'Email' => '<EMAIL>',
                'BinhLuan' => 'Bài viết rất hay và bổ ích. Cảm ơn tác giả đã chia sẻ!',
                'TrangThaiKiemDuyet' => 1, // Đã duyệt
                'NguoiKiemDuyet' => 'Admin',
                'NgayKiemDuyet' => Carbon::now()->subDays(1),
            ],
            [
                'HoVaTen' => 'Trần Thị Bình',
                'Email' => '<EMAIL>',
                'BinhLuan' => 'Thông tin rất hữu ích, tôi đã áp dụng và thấy hiệu quả.',
                'TrangThaiKiemDuyet' => 1, // Đã duyệt
                'NguoiKiemDuyet' => 'Admin',
                'NgayKiemDuyet' => Carbon::now()->subHours(12),
            ],
            [
                'HoVaTen' => 'Lê Văn Cường',
                'Email' => '<EMAIL>',
                'BinhLuan' => 'Mong muốn có thêm nhiều bài viết như thế này.',
                'TrangThaiKiemDuyet' => 0, // Chờ duyệt
            ],
            [
                'HoVaTen' => 'Phạm Thị Dung',
                'Email' => '<EMAIL>',
                'BinhLuan' => 'Bài viết hay nhưng cần bổ sung thêm ví dụ cụ thể.',
                'TrangThaiKiemDuyet' => 1, // Đã duyệt
                'NguoiKiemDuyet' => 'Moderator',
                'NgayKiemDuyet' => Carbon::now()->subHours(6),
            ],
            [
                'HoVaTen' => 'Hoàng Văn Em',
                'Email' => '<EMAIL>',
                'BinhLuan' => 'Nội dung không phù hợp với chủ đề.',
                'TrangThaiKiemDuyet' => 2, // Từ chối
                'NguoiKiemDuyet' => 'Admin',
                'NgayKiemDuyet' => Carbon::now()->subHours(3),
                'GhiChuKiemDuyet' => 'Nội dung không liên quan đến chủ đề bài viết.',
            ],
        ];

        foreach ($tinTucs as $tinTuc) {
            foreach ($binhLuanMau as $binhLuan) {
                BinhLuan::create([
                    'TinTucID' => $tinTuc->_id,
                    'TieuDe' => $tinTuc->TieuDe,
                    'HoVaTen' => $binhLuan['HoVaTen'],
                    'Email' => $binhLuan['Email'],
                    'BinhLuan' => $binhLuan['BinhLuan'],
                    'TrangThaiKiemDuyet' => $binhLuan['TrangThaiKiemDuyet'],
                    'NguoiKiemDuyet' => $binhLuan['NguoiKiemDuyet'] ?? null,
                    'NgayKiemDuyet' => $binhLuan['NgayKiemDuyet'] ?? null,
                    'GhiChuKiemDuyet' => $binhLuan['GhiChuKiemDuyet'] ?? null,
                    'NgayTao' => Carbon::now()->subDays(rand(1, 7))->subHours(rand(1, 23)),
                    'DangSD' => true,
                ]);
            }
        }

        $this->command->info('Đã tạo ' . ($tinTucs->count() * count($binhLuanMau)) . ' bình luận mẫu.');
    }
}
