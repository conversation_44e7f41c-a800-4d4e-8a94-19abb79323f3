#mdLapHoSo_us .modal-dialog {
    min-width: 1000px;
}

#LoaiHoSo_us .form-selectgroup-label {
    font-size: 12px;
}

#LoaiHoSo_us .form-selectgroup-label {
    padding-right: 90px;
}

.line {
    flex-grow: 2;
    border-bottom: 1px solid #dadcde;
    margin: 0 10px;
    border-color: #07a607;
}

.label-text {
    color: #07a607;
}

.form-check .form-check-input {
    margin-left: unset !important;
}

.steps-vertical .step-item:before {
    top: var(--tblr-steps-dot-offset);
    left: -8px !important;
    transform: translate(0, 0);
    width: 35px;
    height: 35px;
}

#gridDonVi .tabulator-footer {
    display: none !important;
}

#gridDonVi .tabulator-data-tree-control {
    display: none !important;
}
.modal {
    z-index: 1202 !important;
}
.card {
    margin-bottom: 4px !important;
}
/* Child modals higher z-index */
#mdThemMoiHocSinh.modal,
#mdThemMoiHocSinh.modal .modal-dialog,
#mdChonHocSinh.modal,
#mdChonHocSinh.modal .modal-dialog {
    z-index: 1500 !important;
}

.modal-backdrop {
    z-index: 1201 !important;
}

.full-screen-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5); /* dark semi-transparent overlay */
    z-index: 1200; /* above other content */
    overflow-y: auto; /* scroll if content too tall */
    display: none; /* hidden by default */
}

.panel-content {
    background: white;
    border-radius: 0;
    padding-inline: 1rem;
    height: auto;
    overflow-y: auto;
}

#customSelect {
    position: relative; /* Make sure parent is positioned */
}

#customDropdown.show {
    display: block;
}

#mdXemDinhKem {
    z-index: 2000 !important;
}

/* make all step‐items non‐active text & counter orange */
.steps.steps-counter.steps-vertical .step-item:not(.active) {
    /* change the text */
    color: var(--main-color) !important;
}

/* and if you have a circle indicator, e.g. .step-counter */
.steps.steps-counter.steps-vertical .step-item:not(.active) .step-counter {
    background-color: var(--main-color) !important;
    border-color: var(--main-color) !important;
}

.steps.steps-counter.steps-vertical .step-item.active .step-title,
.steps.steps-counter.steps-vertical .step-item.active .step-desc {
    /* change the text */
    --tblr-text-opacity: 1;
    color: rgba(var(--tblr-primary-rgb), var(--tblr-text-opacity)) !important;
}

.steps.steps-counter.steps-vertical .step-desc {
    text-align: justify;
    text-justify: inter-word;
}
