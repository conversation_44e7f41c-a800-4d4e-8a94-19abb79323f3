/* News & Events Component Styles */

.news-section {
    padding: 15px 0;
    margin: 90px auto;
}

.news-section-header {
    text-align: center;
    margin-bottom: 50px;
}

.news-section-header h2 {
    font-size: 28px;
    font-weight: bold;
    margin: 0;
}

.news-cards-container {
    display: flex;
    gap: 15px;
    max-width: 1300px;
    margin: 0 auto;
    padding: 0 20px;
    overflow: hidden;
    position: relative;
}

.news-cards-wrapper {
    display: flex;
    gap: 15px;
    /* Mặc định: căn gi<PERSON>a khi có ít tin tức */
    width: auto;
    min-width: 100%;
    justify-content: center;
    flex-wrap: wrap;
}

/* Chỉ áp dụng animation khi có nhiều tin tức */
.news-cards-wrapper.has-multiple-news {
    animation: slideLeft 24s linear infinite;
    width: calc(365px * 8 + 15px * 7); /* <PERSON><PERSON> chỗ cho 8 cards + gaps */
    justify-content: flex-start;
    flex-wrap: nowrap;
    will-change: transform; /* <PERSON><PERSON>i <PERSON> performance */
}

.news-card {
    flex: 0 0 365px;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border: 2px solid #e5e7eb;
    transition: border-color 0.3s ease;
    position: relative;
}

.news-card:hover {
    border-color: #0066ff;
}

/* Card 1 - Orange/Yellow theme */
.card-1 .card-header {
    background: linear-gradient(135deg, #ff9500 0%, #ffb347 100%);
    padding: 12px 16px;
    position: relative;
}

.card-1 .card-tag {
    background: #0066ff;
    color: white;
    padding: 6px 12px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.card-1 .card-image {
    background: linear-gradient(135deg, #ff9500 0%, #ffb347 100%);
    height: 250px;
    position: relative;
    overflow: hidden;
}

.illustration-1 {
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 200"><defs><style>.cls-1{fill:%23fff;opacity:0.8;}.cls-2{fill:%23333;}.cls-3{fill:%230066ff;}</style></defs><rect class="cls-1" x="50" y="60" width="80" height="60" rx="8"/><rect class="cls-1" x="150" y="40" width="100" height="80" rx="8"/><rect class="cls-1" x="270" y="70" width="70" height="50" rx="8"/><circle class="cls-3" cx="100" cy="30" r="15"/><circle class="cls-3" cx="200" cy="150" r="12"/><circle class="cls-3" cx="320" cy="40" r="10"/><path class="cls-2" d="M80,100 L120,100 M90,110 L110,110"/><path class="cls-2" d="M170,70 L230,70 M170,85 L210,85"/></svg>') center/contain no-repeat;
    width: 100%;
    height: 100%;
    opacity: 0.9;
    transition: transform 0.3s ease;
}

.card-1:hover .illustration-1 {
    transform: scale(1.1);
}


/* Auto-slide animation */
@keyframes slideLeft {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(calc(-365px * 4 - 15px * 4)); /* Cuộn qua 4 cards */
    }
}

/* Pause animation on hover */
.news-cards-container:hover .news-cards-wrapper {
    animation-play-state: paused;
}

.card-content {
    padding: 20px;
    background: white;
}


.card-content h3 {
    font-size: 16px;
    font-weight: bold;
    color: #0066ff;
    margin: 0 0 15px 0;
    line-height: 1.4;
}

.card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 12px;
    color: #999;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 12px;
    flex-wrap: wrap;
    gap: 10px;
}

.card-meta .author {
    color: #999;
    font-size: 16px;
    font-weight: 800;
}

.card-meta .date {
    color: #999;
    font-size: 16px;
    font-weight: 800;
}

.card-meta .views {
    color: #28a745;
    font-size: 14px;
    font-weight: 600;
}

.category-tag {
  display: flex;
  align-items: center;
  color: #9B9B9B;
  font-size: 15px;
  font-weight: 500;
}

.category-tag .icon {
  margin-right: 10px;
  font-size: 20px;
  display: inline-block;
  transform: scaleX(1.2);
}


.card-description {
    font-size: 16px;
    color: #000;
    font-weight:500;
    margin: 0;
    text-align: justify;
}

/* Responsive design */
@media (max-width: 1100px) {
    .news-cards-container {
        max-width: 1000px;
    }

    .news-card {
        flex: 0 0 300px;
    }

    .news-cards-wrapper.has-multiple-news {
        width: calc(300px * 8 + 15px * 7);
    }

    @keyframes slideLeft {
        0% {
            transform: translateX(0);
        }
        100% {
            transform: translateX(calc(-300px * 4 - 15px * 4));
        }
    }
}

@media (max-width: 768px) {
    .news-cards-wrapper {
        animation-duration: 25s;
    }

    .news-card {
        flex: 0 0 280px;
    }

    .news-cards-wrapper.has-multiple-news {
        width: calc(280px * 8 + 15px * 7);
    }

    @keyframes slideLeft {
        0% {
            transform: translateX(0);
        }
        100% {
            transform: translateX(calc(-280px * 4 - 15px * 4));
        }
    }

    .news-section-header h2 {
        font-size: 24px;
    }

    .card-content {
        padding: 18px;
    }

    .card-content h3 {
        font-size: 15px;
    }

    .card-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}

@media (max-width: 480px) {
    .news-card {
        flex: 0 0 260px;
    }

    .news-cards-wrapper.has-multiple-news {
        animation-duration: 20s;
        width: calc(260px * 8 + 15px * 7);
    }

    @keyframes slideLeft {
        0% {
            transform: translateX(0);
        }
        100% {
            transform: translateX(calc(-260px * 4 - 15px * 4));
        }
    }

    .card-content {
        padding: 16px;
    }

    .card-content h3 {
        font-size: 14px;
        margin-bottom: 12px;
    }

    .card-description {
        font-size: 12px;
    }

    .card-meta {
        font-size: 11px;
        margin-bottom: 10px;
        padding-bottom: 10px;
    }

    .category-tag {
        font-size: 10px;
        padding: 4px 8px;
    }
}
