var tempthem = "them";
///////// PHÍM TẮT /////////
var hotKey = 0; // 1 thêm
$(document).on("keydown", function (e) {
    switch (e.keyCode) {
        case 113:
            if (hotKey == 0) $("#btnThemMoi").trigger("click");
            e.preventDefault();
            break;
        case 114:
            if (hotKey == 0) $(".nav-search-input").focus();
            e.preventDefault();
            break;
        case 115:
            if (hotKey == 1) $("#mdThemMoi").modal("hide");
            e.preventDefault();
            break;
        case 120:
            if (hotKey == 1) $("#btnLuuVaDong").trigger("click");
            e.preventDefault();
            break;
    }
});
$(document).on("shown.bs.modal", "#mdThemMoi", function () {
    hotKey = 1;
});
$(document).on("hidden.bs.modal", "#mdThemMoi", function () {
    hotKey = 0;
});
checkDangSD(".checkDangSD", "tieu_chis", "TrangThai");
//////////////////////////
$(async function () {
    LoadDataTable();
    await NTS.loadDataComboAsync({
        name: "#Font",
        type: "POST",
        ajaxUrl: window.Laravel.local.GetFontFamily_Combo,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText: 0,
        textShowTatCa: "Mặc định",
        showTatCa: !0,
    });

    await NTS.loadDataComboAsync({
        name: "#FontSize",
        type: "POST",
        ajaxUrl: window.Laravel.local.GetFontSize_Combo,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText: 0,
        textShowTatCa: "Mặc định",
        showTatCa: !0,
    });
    //$("#LoaiTieuChi").select2({ width: "100%" });
    $("#TextAlign").select2({ width: "100%" });
    $("#Float").select2({ width: "100%" });
    NTS.loadDataComboAsync({
        name: "#LoaiTieuChi",
        type: "POST",
        ajaxUrl: window.Laravel.local.getloaiphoivanbangchungchi,
        ajaxParam: {},
        columns: 2,
        indexValue: 0,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
});

$("#TrangThai").on("change", function () {
    UpdateLabelDangSD(this);
});
$(document).on("click", "#btnThemMoi", function () {
    ThemDuLieu();
});
$(document).on("click", ".btnSuaGrid1", function () {
    $("#TieuChiID").val($(this).attr("data"));
    SuaDuLieu($(this).attr("data"));
});
$(document).on("click", ".btnXoaGrid1", function () {
    $("#TieuChiID").val($(this).attr("data"));
    XoaDuLieu($(this).attr("data"));
});

async function SuaDuLieu(ID) {
    // if (!QuyenSua()) {
    //     return false;
    // }
    resetForm("#mdThemMoi");
    $("#tieuDeModal").text("Cập nhật thông tin tiêu chí");
    var result = NTS.getAjaxAPI(
        "GET",
        window.location.pathname + "/loaddulieusua",
        { id: ID }
    );
    if (!result.Err) {
        let data = result.Result[0];
        $("#MaTieuChi").value(data.MaTieuChi);
        $("#TenTieuChi").value(data.TenTieuChi);
        $("#MauSac").value(data.MauSac);
        $("#TenTieuChiHTML").value(data.TenTieuChiHTML);
        $("#StringSQL").value(data.StringSQL);
        $("#InNghien").value(data.InNghien);
        $("#InDam").value(data.InDam);
        $("#InHoa").value(data.InHoa);
        $("#GachChan").value(data.GachChan);
        $("#LoaiTieuChi").value(data.LoaiTieuChi);
        $("#Font").value(data.Font);
        $("#FontSize").value(data.FontSize);
        // Gán Margin nếu có
        debugger;
        if (data.Margin) {
            const margins = data.Margin.replaceAll("pt", "").split(" ");
            $("#Margin1").val(margins[0] || "0");
            $("#Margin2").val(margins[1] || "0");
            $("#Margin3").val(margins[2] || "0");
            $("#Margin4").val(margins[3] || "0");
        }
        $("#Class").value(data.Class);
        $("#TextAlign").value(data.TextAlign);
        $("#Float").value(data.Float);
        $("#TrangThai").value(data.TrangThai);
        $("#TieuChiID").value(data.TieuChiID);
        UpdateLabelDangSD("#TrangThai");
        $("#mdThemMoi").modal("show");
        tempthem = "sua";
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}

async function ThemDuLieu() {
    resetForm("#mdThemMoi");
    $("#mdThemMoi").modal("show");
    $("#tieuDeModal").text("Thêm mới thông tin tiêu chí");
    $("#TieuChiID").val("");
    $("#Class").value("col-md-12");
    $("#Margin1").val("0");
    $("#Margin2").val("0");
    $("#Margin3").val("0");
    $("#Margin4").val("0");
    $("#Font").val("Times New Roman");
    $("#FontSize").val("13");
    $("#TrangThai").prop("checked", true);
    UpdateLabelDangSD("#TrangThai");
    tempthem = "them";
}
var fmThaoTac = function (cell) {
    return formaterbtnThaoTac(cell.getData().id);
};
var fmDangSD = function (cell) {
    return formaterDangSD(cell.getValue(), cell.getData().id);
};
var table = new Tabulator("#Grid1", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "550",
    HeaderVertAlign: "center",
    groupBy: "LoaiTieuChiLabel",
    columns: [
        {
            title: '<i class="fa fa-ellipsis-h"></i>',
            headerHozAlign: "center",
            hozAlign: "center",
            formatter: fmThaoTac,
            width: 60,
            headerSort: false,
            frozen: true,
            vertAlign: "middle",
            print: false,
        },
        {
            title: "Mã",
            field: "MaTieuChi",
            width: 100,
            hozAlign: "left",
            formatter: "textarea",
            headerHozAlign: "center",
        },
        {
            title: "Tên tiêu chí",
            field: "TenTieuChi",
            width: 200,
            hozAlign: "left",
            formatter: "textarea",
            headerHozAlign: "center",
        },
        {
            title: "Class",
            field: "Class",
            width: 150,
            hozAlign: "center",
            headerHozAlign: "center",
        },
        {
            title: "Tên tiêu chí HTML",
            field: "TenTieuChiHTML",
            width: 200,
            formatter: "textarea",
            hozAlign: "left",
            headerHozAlign: "center",
        },
        {
            title: "Câu truy vấn",
            field: "StringSQL",
            width: 250,
            formatter: "textarea",
            hozAlign: "left",
            headerHozAlign: "center",
        },
        {
            title: "In nghiêng",
            field: "InNghien",
            width: 100,
            hozAlign: "center",
            formatter: "tickCross",
            hozAlign: "center",
            headerHozAlign: "center",
        },
        {
            title: "In đậm",
            field: "InDam",
            width: 100,
            hozAlign: "center",
            formatter: "tickCross",
            hozAlign: "center",
            headerHozAlign: "center",
        },
        {
            title: "Gạch chân",
            field: "GachChan",
            width: 100,
            hozAlign: "center",
            formatter: "tickCross",
            hozAlign: "center",
            headerHozAlign: "center",
        },
        {
            title: "In hoa",
            field: "InHoa",
            width: 100,
            hozAlign: "center",
            formatter: "tickCross",
            hozAlign: "center",
            headerHozAlign: "center",
        },
        {
            title: "Loại tiêu chí",
            field: "LoaiTieuChi",
            width: 120,
            hozAlign: "center",
            headerHozAlign: "center",
        },
        {
            title: "Font",
            field: "Font",
            width: 150,
            hozAlign: "center",
            headerHozAlign: "center",
        },
        {
            title: "FontSize",
            field: "FontSize",
            width: 100,
            hozAlign: "center",
            hozAlign: "center",
            headerHozAlign: "center",
        },
        {
            title: "Margin",
            field: "Margin",
            width: 120,
            hozAlign: "center",
            headerHozAlign: "center",
        },
        {
            title: "TextAlign",
            field: "TextAlign",
            width: 120,
            hozAlign: "center",
            headerHozAlign: "center",
        },
        {
            title: "Float",
            field: "Float",
            width: 100,
            hozAlign: "center",
            headerHozAlign: "center",
        },
        {
            title: "Màu sắc",
            field: "MauSac",
            width: 100,
            hozAlign: "center",
            headerHozAlign: "center",
        },
        {
            title: "Trạng thái sử dụng",
            field: "TrangThai",
            headerWordWrap: true,
            hozAlign: "center",
            vertAlign: "middle",
            formatter: fmDangSD,
            headerSort: false,
            width: 135,
            headerHozAlign: "center",
        },
    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});
table.on("rowDblClick", function (e, row) {
    var rowData = row.getData();
    SuaDuLieu(rowData.id);
});
$(document).on("keyup", "#timKiem", function (e) {
    var data = $(this).val();
    if (e.keyCode == "13") {
        table.setFilter(matchAny, { value: $(this).val() });
        table.redraw(true);
    }
});

async function LoadDataTable() {
    table.clearData();
    var result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/getall",
        {}
    );
    if (!result.Err) {
        table.setData(result.result);
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}

$("#btnLuuVaDong").on("click", function () {
    const validate = new NTSValidate("#mdThemMoi");
    if (!validate.trim().check()) return false;
    let marginTop = $("#Margin1").val() + "pt" || "0pt";
    let marginRight = $("#Margin2").val() + "pt" || "0pt";
    let marginBottom = $("#Margin3").val() + "pt" || "0pt";
    let marginLeft = $("#Margin4").val() + "pt" || "0pt";

    const payload = {
        loai: tempthem, // "them" hoặc "sua"
        MaTieuChi: $("#MaTieuChi").value(),
        TenTieuChi: $("#TenTieuChi").value(),

        MauSac: $("#MauSac").value(),
        TenTieuChiHTML: $("#TenTieuChiHTML").value(),
        StringSQL: $("#StringSQL").value(),
        InNghien: $("#InNghien").value(),
        InDam: $("#InDam").value(),
        InHoa: $("#InHoa").value(),
        GachChan: $("#GachChan").value(),
        LoaiTieuChi: $("#LoaiTieuChi").value(),
        Font: $("#Font").value() || "Times New Roman",
        FontSize: $("#FontSize").value() || "13",
        Margin: `${marginTop} ${marginRight} ${marginBottom} ${marginLeft}`,
        Class: $("#Class").value(),
        TextAlign: $("#TextAlign").value() || "left",
        Float: $("#Float").value() || "left",

        TrangThai: $("#TrangThai").value(),
        TieuChiID: $("#TieuChiID").value(),
    };
    var met = "POST";
    if (tempthem == "them") {
        met = "POST";
    } else {
        met = "PUT";
    }
    var result = NTS.getAjaxAPI(
        met,
        window.location.pathname + "/luuthongtin",
        payload
    );
    if (!result.Err) {
        LoadDataTable();
        NTS.thanhcong(result.Msg);
        $("#mdThemMoi").modal("hide");
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        return false;
    }
});

async function XoaDuLieu(ID) {
    var result_ktxoa = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.layouts.KiemTraXoa,
        {
            ma: ID,
            model: "DanhMuc\\TieuChi",
        }
    );
    if (!result_ktxoa.Err) {
        CanhBaoXoa(() => {
            var result = NTS.getAjaxAPI(
                "DELETE",
                window.location.pathname + "/xoa",
                { ma: ID }
            );
            if (!result.Err) {
                LoadDataTable();
                NTS.thanhcong(result.Msg);
            } else {
                result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
            }
        });
        return false;
    } else {
        NTS.canhbao(result_ktxoa.Msg);
        return false;
    }
}
