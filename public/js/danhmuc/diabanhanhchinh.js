var tempthem = "them";
///////// PHÍM TẮT /////////
var hotKey = 0; // 1 thêm
let commonComboConfig = {
    columns: 2,
    indexValue: 2,
    indexText: 0, // assuming your result rows are [id, code, name]
    indexText1: 1, // assuming your result rows are [id, code, name]
    textShowTatCa: "-Chọn-",
    showTatCa: true,
};
let comboLDB = () =>
    NTS.loadDataComboAsync({
        name: "#LoaiDiaBanHCID",
        type: "POST",
        ajaxUrl: window.Laravel.diabanhanhchinh.GetDSLoaiDiaBanHC,
        ajaxParam: {},
        ...commonComboConfig,
        showTatCa: false,
    });
let comboDBCha = () =>
    NTS.loadDataComboAsync({
        name: "#DiaBanHanhChinhID_Cha",
        type: "POST",
        ajaxUrl: window.Laravel.diabanhanhchinh.GetDSDiaBanHC,
        ajaxParam: { LoaiDiaBan: "00" },
        ...commonComboConfig,
    });
$(document).on("keydown", function (e) {
    switch (e.keyCode) {
        case 113:
            if (hotKey == 0) $("#btnThemMoi").trigger("click");
            e.preventDefault();
            break;
        case 114:
            if (hotKey == 0) $(".nav-search-input").focus();
            e.preventDefault();
            break;
        case 115:
            if (hotKey == 1) $("#mdThemMoi").modal("hide");
            e.preventDefault();
            break;
        case 120:
            if (hotKey == 1) $("#btnLuuVaDong").trigger("click");
            e.preventDefault();
            break;
    }
});
$(document).on("shown.bs.modal", "#mdThemMoi", function () {
    hotKey = 1;
});
$(document).on("hidden.bs.modal", "#mdThemMoi", function () {
    hotKey = 0;
});
checkDangSD(".checkDangSD", "dia_ban_hanh_chinhs", "TrangThai");
//////////////////////////
$(async function () {
    LoadDataTable();
    LoadCombos();
});

$("#DangSD").on("change", function () {
    UpdateLabelDangSD(this);
});
$(document).on("click", "#btnThemMoi", async function () {
    await LoadCombos();
    ThemDuLieu();
});
$(document).on("click", ".btnSuaGrid1", function () {
    $("#DiaBanID").val($(this).attr("data"));
    SuaDuLieu($(this).attr("data"));
});
$(document).on("click", ".btnXoaGrid1", function () {
    $("#DiaBanID").val($(this).attr("data"));
    XoaDuLieu($(this).attr("data"));
});

$(document).on("change", "#LoaiDiaBanHCID", function () {
    NTS.loadDataComboAsync({
        name: "#DiaBanHanhChinhID_Cha",
        type: "POST",
        ajaxUrl: window.Laravel.diabanhanhchinh.GetDSDiaBanHC,
        ajaxParam: { LoaiDiaBan: $("#LoaiDiaBanHCID option:selected").text() },
        ...commonComboConfig,
    });
});
async function LoadCombos() {
    await NTS.loadDataComboAsync({
        name: "#LoaiDiaBanHCID",
        type: "POST",
        ajaxUrl: window.Laravel.diabanhanhchinh.GetDSLoaiDiaBanHC,
        ajaxParam: {},
        ...commonComboConfig,
    });
    await NTS.loadDataComboAsync({
        name: "#DiaBanHanhChinhID_Cha",
        type: "POST",
        ajaxUrl: window.Laravel.diabanhanhchinh.GetDSDiaBanHC,
        ajaxParam: { LoaiDiaBan: "00" },
        ...commonComboConfig,
    });
}

async function SuaDuLieu(ID) {
    if (!QuyenSua()) {
        return false;
    }

    $("#tieuDeModal").text("Cập nhật thông tin địa bàn hành chính");
    var result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/loaddulieusua",
        { id: ID }
    );

    if (!result.Err) {
        let data = result.Result[0];
        $("#MaDiaBan").value(data.MaDiaBan);
        $("#TenDiaBan").value(data.TenDiaBan);
        $("#DangSD").value(data.TrangThai);
        UpdateLabelDangSD("#DangSD");
        $("#MoTa").value(data.MoTa);
        $("#DiaBanID").value(data.DiaBanID);
        $("#LoaiDiaBanHCID").value(data.LoaiDiaBanHCID);
        NTS.loadDataCombo({
            name: "#DiaBanHanhChinhID_Cha",
            type: "POST",
            ajaxUrl: window.Laravel.diabanhanhchinh.GetDSDiaBanHC,
            ajaxParam: {
                LoaiDiaBan: $("#LoaiDiaBanHCID option:selected").text(),
            },
            ...commonComboConfig,
        });
        setTimeout(() => {
            $("#DiaBanHanhChinhID_Cha").value(data.DiaBanID_Cha);
        }, 50);

        $("#mdThemMoi").modal("show");
        tempthem = "sua";
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}

async function ThemDuLieu() {
    if (!QuyenThem()) {
        return false;
    }
    $("#mdThemMoi").modal("show");
    $("#tieuDeModal").text("Thêm mới thông tin địa bàn hành chính");
    $("#DiaBanID").val("");
    resetForm("#mdThemMoi");
    $("#DangSD").prop("checked", true);
    UpdateLabelDangSD("#DangSD");
    $("#DiaBanHanhChinhID_Cha").value("");
    $("#LoaiDiaBanHCID").value("");
    tempthem = "them";
}
var fmThaoTac = function (cell) {
    return formaterbtnThaoTac(cell.getData().id);
};
var fmDangSD = function (cell) {
    return formaterDangSD(cell.getValue(), cell.getData().id);
};
var table = new Tabulator("#Grid1", {
    layout: "fitColumns",
    ajaxURL: window.location.pathname + "/getall", // URL for fetching data
    ajaxParams: {}, // initial parameters
    ajaxConfig: "GET",
    paginationMode: "remote", // enable remote pagination
    pagination: true,
    paginationSize: 100,
    paginationSizeSelector: [500, 100, 500, 1000],
    height: "550",
    HeaderVertAlign: "center",
    groupBy: ["TenDiaBanChaSort", "TenLoaiDiaBanHC"],
    columns: [
        {
            title: '<i class="fa fa-ellipsis-h"></i>',
            headerHozAlign: "center",
            hozAlign: "center",
            formatter: fmThaoTac,
            width: 60,
            headerSort: false,
            frozen: true,
            vertAlign: "middle",
            print: false,
        },
        {
            title: "Mã",
            field: "MaDiaBan",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 100,
        },
        {
            title: "Tên địa bàn hành chính",
            field: "TenDiaBan",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 250,
        },
        {
            title: "Thuộc địa bàn hành chính",
            field: "TenDiaBanCha",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 250,
        },
        {
            title: "Loại địa bàn",
            field: "TenLoaiDiaBanHC",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            width: 120,
        },
        {
            title: "Mô tả",
            field: "MoTa",
            hozAlign: "left",
            headerHozAlign: "center",
            formatter: "textarea",
            vertAlign: "middle",
            minWidth: 200,
        },
        {
            title: "Trạng thái sử dụng",
            field: "TrangThai",
            headerWordWrap: true,
            hozAlign: "center",
            vertAlign: "middle",
            formatter: fmDangSD,
            headerSort: false,
            width: 135,
            headerHozAlign: "center",
        },
    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});
table.on("rowDblClick", function (e, row) {
    var rowData = row.getData();
    SuaDuLieu(rowData.id);
});
$(document).on("keyup", "#timKiem", function (e) {
    var data = $(this).val();
    if (e.keyCode == "13") {
        if (data == "" || data == undefined) {
            data = $("#timKiem").value();
        }
        dulieuloc = data;
        table.setFilter([
            [
                {
                    field: "MaDiaBan",
                    type: "like",
                    value: data,
                },
                {
                    field: "TenDiaBan",
                    type: "like",
                    value: data,
                },
                {
                    field: "MoTa",
                    type: "like",
                    value: data,
                },
            ],
        ]);
    }
});

function LoadDataTable() {
    NTS.loadding();
    table.clearData();
    table.setData();
    NTS.unloadding();
}

$("#btnLuuVaDong").on("click", async function () {
    const validate = new NTSValidate("#mdThemMoi");
    if (!validate.trim().check()) return false;
    const payload = {
        loai: tempthem,
        MaDiaBan: $("#MaDiaBan").value(),
        TenDiaBan: $("#TenDiaBan").value(),
        DiaBanID_Cha: $("#DiaBanHanhChinhID_Cha").value(),
        LoaiDiaBanHCID: $("#LoaiDiaBanHCID").value(),
        MoTa: $("#MoTa").value(),
        TrangThai: $("#DangSD").value(),
        diabanId: $("#DiaBanID").value(),
    };
    var met = "POST";
    if (tempthem == "them") {
        met = "POST";
    } else {
        met = "PUT";
    }
    var result = await NTS.getAjaxAPIAsync(
        met,
        window.location.pathname + "/luuthongtin",
        payload
    );
    if (!result.Err) {
        LoadDataTable();
        NTS.thanhcong(result.Msg);
        $("#mdThemMoi").modal("hide");
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        return false;
    }
});

async function XoaDuLieu(ID) {
    if (!QuyenXoa()) {
        return false;
    }
    try {
        var result_ktxoa = await NTS.getAjaxAPIAsync(
            "GET",
            window.Laravel.layouts.KiemTraXoa,
            {
                ma: ID,
                model: "DanhMuc\\DiaBanHanhChinh",
            }
        );
        if (!result_ktxoa.Err) {
            CanhBaoXoa(async () => {
                var result = await NTS.getAjaxAPIAsync(
                    "DELETE",
                    window.location.pathname + "/xoa",
                    { ma: ID }
                );
                if (!result.Err) {
                    LoadDataTable();
                    NTS.thanhcong(result.Msg);
                } else {
                    result.canhbao
                        ? NTS.canhbao(result.Msg)
                        : NTS.loi(result.Msg);
                }
            });
            return false;
        } else {
            NTS.canhbao(result_ktxoa.Msg);
            return false;
        }
    } catch (e) {
        CanhBaoXoa(async () => {
            var result = await NTS.getAjaxAPIAsync(
                "DELETE",
                window.location.pathname + "/xoa",
                { ma: ID }
            );
            if (!result.Err) {
                LoadDataTable();
                NTS.thanhcong(result.Msg);
            } else {
                result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
            }
        });
        return false;
    }
}

//#region Nhận Excel
$(document).on("click", "#btnImport", function () {
    $("#mdNhanExcel").modal("show");
    $("#ChonFileNhanExcel").val("");
    $("#btnKiemTraFile").prop("disabled", true);
    return false;
});
// khi chọn file
$("#ChonFileNhanExcel").on("change", async function () {
    const fileInput = $("#ChonFileNhanExcel")[0];
    if (!fileInput.files.length) return;

    const file = fileInput.files[0];
    const ext = file.name.split(".").pop().toLowerCase();
    if (!["xlsx", "xls"].includes(ext)) {
        return NTS.canhbao("Chỉ cho phép file .xlsx hoặc .xls");
    }

    const form = new FormData();
    form.append("file", file);
    form.append("thumuc", "diabanhanhchinh");
    const res = await NTS.getAjaxAPIAsync(
        "POST",
        window.Laravel.diabanhanhchinh.uploadDocUrl,
        form
    );

    if (res.Err) {
        return NTS.canhbao(res.Msg);
    }

    // save the returned path
    Path = res.url[0];
    const fullUrl = new URL(`${Path}`, window.location.origin).href;

    await NTS.loadDataComboAsync({
        name: "#TenSheet",
        type: "POST",
        ajaxUrl: window.Laravel.diabanhanhchinh.loadTenSheetUrl,
        ajaxParam: { path: fullUrl },
        columns: 1,
        indexValue: 0,
        indexText: 0,
        textShowTatCa: "-Chọn-",
        showTatCa: true,
    });

    $("#btnKiemTraFile").prop("disabled", !this.files.length);
});

function TabulatorColumns() {
    return [
        // a checkbox selector column
        {
            formatter: "rowSelection",
            titleFormatter: "rowSelection",
            hozAlign: "center",
            headerSort: false,
            width: 40,
        },
        { title: "Mã địa bàn", formatter: "textarea", field: "MaDiaBan" },
        { title: "Tên địa bàn", formatter: "textarea", field: "TenDiaBan" },
        { title: "Loại địa bàn", formatter: "textarea", field: "LoaiDiaBan" },
        {
            title: "Thuộc địa bàn",
            formatter: "textarea",
            field: "TenDiaBan_Cha",
        },
        {
            title: "Trạng thái",
            field: "trangThai",
            headerFilterParams: {
                values: {
                    "Chờ nhận": "Chờ nhận",
                    "Nhận thành công": "Nhận thành công",
                    Lỗi: "Lỗi",
                },
            },
            formatter: "textarea",
        },
    ];
}

// then your existing
var GridExcel = new Tabulator("#GridExcel", {
    columns: TabulatorColumns(),
    layout: "fitColumns",
    pagination: false,
    // paginationSize: 50,
    // paginationSizeSelector: [true],
    height: "400",
    HeaderVertAlign: "center",
    locale: true,
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
    rowFormatter: function (row) {
        var s = row.getData().trangThai;
        if (s === "Chờ nhận") row.getElement().style.color = "#333";
        else if (s === "Nhận thành công")
            row.getElement().style.color = "#27ae60";
        else row.getElement().style.color = "#c00";
    },
    // …all your other options…
});
$(document).on("keyup", "#timkiem_gridNhapExcel", function (e) {
    var data = $(this).val();
    if (e.keyCode == "13") {
        if (data == "" || data == undefined) {
            data = $("#timkiem_gridNhapExcel").value();
        }
        dulieuloc = data;
        GridExcel.setFilter([
            [
                {
                    field: "MaDiaBan",
                    type: "like",
                    value: data,
                },
                {
                    field: "TenDiaBan",
                    type: "like",
                    value: data,
                },
                {
                    field: "LoaiDiaBan",
                    type: "like",
                    value: data,
                },
                ,
                {
                    field: "TenDiaBan_Cha",
                    type: "like",
                    value: data,
                },
            ],
        ]);
    }
});
// kiểm tra cấu trúc
$("#btnKiemTraFile").on("click", async () => {
    const sheet = $("#TenSheet").val();
    if (!sheet) return NTS.canhbao("Bạn chưa chọn sheet!");

    const res = await fetch(window.Laravel.diabanhanhchinh.checkExcel, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "X-CSRF-TOKEN": $("meta[name=csrf-token]").attr("content"),
        },
        body: JSON.stringify({
            path: Path, // ← server path, e.g. "/storage/uploads/…xlsx"
            sheet: sheet, // ← selected sheet name
        }),
    }).then((r) => r.json());

    if (res.Err) return NTS.canhbao(res.Msg);

    // now load the preview (also reading from server‐stored data)
    const data = await fetch(window.Laravel.diabanhanhchinh.loadExcel, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "X-CSRF-TOKEN": $("meta[name=csrf-token]").attr("content"),
        },
        body: JSON.stringify({ path: Path, sheet: sheet }),
    }).then((r) => r.json());

    GridExcel.setData(data);
});

// cuối cùng: import những dòng đang chọn
$("#btnLuuVaDongExcel").on("click", async () => {
    NTS.loadding();
    // get the row components that are selected
    const selectedRows = GridExcel.getSelectedRows().filter((row) => {
        const data = row.getData();
        return data.trangThai === "Chờ nhận";
    });
    // extract their positions for the POST
    const rows = selectedRows.map((r) => r.getPosition());

    if (rows.length == 0) {
        NTS.canhbao(
            `Vui lòng chọn dữ liệu cần nhận và dữ liệu có trạng thái "Chờ nhận".`
        );
        return false;
    }
    const res = await fetch(window.Laravel.diabanhanhchinh.importExcel, {
        method: "POST",
        headers: {
            "X-CSRF-TOKEN": $("meta[name=csrf-token]").attr("content"),
            "Content-Type": "application/json",
        },
        body: JSON.stringify({ rows }),
    }).then((r) => r.json());

    // now update each RowComponent
    selectedRows.forEach((rowComp) => {
        const pos = rowComp.getPosition();
        const result = res[pos];
        if (result.Err) {
            rowComp.update({ trangThai: result.Msg });
            rowComp.getElement().style.color = "red";
        } else {
            rowComp.update({ trangThai: "Nhận thành công" });
            rowComp.getElement().style.color = "green";
        }
        rowComp.deselect();
    });
    NTS.unloadding();
    LoadDataTable();
    NTS.thanhcong("Nhận dữ liệu thành công");
});

$("#dropzone")
    .on("dragover", (e) => e.preventDefault())
    .on("drop", (e) => {
        e.preventDefault();
        const files = e.originalEvent.dataTransfer.files;
        $("#ChonFileNhanExcel")[0].files = files;
        $("#ChonFileNhanExcel").trigger("change");
    });
//#endregion
