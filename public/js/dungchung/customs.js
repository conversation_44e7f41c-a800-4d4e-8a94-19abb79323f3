﻿async function QuyenThem() {
    if (await ntspermiss.them) {
        return true;
    }
    NTS.canhbao(
        "User bạn đang sử dụng không thể thực hiện thao tác thêm mới. Vui lòng kiểm tra lại."
    );
    return false;
}
async function QuyenSua() {
    if (await ntspermiss.sua) {
        return true;
    }
    NTS.canhbao(
        "User bạn đang sử dụng không thể thực hiện thao tác chỉnh sửa. Vui lòng kiểm tra lại."
    );
    return false;
}
async function QuyenXoa() {
    if (await ntspermiss.xoa) {
        return true;
    }
    NTS.canhbao(
        "User bạn đang sử dụng không thể thực hiện thao tác xóa. Vui lòng kiểm tra lại."
    );
    return false;
}

$(function () {
    if (document.getElementById("btnThemMoi")) {
        if (!ntspermiss.them) {
            $("#btnThemMoi").addClass("not-allowed");
            $("#btnThemMoi").prop("disabled", true);
        }
    }
});

function checkMacDinhSD(selector, tenBang, tenCot) {
    $(document).on("change", selector, function () {
        var inp = $(this);
        var id = $(this).attr("data");
        $.confirm({
            title: '<span class="text-dark">Cảnh báo!</span>',
            type: "blue",
            icon: "fa fa-question-circle",
            typeAnimated: true,
            theme: "material",
            columnClass: "col-md-5 col-md-offset-3 w-max-400px",
            content: NTS.CauCanhBaoDangSD,
            buttons: {
                confirm: {
                    text: '<i class="fa fa-check"></i> Chấp nhận',
                    btnClass: "btn-primary",
                    keys: ["enter"],
                    action: function () {
                        var result = NTS.getAjax(
                            "/DanhMuc/DungChung/LuuMacDinhSD",
                            {
                                ID: id,
                                strCotID: tenCot,
                                strBang: tenBang,
                                value: inp.prop("checked"),
                            }
                        );
                        if (!result.Err) {
                            LoadDataTable();
                            NTS.thanhcong(result.Msg);
                        } else {
                            NTS.loi(result.Msg);
                            inp.prop("checked", !inp.prop("checked"));
                        }
                    },
                },
                cancel: {
                    text: '<i class="fa fa-close"></i> Hủy bỏ',
                    btnClass: "btn-danger",
                    keys: ["esc"],
                    action: function () {
                        inp.prop("checked", !inp.prop("checked"));
                    },
                },
            },
        });
    });
}

async function checkDangSD(selector, tenBang, tenCot) {
    $(document).on("change.checkDangSD", selector, async function () {
        if (!(await ntspermiss.sua)) {
            $(this).click();
            NTS.canhbao("Bạn không có quyền sửa");
            return;
        }
        const inp = $(this);
        const id = inp.attr("data");
        const originalValue = inp.prop("checked");

        if (!id) {
            console.warn("No data attribute found on:", inp);
            inp.prop("checked", !originalValue);
            NTS.canhbao("Không tìm thấy ID bản ghi.");
            return;
        }

        $.confirm({
            title: '<span class="text-dark">Cảnh báo!</span>',
            type: "blue",
            icon: "fa fa-question-circle",
            typeAnimated: true,
            theme: "material",
            columnClass: "col-md-5 col-md-offset-3 w-max-400px",
            content:
                NTS.CauCanhBaoDangSD ||
                "Bạn có chắc muốn thay đổi trạng thái này?",
            buttons: {
                confirm: {
                    text: '<i class="fa fa-check"></i> Chấp nhận',
                    btnClass: "btn-primary",
                    keys: ["enter"],
                    action: async function () {
                        try {
                            const apiUrl = window.Laravel.layouts.luuDangSDUrl;
                            const response = await NTS.getAjaxAPIAsync(
                                "POST",
                                apiUrl,
                                {
                                    ID: id,
                                    strBang: tenBang,
                                    strCotID: tenCot,
                                    value: inp.prop("checked"),
                                }
                            );
                            if (response && !response.err) {
                                NTS.thanhcong(
                                    response.msg || "Cập nhật thành công."
                                );
                            } else {
                                inp.prop("checked", !originalValue);
                                const msg =
                                    response && response.msg
                                        ? response.msg
                                        : "Lỗi không xác định.";
                                response && response.canhbao
                                    ? NTS.canhbao(msg)
                                    : NTS.loi(msg);
                            }
                        } catch (error) {
                            inp.prop("checked", !originalValue);
                            NTS.loi("Lỗi hệ thống: " + error.message);
                        }
                    },
                },
                cancel: {
                    text: '<i class="fa fa-close"></i> Hủy bỏ',
                    btnClass: "btn-danger",
                    keys: ["esc"],
                    action: function () {
                        inp.prop("checked", !originalValue);
                    },
                },
            },
        });
    });
}
function checkDangSD3(selector, tenBang, tenCot, id) {
    $(document).on("change", selector, function () {
        var inp = $(this);
        if (!QuyenSua()) {
            inp.prop("checked", !inp.prop("checked"));
            return false;
        }
        $.confirm({
            title: '<span class="text-dark">Cảnh báo!</span>',
            type: "blue",
            icon: "fa fa-question-circle",
            typeAnimated: true,
            theme: "material",
            columnClass: "col-md-5 col-md-offset-3 w-max-400px",
            content: NTS.CauCanhBaoDangSD,
            buttons: {
                confirm: {
                    text: '<i class="fa fa-check"></i> Chấp nhận',
                    btnClass: "btn-primary",
                    keys: ["enter"],
                    action: function () {
                        var result = NTS.getAjax(
                            "/DanhMuc/DungChung/LuuDangSD",
                            {
                                ID: id,
                                strCotID: tenCot,
                                strBang: tenBang,
                                value: inp.prop("checked"),
                            }
                        );
                        if (!result.Err) {
                            NTS.thongbao(result.Msg);
                        } else {
                            NTS.thongbaoloi(result.Msg);
                            inp.prop("checked", !inp.prop("checked"));
                        }
                    },
                },
                cancel: {
                    text: '<i class="fa fa-close"></i> Hủy bỏ',
                    btnClass: "btn-danger",
                    keys: ["esc"],
                    action: function () {
                        inp.prop("checked", !inp.prop("checked"));
                    },
                },
            },
        });
    });
}
function formaterTrangThai(value, ID) {
    return (
        ` <div class="form-group">
                <label class="form-check form-switch">
                    <input class="form-check-input checkTrangThai" type="checkbox" data='${ID}' id="customCheckbox1${ID}" ` +
        (value ? "checked" : "") +
        `>
                    <label class="form-check-label" for="customCheckbox1${ID}"></label>
                </label>
            </div>`
    );
}
function CanhBaoXoa(DongY, Huy) {
    $.confirm({
        title: '<span class="text-dark">Cảnh báo!</span>',
        type: "red",
        icon: "fa fa-warning",
        typeAnimated: true,
        theme: "material",
        columnClass: "col-md-5 col-md-offset-3 w-max-400px",
        content: NTS.CauThongBaoXoa,
        buttons: {
            confirm: {
                text: '<i class="fa fa-check"></i> Chấp nhận',
                btnClass: "btn-primary",
                keys: ["shift"],
                action: DongY,
            },
            cancel: {
                text: '<i class="fa fa-close"></i> Hủy bỏ',
                btnClass: "btn-danger",
                keys: ["enter", "esc", "space"],
                action: Huy,
            },
        },
    });
}

function CanhBaoXoaNhieuDong(Xoa1Dong, XoaNhieuDong, Huy) {
    $.confirm({
        title: '<span class="text-dark">Cảnh báo!</span>',
        type: "red",
        icon: "fa fa-warning",
        typeAnimated: true,
        theme: "material",
        columnClass: "col-md-7 col-md-offset-3 w-max-700px",
        content:
            "<p>Bạn có thật sự muốn xóa dòng dữ liệu không? <br/>- Xóa 1 dòng đang chọn chọn <b>'Xóa dòng chọn'</b><br/>- Xóa tất cả các dòng đang chọn chọn <b>'Xóa các dòng đang chọn'</b><br/>- Không đồng ý chọn <b>'Hủy bỏ'</b>",
        buttons: {
            confirm: {
                text: '<i class="fa fa-trash"></i> Xóa dòng chọn',
                btnClass: "btn-blue",
                keys: ["enter"],
                action: Xoa1Dong,
            },
            confirm2: {
                text: '<i class="fa fa-check"></i> Xóa các dòng đang chọn',
                btnClass: "btn-warning",
                keys: ["enter", "shift"],
                action: XoaNhieuDong,
            },
            cancel: {
                text: '<i class="fa fa-close"></i> Hủy bỏ',
                btnClass: "btn-red",
                keys: ["esc", "space"],
                action: Huy,
            },
        },
    });
}

function CanhBaoXoaTatCa(Xoa1Dong, XoaNhieuDong, Huy) {
    $.confirm({
        title: '<span class="text-dark">Cảnh báo!</span>',
        type: "red",
        icon: "fa fa-warning",
        typeAnimated: true,
        theme: "material",
        columnClass: "col-md-7 col-md-offset-3 w-max-700px",
        content:
            "<p>Bạn có thật sự muốn xóa dòng dữ liệu không? <br/>- Xóa 1 dòng đang chọn chọn <b>'Xóa dòng chọn'</b><br/>- Xóa tất cả các dòng chọn <b>'Xóa tất cả'</b><br/>- Không đồng ý chọn <b>'Hủy bỏ'</b>",
        buttons: {
            confirm: {
                text: '<i class="fa fa-trash"></i> Xóa dòng chọn',
                btnClass: "btn-blue",
                keys: ["enter"],
                action: Xoa1Dong,
            },
            confirm2: {
                text: '<i class="fa fa-check"></i> Xóa tất cả',
                btnClass: "btn-warning",
                keys: ["enter", "shift"],
                action: XoaNhieuDong,
            },
            cancel: {
                text: '<i class="fa fa-close"></i> Hủy bỏ',
                btnClass: "btn-red",
                keys: ["esc", "space"],
                action: Huy,
            },
        },
    });
}

function CanhBaoXoaNhieuDongCustom(content, Xoa1Dong, XoaNhieuDong, Huy) {
    $.confirm({
        title: '<span class="text-dark">Cảnh báo!</span>',
        type: "red",
        icon: "fa fa-warning",
        typeAnimated: true,
        theme: "material",
        columnClass: "col-md-7 col-md-offset-3 w-max-700px",
        content: content,
        buttons: {
            confirm: {
                text: '<i class="fa fa-trash"></i> Xóa dòng chọn',
                btnClass: "btn-blue",
                keys: ["enter"],
                action: Xoa1Dong,
            },
            confirm2: {
                text: '<i class="fa fa-check"></i> Xóa các dòng đang chọn',
                btnClass: "btn-warning",
                keys: ["enter", "shift"],
                action: XoaNhieuDong,
            },
            cancel: {
                text: '<i class="fa fa-close"></i> Hủy bỏ',
                btnClass: "btn-red",
                keys: ["esc", "space"],
                action: Huy,
            },
        },
    });
}

function ThongBaoXuLyNhieuDong(content, MotDong, NhieuDong, text, Huy) {
    $.confirm({
        title: '<span class="text-dark">Cảnh báo!</span>',
        type: "red",
        icon: "fa fa-warning",
        typeAnimated: true,
        theme: "material",
        columnClass: "col-md-8 col-md-offset-3 w-max-800px",
        content: content,
        buttons: {
            confirm: {
                text: '<i class="fa fa-check"></i> ' + text[0],
                btnClass: "btn-blue",
                keys: ["enter"],
                action: MotDong,
            },
            confirm2: {
                text:
                    '<i style="vertical-align: middle; font-size: 20px;" class="bx bx-check-double"></i> ' +
                    text[1],
                btnClass: "btn-warning",
                keys: ["enter", "shift"],
                action: NhieuDong,
            },
            cancel: {
                text: '<i class="fa fa-close"></i> Hủy bỏ',
                btnClass: "btn-red",
                keys: ["esc", "space"],
                action: Huy,
            },
        },
    });
}
function ThongBaoXuLyNhieuDongv2(col, content, MotDong, NhieuDong, text, Huy) {
    $.confirm({
        title: '<span class="text-dark">Cảnh báo!</span>',
        type: "red",
        icon: "fa fa-warning",
        typeAnimated: true,
        theme: "material",
        columnClass: "col-md-" + col + " col-md-offset-3 w-max-800px",
        content: content,
        buttons: {
            confirm: {
                text: '<i class="fa fa-check"></i> ' + text[0],
                btnClass: "btn-blue",
                keys: ["enter"],
                action: MotDong,
            },
            confirm2: {
                text:
                    '<i style="vertical-align: middle; font-size: 20px;" class="bx bx-check-double"></i> ' +
                    text[1],
                btnClass: "btn-warning",
                keys: ["enter", "shift"],
                action: NhieuDong,
            },
            cancel: {
                text: '<i class="fa fa-close"></i> Hủy bỏ',
                btnClass: "btn-red",
                keys: ["esc", "space"],
                action: Huy,
            },
        },
    });
}
function CanhBaoDuLieuDangSD(result_ktxoa) {
    $.confirm({
        title: '<span class="text-dark">Cảnh báo!</span>',
        type: "red",
        icon: "fa fa-warning",
        typeAnimated: true,
        theme: "material",
        columnClass: "col-md-5 col-md-offset-3 w-max-400px",
        content:
            "Dữ liệu này đang được sử dụng. Không thể xoá, danh sách kèm theo:<br><table>" +
            result_ktxoa +
            "</table>",
        buttons: {
            cancel: {
                text: '<i class="fa fa-close"></i> Đóng',
                btnClass: "btn-danger",
                keys: ["enter", "esc", "space"],
            },
        },
    });
}

function CanhBaoXoaDinhKem(DongY, Huy) {
    $.confirm({
        title: '<span class="text-dark">Cảnh báo!</span>',
        type: "red",
        icon: "fa fa-warning",
        typeAnimated: true,
        theme: "material",
        columnClass: "col-md-5 col-md-offset-3 w-max-400px",
        content: NTS.CauThongBaoXoaDinhKem,
        buttons: {
            confirm: {
                text: '<i class="fa fa-check"></i> Chấp nhận',
                btnClass: "btn-primary",
                keys: ["shift"],
                action: DongY,
            },
            cancel: {
                text: '<i class="fa fa-close"></i> Hủy bỏ',
                btnClass: "btn-danger",
                keys: ["enter", "esc", "space"],
                action: Huy,
            },
        },
    });
}

function resetForm(selector) {
    $(selector)
        .find('input:not([type="radio"]), textarea, select')
        .each(function () {
            $(this).value("");
        });
    focusInput(selector);
}

function UpdateLabelDangSD(selector) {
    // if (!QuyenThem()) {
    //     return false;
    // }
    // if (!QuyenSua()) {
    //     return false;
    // }
    // if (!QuyenXoa()) {
    //     return false;
    // }
    UpdateLabel(selector, "Đang sử dụng", "Ngưng sử dụng");
}
function UpdateLabelConHieuLuc(selector) {
    if (!QuyenThem()) {
        return false;
    }
    if (!QuyenSua()) {
        return false;
    }
    if (!QuyenXoa()) {
        return false;
    }
    UpdateLabel(selector, "Còn hiệu lực", "Hết hiệu lực");
}
function UpdateLabelDangCongTac(selector) {
    if (!QuyenThem()) {
        return false;
    }
    if (!QuyenSua()) {
        return false;
    }
    if (!QuyenXoa()) {
        return false;
    }
    UpdateLabel(selector, "Đang công tác", "Ngừng công tác");
}
function UpdateLabelDangThucHien(selector) {
    if (!QuyenThem()) {
        return false;
    }
    if (!QuyenSua()) {
        return false;
    }
    if (!QuyenXoa()) {
        return false;
    }
    UpdateLabel(selector, "Hoàn thành", "Đang thực hiện");
}
function UpdateLabel(selector, text1, text2) {
    let _switch = $(selector);
    $("label[for='" + _switch.prop("id") + "']").html(
        _switch.prop("checked") ? text1 : text2
    );
}

function UpdateLabelDangSD3(selector, textcheck, textuncheck) {
    let _switch = $(selector);
    $("label[for='" + _switch.prop("id") + "']").html(
        _switch.prop("checked") ? textcheck : textuncheck
    );
}

function formaterMacDinhSD(value, ID) {
    return (
        `<div class="form-check d-flex justify-content-center align-items-center"><input data='${ID}' class="form-check-input checkMacDinhSD" type="checkbox" value="" ` +
        (value ? "checked" : "") +
        `></div>`
    );
}

function formaterDangSD(value, ID) {
    return (
        ` <div class="form-group">
                <label class="form-check form-switch" style="margin-bottom:unset">
                    <input class="form-check-input checkDangSD" type="checkbox" data='${ID}' id="customCheckbox1${ID}" ` +
        (value ? "checked" : "") +
        ` >
                    <label class="form-check-label" for="customCheckbox1${ID}"></label>
                </label>
            </div>`
    );
}

function formaterbtnThaoTac(ID) {
    return `<div class="show-or-hide"><a class='text-primary btnSuaGrid1' title="Sửa" data='${ID}'><i class="fa fa-pencil"></i></a></b>&ensp;<a class='text-danger btnXoaGrid1' title="Xoá" data='${ID}'><i class='fa fa-trash-o'></i></a></div>`;
}

function formaterbtnThaoTac2(ID, btnXoaGrid1) {
    return `<div class="show-or-hide col-md-12 no-padding" style="padding-right: 0px; padding-left: 0px; "><a class='text-danger ${btnXoaGrid1} nts-btn-xoa' title="Xoá" data='${ID}'><i class='fa fa-trash'></i></a></div>`;
}
function formaterbtnThaoTac3(ID, btnSuaGrid1, btnXoaGrid1) {
    return `<div class="show-or-hide col-md-12 no-padding" style="padding-right: 0px; padding-left: 0px; "><a class='text-primary ${btnSuaGrid1} nts-btn-sua' title="Sửa" data='${ID}'><i class='fa fa-pencil'></i></a></b>&ensp;<a class='text-danger ${btnXoaGrid1} nts-btn-xoa' title="Xoá" data='${ID}'><i class='fa fa-trash'></i></a></div>`;
}

function formaterbtnThaoTacXoa(ID, btnXoaGrid1) {
    return `<div class="show-or-hide"><a class='text-danger ${btnXoaGrid1}' title="Xoá" data='${ID}'><i class='fa fa-trash-o'></i></a></div>`;
}

function formaterbtnThaoTacXem(ID, btnXemGrid) {
    return `<div class="show-or-hide"><a class='text-success ${btnXemGrid}' title="Xem" data='${ID}'><i class='fa fa-eye'></i></a></div>`;
}

function formaterbtnThaoTac3(ID, btnSuaGrid1, btnXoaGrid1) {
    return `<div class="show-or-hide"><a class='text-primary ${btnSuaGrid1}' title="Sửa" data='${ID}'><i class='fa fa-pencil'></i></a></b>&ensp;<a class='text-danger ${btnXoaGrid1}' title="Xoá" data='${ID}'><i class='fa fa-trash'></i></a></div>`;
}

function formatterPrintMacDinhSD(cell) {
    return (
        `<div class="d-flex justify-content-center align-items-center"><input type="checkbox" ` +
        (cell.getValue() ? "checked" : "") +
        `/></div>`
    );
}
function formatterPrintDangSD(cell) {
    return (
        `<div class="d-flex justify-content-center align-items-center"><input type="checkbox" ` +
        (cell.getValue() ? "checked" : "") +
        `/></div>`
    );
}
function formaterbtnXemDinhKem(ID, loai) {
    return `<div><button class='btn btn-sm btn-success text-xemdinhkem' title="Xem đính kèm" data='${ID}' onclick="XemDinhKem('${loai}','${ID}');return false;"><i class='fa fa-paperclip'></i>&ensp;Xem đính kèm</button></div>`;
}

function focusInput(selector) {
    $(selector + " :input:visible:enabled:first").focus();
}

function showMsg(ExecPremiss) {
    if (typeof ExecPremiss == "object" && ExecPremiss.Msg != "") {
        ExecPremiss.CanhBao
            ? NTS.canhbao(ExecPremiss.Msg)
            : NTS.thongbaoloi(ExecPremiss.Msg);
    }
}

function AnHienMenu(an) {
    if (an) {
        $("aside.navbar").hide();
        $(".page-wrapper").css("margin-left", "0px");
        $("header.navbar").css("margin-left", "0px");
    } else {
        $("aside.navbar").show();
        $(".page-wrapper").css("margin-left", "15rem");
        $("header.navbar").css("margin-left", "15rem");
    }
    //an ? $('#layout-menu').hide() : $('#layout-menu').show();
}

function checkLaNoiBat(selector, tenBang, tenCot) {
    $(document).on("change", selector, function () {
        var inp = $(this);
        var id = $(this).attr("data");
        $.confirm({
            title: '<span class="text-dark">Cảnh báo!</span>',
            type: "blue",
            icon: "fa fa-question-circle",
            typeAnimated: true,
            theme: "material",
            columnClass: "col-md-5 col-md-offset-3 w-max-400px",
            content: NTS.CauCanhBaoDangSD,
            buttons: {
                confirm: {
                    text: '<i class="fa fa-check"></i> Chấp nhận',
                    btnClass: "btn-primary",
                    keys: ["enter"],
                    action: function () {
                        var result = NTS.getAjaxAPIAsync(
                            "/DanhMuc/DungChung/LuuNoiBat",
                            {
                                ID: id,
                                strCotID: tenCot,
                                strBang: tenBang,
                                value: inp.prop("checked"),
                            }
                        );

                        if (!result.Err) {
                            NTS.thongbao(result.Msg);
                        } else {
                            NTS.thongbaoloi(result.Msg);

                            inp.prop("checked", !inp.prop("checked"));
                        }
                    },
                },
                cancel: {
                    text: '<i class="fa fa-close"></i> Hủy bỏ',
                    btnClass: "btn-danger",
                    keys: ["esc"],
                    action: function () {
                        inp.prop("checked", !inp.prop("checked"));
                    },
                },
            },
        });
    });
}

function formaterLaNoiBat(value, ID) {
    return (
        `<div class="form-check form-switch d-flex justify-content-center align-items-center"><input data='${ID}' class="form-check-input float-start checkLaNoiBat" type="checkbox" ` +
        (value ? "checked" : "") +
        `/></div>`
    );
}

function checkHienThiHDSD(selector, tenBang, tenCot) {
    $(document).on("change", selector, function () {
        var inp = $(this);
        var id = $(this).attr("data");
        $.confirm({
            title: '<span class="text-dark">Cảnh báo!</span>',
            type: "blue",
            icon: "fa fa-question-circle",
            typeAnimated: true,
            theme: "material",
            columnClass: "col-md-5 col-md-offset-3 w-max-400px",
            content: NTS.CauCanhBaoDangSD,
            buttons: {
                confirm: {
                    text: '<i class="fa fa-check"></i> Chấp nhận',
                    btnClass: "btn-primary",
                    keys: ["enter"],
                    action: function () {
                        var result = NTS.getAjax(
                            "/DanhMuc/DungChung/LuuHuongDanSuDung",
                            {
                                ID: id,
                                strCotID: tenCot,
                                strBang: tenBang,
                                value: inp.prop("checked"),
                            }
                        );

                        if (!result.Err) {
                            NTS.thongbao(result.Msg);
                        } else {
                            NTS.thongbaoloi(result.Msg);

                            inp.prop("checked", !inp.prop("checked"));
                        }
                    },
                },
                cancel: {
                    text: '<i class="fa fa-close"></i> Hủy bỏ',
                    btnClass: "btn-danger",
                    keys: ["esc"],
                    action: function () {
                        inp.prop("checked", !inp.prop("checked"));
                    },
                },
            },
        });
    });
}
function formaterHienThiHDSD(value, ID) {
    return (
        `<div class="form-check form-switch d-flex justify-content-center align-items-center"><input data='${ID}' class="form-check-input float-start checkHienThiHDSD" type="checkbox" ` +
        (value ? "checked" : "") +
        `/></div>`
    );
}
$(".modal").each(function (index) {
    $(this)
        .find("select")
        .attr(
            "data-dropdown-parent",
            "#" + $(this).attr("id") + " .modal-body"
        );
});
$(document).on("select2:open", function () {
    try {
        setTimeout(function () {
            document.querySelector(".select2-search__field").focus();
        });
    } catch (e) {}
});

//hàm load combo địa bàn theo đơn vị đăng nhập và có phân quyền theo nhóm người dùng
function PhanQuyenComBoDiaBan(TinhID_id, HuyenID_id, XaID_id, ThonID_id) {
    var TinhID_PhanQuyen = "";
    var HuyenID_PhanQuyen = "";
    var XaID_PhanQuyen = "";
    var ThonID_PhanQuyen = "";
    const donvi = NTS.getAjax("/DanhMuc/DungChung/GetDataThietLapDonVi", {});
    if (donvi.length != 0) {
        let data = donvi[0];
        if (
            data.TinhID != "" &&
            data.TinhID != "********-0000-0000-0000-********0000" &&
            data.TinhID != null
        ) {
            TinhID_PhanQuyen = data.TinhID;
            HuyenID_PhanQuyen = data.HuyenID;
            XaID_PhanQuyen = data.XaID;
            ThonID_PhanQuyen = data.ThonID;
        } else {
            const donviThaoTac = NTS.getAjax(
                "/DanhMuc/DungChung/GetThongTinDonViThaoTac",
                {}
            );
            if (!donviThaoTac.Err && donviThaoTac.Result != null) {
                let data = donviThaoTac.Result[0];
                TinhID_PhanQuyen = data.TinhID;
                HuyenID_PhanQuyen = data.HuyenID;
                XaID_PhanQuyen = data.XaID;
                ThonID_PhanQuyen = data.ThonID;
            }
        }
    } else {
        const donviThaoTac = NTS.getAjax(
            "/DanhMuc/DungChung/GetThongTinDonViThaoTac",
            {}
        );
        if (!donviThaoTac.Err && donviThaoTac.Result != null) {
            let data = donviThaoTac.Result[0];
            TinhID_PhanQuyen = data.TinhID;
            HuyenID_PhanQuyen = data.HuyenID;
            XaID_PhanQuyen = data.XaID;
            ThonID_PhanQuyen = data.ThonID;
        }
    }
    $("#" + TinhID_id).value(TinhID_PhanQuyen);
    const NhomNguoiDung = NTS.getAjax(
        "/DanhMuc/DungChung/GetNhomNguoiDung",
        {}
    );
    if (parseInt(NhomNguoiDung) > 1) {
        $("#" + TinhID_id).prop("disabled", true);
    }

    if (NhomNguoiDung == "2") {
        setTimeout(function () {
            $("#" + HuyenID_id).value(HuyenID_PhanQuyen);
            $("#" + HuyenID_id).prop("disabled", true);
        }, 10);
        setTimeout(function () {
            $("#" + XaID_id).value(XaID_PhanQuyen);
        }, 20);
        setTimeout(function () {
            $("#" + ThonID_id).value(ThonID_PhanQuyen);
        }, 30);
    } else if (NhomNguoiDung == "3") {
        setTimeout(function () {
            $("#" + HuyenID_id).value(HuyenID_PhanQuyen);
            $("#" + HuyenID_id).prop("disabled", true);
        }, 10);
        setTimeout(function () {
            $("#" + XaID_id).value(XaID_PhanQuyen);
        }, 20);
        setTimeout(function () {
            $("#" + ThonID_id).value(ThonID_PhanQuyen);
        }, 30);
    } else if (NhomNguoiDung == "4") {
        setTimeout(function () {
            $("#" + HuyenID_id).value(HuyenID_PhanQuyen);
            $("#" + HuyenID_id).prop("disabled", true);
        }, 10);
        setTimeout(function () {
            $("#" + XaID_id).value(XaID_PhanQuyen);
            $("#" + XaID_id).prop("disabled", true);
        }, 20);
        setTimeout(function () {
            if (ThonID_id != "") {
                $("#" + ThonID_id).value(ThonID_PhanQuyen);
                $("#" + ThonID_id).prop("disabled", true);
            }
        }, 30);
    } else if (NhomNguoiDung == "0") {
        setTimeout(function () {
            $("#" + HuyenID_id).value(HuyenID_PhanQuyen);
        }, 10);
        setTimeout(function () {
            $("#" + XaID_id).value(XaID_PhanQuyen);
        }, 20);
        setTimeout(function () {
            $("#" + ThonID_id).value(ThonID_PhanQuyen);
        }, 30);
    } else if (NhomNguoiDung == "1") {
        setTimeout(function () {
            $("#" + HuyenID_id).value(HuyenID_PhanQuyen);
        }, 10);
        setTimeout(function () {
            $("#" + XaID_id).value(XaID_PhanQuyen);
        }, 20);
        setTimeout(function () {
            $("#" + ThonID_id).value(ThonID_PhanQuyen);
        }, 30);
    }
}

function PhanQuyenComBoDiaBanKhongDisable(
    TinhID_id,
    HuyenID_id,
    XaID_id,
    ThonID_id
) {
    var TinhID_PhanQuyen = "";
    var HuyenID_PhanQuyen = "";
    var XaID_PhanQuyen = "";
    var ThonID_PhanQuyen = "";

    const donvi = NTS.getAjax("/DanhMuc/DungChung/GetDataThietLapDonVi", {});
    if (!donvi.Err && donvi.Result != null) {
        let data = donvi.Result[0];
        if (
            data.TinhID != "" &&
            data.TinhID != "********-0000-0000-0000-********0000" &&
            data.TinhID != null
        ) {
            TinhID_PhanQuyen = data.TinhID;
            HuyenID_PhanQuyen = data.HuyenID;
            XaID_PhanQuyen = data.XaID;
            ThonID_PhanQuyen = data.ThonID;
        } else {
            const donviThaoTac = NTS.getAjax(
                "/DanhMuc/DungChung/GetThongTinDonViThaoTac",
                {}
            );
            if (!donviThaoTac.Err && donviThaoTac.Result != null) {
                let data = donviThaoTac.Result[0];
                TinhID_PhanQuyen = data.TinhID;
                HuyenID_PhanQuyen = data.HuyenID;
                XaID_PhanQuyen = data.XaID;
                ThonID_PhanQuyen = data.ThonID;
            }
        }
    } else {
        const donviThaoTac = NTS.getAjax(
            "/DanhMuc/DungChung/GetThongTinDonViThaoTac",
            {}
        );
        if (!donviThaoTac.Err && donviThaoTac.Result != null) {
            let data = donviThaoTac.Result[0];
            TinhID_PhanQuyen = data.TinhID;
            HuyenID_PhanQuyen = data.HuyenID;
            XaID_PhanQuyen = data.XaID;
            ThonID_PhanQuyen = data.ThonID;
        }
    }
    $("#" + TinhID_id).value(TinhID_PhanQuyen);
    const NhomNguoiDung = NTS.getAjax(
        "/DanhMuc/DungChung/GetNhomNguoiDung",
        {}
    );
    if (parseInt(NhomNguoiDung) > 1) {
        /*    $('#' + TinhID_id).prop('disabled', true)*/
    }
    if (NhomNguoiDung == "2") {
        setTimeout(function () {
            $("#" + HuyenID_id).value(HuyenID_PhanQuyen);
            /*     $('#' + HuyenID_id).prop('disabled', true)*/
        }, 10);
    } else if (NhomNguoiDung == "3") {
        setTimeout(function () {
            $("#" + HuyenID_id).value(HuyenID_PhanQuyen);
            /*     $('#' + HuyenID_id).prop('disabled', true)*/
        }, 10);
        setTimeout(function () {
            $("#" + XaID_id).value(XaID_PhanQuyen);
            /*    $('#' + XaID_id).prop('disabled', true)*/
        }, 20);
    } else if (NhomNguoiDung == "4") {
        setTimeout(function () {
            $("#" + HuyenID_id).value(HuyenID_PhanQuyen);
            /*     $('#' + HuyenID_id).prop('disabled', true)*/
        }, 10);
        setTimeout(function () {
            $("#" + XaID_id).value(XaID_PhanQuyen);
            /*            $('#' + XaID_id).prop('disabled', true)*/
        }, 20);
        setTimeout(function () {
            if (ThonID_id != "") {
                $("#" + ThonID_id).value(ThonID_PhanQuyen);
                /*     $('#' + ThonID_id).prop('disabled', true)*/
            }
        }, 30);
    } else if (NhomNguoiDung == "0") {
        setTimeout(function () {
            $("#" + HuyenID_id).value(HuyenID_PhanQuyen);
        }, 10);
        setTimeout(function () {
            $("#" + XaID_id).value(XaID_PhanQuyen);
        }, 20);
        setTimeout(function () {
            $("#" + ThonID_id).value(ThonID_PhanQuyen);
        }, 30);
    } else if (NhomNguoiDung == "1") {
        setTimeout(function () {
            $("#" + HuyenID_id).value(HuyenID_PhanQuyen);
        }, 10);
        setTimeout(function () {
            $("#" + XaID_id).value(XaID_PhanQuyen);
        }, 20);
        setTimeout(function () {
            $("#" + ThonID_id).value(ThonID_PhanQuyen);
        }, 30);
    }
}

function GridKhongCoDuLieu(ID) {
    if (
        $(
            "#" +
                ID +
                " .tabulator-tableholder .tabulator-placeholder .tabulator-placeholder-contents"
        ).html() == "Không có dữ liệu"
    ) {
        $(
            "#" +
                ID +
                " .tabulator-tableholder .tabulator-placeholder .tabulator-placeholder-contents"
        ).empty();
        $(
            "#" +
                ID +
                " .tabulator-tableholder .tabulator-placeholder .tabulator-placeholder-contents"
        ).prepend(
            $("<img>", {
                id: "theImg",
                src: "/Images/no-data.svg",
                style: "width: 300px;height: 200px;",
            })
        );
    }
}

//giải mã
async function GiaiMaHoaDuLieu(ID) {
    var result = await NTS.getAjaxAsync("/DanhMuc/DungChung/Decrypt", {
        id: ID,
    });
    if (result.length > 0) {
        return result;
    } else {
        return ID;
    }
}

function MaHoaDuLieu(ID) {
    var result = NTS.getAjax("/DanhMuc/DungChung/Encrypt", { id: ID });
    if (result.length > 0) {
        return result;
    } else {
        return ID;
    }
}
function KiemTraToKhai() {
    //Kiểm tra đường dẫn
    if (window.location.href.replaceAll("#", "").indexOf("?id=") == -1) {
        //Đang thêm mới
        return "0";
    } else {
        //Đang cập nhật
        var url = window.location.href.replaceAll("#", "");
        var id = url.substr(url.indexOf("?id=") + 4, url.length);
        return id;
    }
}
function HienThiTieuDeToKhaiBTXH() {
    if (window.location.href.replaceAll("#", "").indexOf("?id=") == -1) {
        var url = window.location.href.replaceAll("#", "");
        var duongDan = "/" + url.split("/")[3] + "/" + url.split("/")[4];
        var result = NTS.getAjax("/DanhMuc/DungChung/GetTenToKhaiMau", {
            duongDan: duongDan,
        });
        if (result.Result.length > 0) {
            return result.Result[0].TenToKhaiMau;
        } else {
            return "";
        }
    } else {
        var url = window.location.href.replaceAll("#", "").split("?id=")[0];
        var duongDan = "/" + url.split("/")[3] + "/" + url.split("/")[4];
        var result = NTS.getAjax("/DanhMuc/DungChung/GetTenToKhaiMau", {
            duongDan: duongDan,
        });
        if (result.Result.length > 0) {
            return result.Result[0].TenToKhaiMau;
        } else {
            return "";
        }
    }
}
function LoadComboBoxMacDinhSD(SelectID, TenBang, TenCotLayDuLieu) {
    try {
        const result = NTS.getAjax("/DanhMuc/DungChung/LoadComboMacDinhSD", {
            TenBang: TenBang,
        }).Result[0][TenCotLayDuLieu];
        if (result != "") {
            $(SelectID).value(result);
        }
    } catch (e) {}
}

function KiemTraThaoTac_ThuocDonVi(ID, TenBang, TenCotID) {
    const result = NTS.getAjax("/DanhMuc/DungChung/GetDonVi_CuaDoiTuong", {
        ID: ID,
        TenBang: TenBang,
        TenCotID: TenCotID,
    });
    if (result.Result == "TRUE") {
        return true;
    }
    return false;
}

/**
 * Thu thập dữ liệu từ một bảng Tabulator, chỉ lấy các cột được in (print !== false)
 * và tự động sử dụng formatter (nếu có) để lấy nhãn hiển thị.
 *
 * @param {Tabulator} table - Tham chiếu đến instance Tabulator.
 * @returns {{headings: string[], rows: Object[]}}
 *   - headings: Mảng tiêu đề cột (string).
 *   - rows: Mảng các đối tượng, mỗi đối tượng đại diện cho một dòng trong bảng,
 *           key là tiêu đề cột và value là giá trị đã format hoặc giá trị gốc.
 *
 * Chi tiết hoạt động:
 * 1. Lọc ra các cột cần lấy:
 *    - Phải có `field` (không phải cột ảo).
 *    - Bỏ qua cột có `field === "actions"`.
 *    - Bỏ qua cột có `print: false`.
 * 2. Build mảng `headings` từ `column.getDefinition().title`.
 * 3. Duyệt qua từng RowComponent trong bảng:
 *    - Lấy `rowData` gốc bằng `rowComp.getData()`.
 *    - Với mỗi cột:
 *        • Nếu cột có `formatter`, lấy nội dung đã render
 *          bằng `rowComp.getCell(field).getElement().textContent`.
 *        • Nếu không, đọc trực tiếp `rowData[field]`.
 *        • Riêng cột “Trạng thái”, luôn sử dụng nhãn:
 *            – `Đang sử dụng` khi giá trị true.
 *            – `Ngưng sử dụng` khi giá trị false.
 *    - Gán nhãn đó vào đối tượng kết quả với key là tiêu đề cột.
 * 4. Trả về object chứa `headings` và `rows`.
 */
function gatherTableDatav1(table) {
    const cols = table.getColumns().filter((c) => {
        const def = c.getDefinition();
        return (
            def.field && // has a real field
            def.field !== "actions" && // skip your action‐column
            def.print !== false // skip any print:false
        );
    });

    const headings = cols.map(
        (c) => c.getDefinition().title ?? c.getDefinition().field
    );
    const rows = table.getRows().map((rowComp) => {
        const rowData = rowComp.getData();
        const obj = {};

        cols.forEach((col) => {
            const def = col.getDefinition();
            const field = def.field;
            let label;

            if (def.formatter) {
                // use the *formatted* cell text
                const cellComp = rowComp.getCell(field);
                label = cellComp.getElement().textContent.trim();
            } else {
                // plain raw value
                label = rowData[field];
            }
            if (def.title === "Trạng thái") {
                label = rowData[field] ? "Đang sử dụng" : "Ngưng sử dụng";
            }

            obj[def.title ?? field] = label;
        });

        return obj;
    });

    return { headings, rows };
}
/**
 * postAndDownloadv1
 * -----------------
 * Mô tả:
 *   Gửi POST tới URL với payload JSON, nhận về Blob và khởi tạo download tự động.
 *
 * Tham số:
 *   @param {string} url       — Đường dẫn API nhận file trả về.
 *   @param {any}    data      — Payload JSON cần gửi.
 *   @param {string} filename  — Tên file khi download.
 *
 * Trả về:
 *   Promise<void>
 *
 * Cảnh báo:
 *   - Lấy CSRF token từ meta[name="csrf-token"].
 *   - Nếu response không ok, báo lỗi bằng alert.
 */
async function postAndDownloadv1(url, data, filename) {
    const token = document.querySelector('meta[name="csrf-token"]').content;
    const res = await fetch(url, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "X-CSRF-TOKEN": token,
        },
        body: JSON.stringify(data),
    });
    if (!res.ok) return alert("Export lỗi: " + res.statusText);
    const blob = await res.blob();
    const blobUrl = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = blobUrl;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    a.remove();
    URL.revokeObjectURL(blobUrl);
}

/**
 * previewExportv1
 * ---------------
 * Mô tả:
 *   Xem trước nội dung export dưới dạng PDF hoặc Excel trong modal,
 *   sau đó user có thể bấm “Download” để tải file thật.
 *
 * Tham số:
 *   @param {string} type — "pdf" hoặc khác (mặc định Excel).
 *   @param {Tabulator} data — instance Tabulator để xuất dữ liệu.
 *
 * Cách hoạt động:
 *   1. Gọi gatherTableDatav1 để lấy headings + rows.
 *   2. Nếu type === "pdf":
 *      • POST về exportPdfUrl, nhận Blob PDF, hiển thị trong <iframe>.
 *      • Gán sự kiện cho nút Download để gọi lại postAndDownloadv1.
 *   3. Nếu type !== "pdf" (Excel):
 *      • Tạo bảng HTML tạm trong modal.
 *      • Gán nút Download để gọi postAndDownloadv1 tới exportExcelUrl.
 *   4. Hiển thị modal bằng bootstrap.Modal.
 */
/**
 * Show export preview (PDF or Excel).
 *
 * @param {'pdf'|'excel'} type
 * @param {Array|Object} data     // whatever your gatherTableDatav1 needs
 * @param {boolean}   isLocal    // true  = full dataset
 *                                // false = only current page
 */
async function previewExportv1(type, data, isLocal = false) {
    // let your gatherer know whether it should pull ALL pages or just this one
    const { headings, rows } = gatherTableDatav1(data, isLocal);

    // adjust the title so the user can see if it's only current page
    const scopeLabel = isLocal ? "toàn bộ dữ liệu" : "trang hiện tại";

    if (type === "pdf") {
        const res = await fetch(window.Laravel.layouts.exportPdfUrl, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": document.querySelector(
                    'meta[name="csrf-token"]'
                ).content,
            },
            body: JSON.stringify({ headings, rows, isLocal }),
        });
        if (!res.ok) return alert("Error generating PDF");
        const blob = await res.blob();
        const blobUrl = URL.createObjectURL(blob);

        document.getElementById(
            "exportPreviewTitle"
        ).textContent = `Xem trước khi in (${scopeLabel})`;
        document.getElementById(
            "previewContent"
        ).innerHTML = `<iframe style="width:100%;height:75vh;border:none" src="${blobUrl}"></iframe>`;

        document.getElementById("confirmDownloadBtn").onclick = () => {
            postAndDownloadv1(
                window.Laravel.layouts.exportPdfUrl,
                { headings, rows, isLocal },
                `export_${Date.now()}.pdf`
            );
            bootstrap.Modal.getInstance(
                document.getElementById("exportPreviewModal")
            ).hide();
        };
    } else {
        // Excel preview
        let html =
            '<div class="table-responsive"><table class="table table-bordered"><thead><tr>';
        headings.forEach((h) => (html += `<th>${h}</th>`));
        html += "</tr></thead><tbody>";
        rows.forEach((r) => {
            html += "<tr>";
            headings.forEach((h) => (html += `<td>${r[h] || ""}</td>`));
            html += "</tr>";
        });
        html += "</tbody></table></div>";

        document.getElementById(
            "exportPreviewTitle"
        ).textContent = `Xem trước dữ liệu (${scopeLabel})`;
        document.getElementById("previewContent").innerHTML = html;

        document.getElementById("confirmDownloadBtn").onclick = () => {
            postAndDownloadv1(
                window.Laravel.layouts.exportExcelUrl,
                { headings, rows, isLocal },
                `export_${Date.now()}.xlsx`
            );
            bootstrap.Modal.getInstance(
                document.getElementById("exportPreviewModal")
            ).hide();
        };
    }

    bootstrap.Modal.getOrCreateInstance(
        document.getElementById("exportPreviewModal")
    ).show();
}

/**
 * LayDanhSachIconNew
 * ------------------
 * Mô tả:
 *   Trả về và append danh sách các icon FontAwesome vào container .box.
 *
 * Thao tác:
 *   1. Khởi tạo mảng res chứa các tên class của icon.
 *   2. Với mỗi item, tạo <div> chứa <i class="fa ..."> và append vào .box.
 *
 * Lưu ý:
 *   - Container .box phải tồn tại trên DOM.
 *   - Mỗi icon gắn attribute data để lấy tên về khi cần.
 */
function LayDanhSachIconNew() {
    var res = [
        "fa-500px",
        "fa-address-book",
        "fa-address-book-o",
        "fa-address-card",
        "fa-address-card-o",
        "fa-adjust",
        "fa-adn",
        "fa-align-center",
        "fa-align-justify",
        "fa-align-left",
        "fa-align-right",
        "fa-amazon",
        "fa-ambulance",
        "fa-american-sign-language-interpreting",
        "fa-anchor",
        "fa-android",
        "fa-angellist",
        "fa-angle-double-down",
        "fa-angle-double-left",
        "fa-angle-double-right",
        "fa-angle-double-up",
        "fa-angle-down",
        "fa-angle-left",
        "fa-angle-right",
        "fa-angle-up",
        "fa-apple",
        "fa-archive",
        "fa-area-chart",
        "fa-arrow-circle-down",
        "fa-arrow-circle-left",
        "fa-arrow-circle-o-down",
        "fa-arrow-circle-o-left",
        "fa-arrow-circle-o-right",
        "fa-arrow-circle-o-up",
        "fa-arrow-circle-right",
        "fa-arrow-circle-up",
        "fa-arrow-down",
        "fa-arrow-left",
        "fa-arrow-right",
        "fa-arrow-up",
        "fa-arrows",
        "fa-arrows-alt",
        "fa-arrows-h",
        "fa-arrows-v",
        "fa-asl-interpreting",
        "fa-assistive-listening-systems",
        "fa-asterisk",
        "fa-at",
        "fa-audio-description",
        "fa-automobile",
        "fa-backward",
        "fa-balance-scale",
        "fa-ban",
        "fa-bandcamp",
        "fa-bank",
        "fa-bar-chart",
        "fa-bar-chart-o",
        "fa-barcode",
        "fa-bars",
        "fa-bath",
        "fa-bathtub",
        "fa-battery",
        "fa-battery-0",
        "fa-battery-1",
        "fa-battery-2",
        "fa-battery-3",
        "fa-battery-4",
        "fa-battery-empty",
        "fa-battery-full",
        "fa-battery-half",
        "fa-battery-quarter",
        "fa-battery-three-quarters",
        "fa-bed",
        "fa-beer",
        "fa-behance",
        "fa-behance-square",
        "fa-bell",
        "fa-bell-o",
        "fa-bell-slash",
        "fa-bell-slash-o",
        "fa-bicycle",
        "fa-binoculars",
        "fa-birthday-cake",
        "fa-bitbucket",
        "fa-bitbucket-square",
        "fa-bitcoin",
        "fa-black-tie",
        "fa-blind",
        "fa-bluetooth",
        "fa-bluetooth-b",
        "fa-bold",
        "fa-bolt",
        "fa-bomb",
        "fa-book",
        "fa-bookmark",
        "fa-bookmark-o",
        "fa-braille",
        "fa-briefcase",
        "fa-btc",
        "fa-bug",
        "fa-building",
        "fa-building-o",
        "fa-bullhorn",
        "fa-bullseye",
        "fa-bus",
        "fa-buysellads",
        "fa-cab",
        "fa-calculator",
        "fa-calendar",
        "fa-calendar-check-o",
        "fa-calendar-minus-o",
        "fa-calendar-o",
        "fa-calendar-plus-o",
        "fa-calendar-times-o",
        "fa-camera",
        "fa-camera-retro",
        "fa-car",
        "fa-caret-down",
        "fa-caret-left",
        "fa-caret-right",
        "fa-caret-square-o-down",
        "fa-caret-square-o-left",
        "fa-caret-square-o-right",
        "fa-caret-square-o-up",
        "fa-caret-up",
        "fa-cart-arrow-down",
        "fa-cart-plus",
        "fa-cc",
        "fa-cc-amex",
        "fa-cc-diners-club",
        "fa-cc-discover",
        "fa-cc-jcb",
        "fa-cc-mastercard",
        "fa-cc-paypal",
        "fa-cc-stripe",
        "fa-cc-visa",
        "fa-certificate",
        "fa-chain",
        "fa-chain-broken",
        "fa-check",
        "fa-check-circle",
        "fa-check-circle-o",
        "fa-check-square",
        "fa-check-square-o",
        "fa-chevron-circle-down",
        "fa-chevron-circle-left",
        "fa-chevron-circle-right",
        "fa-chevron-circle-up",
        "fa-chevron-down",
        "fa-chevron-left",
        "fa-chevron-right",
        "fa-chevron-up",
        "fa-child",
        "fa-chrome",
        "fa-circle",
        "fa-circle-o",
        "fa-circle-o-notch",
        "fa-circle-thin",
        "fa-clipboard",
        "fa-clock-o",
        "fa-clone",
        "fa-close",
        "fa-cloud",
        "fa-cloud-download",
        "fa-cloud-upload",
        "fa-cny",
        "fa-code",
        "fa-code-fork",
        "fa-codepen",
        "fa-codiepie",
        "fa-coffee",
        "fa-cog",
        "fa-cogs",
        "fa-columns",
        "fa-comment",
        "fa-comment-o",
        "fa-commenting",
        "fa-commenting-o",
        "fa-comments",
        "fa-comments-o",
        "fa-compass",
        "fa-compress",
        "fa-connectdevelop",
        "fa-contao",
        "fa-copy",
        "fa-copyright",
        "fa-creative-commons",
        "fa-credit-card",
        "fa-credit-card-alt",
        "fa-crop",
        "fa-crosshairs",
        "fa-css3",
        "fa-cube",
        "fa-cubes",
        "fa-cut",
        "fa-cutlery",
        "fa-dashboard",
        "fa-dashcube",
        "fa-database",
        "fa-deaf",
        "fa-deafness",
        "fa-dedent",
        "fa-delicious",
        "fa-desktop",
        "fa-deviantart",
        "fa-diamond",
        "fa-digg",
        "fa-dollar",
        "fa-dot-circle-o",
        "fa-download",
        "fa-dribbble",
        "fa-drivers-license",
        "fa-drivers-license-o",
        "fa-dropbox",
        "fa-drupal",
        "fa-edge",
        "fa-edit",
        "fa-eercast",
        "fa-eject",
        "fa-ellipsis-h",
        "fa-ellipsis-v",
        "fa-empire",
        "fa-envelope",
        "fa-envelope-o",
        "fa-envelope-open",
        "fa-envelope-open-o",
        "fa-envelope-square",
        "fa-envira",
        "fa-eraser",
        "fa-etsy",
        "fa-eur",
        "fa-euro",
        "fa-exchange",
        "fa-exclamation",
        "fa-exclamation-circle",
        "fa-exclamation-triangle",
        "fa-expand",
        "fa-expeditedssl",
        "fa-external-link",
        "fa-external-link-square",
        "fa-eye",
        "fa-eye-slash",
        "fa-eyedropper",
        "fa-fa",
        "fa-facebook",
        "fa-facebook-f",
        "fa-facebook-official",
        "fa-facebook-square",
        "fa-fast-backward",
        "fa-fast-forward",
        "fa-fax",
        "fa-feed",
        "fa-female",
        "fa-fighter-jet",
        "fa-file",
        "fa-file-archive-o",
        "fa-file-audio-o",
        "fa-file-code-o",
        "fa-file-excel-o",
        "fa-file-image-o",
        "fa-file-movie-o",
        "fa-file-o",
        "fa-file-pdf-o",
        "fa-file-photo-o",
        "fa-file-picture-o",
        "fa-file-powerpoint-o",
        "fa-file-sound-o",
        "fa-file-text",
        "fa-file-text-o",
        "fa-file-video-o",
        "fa-file-word-o",
        "fa-file-zip-o",
        "fa-files-o",
        "fa-film",
        "fa-filter",
        "fa-fire",
        "fa-fire-extinguisher",
        "fa-firefox",
        "fa-first-order",
        "fa-flag",
        "fa-flag-checkered",
        "fa-flag-o",
        "fa-flash",
        "fa-flask",
        "fa-flickr",
        "fa-floppy-o",
        "fa-folder",
        "fa-folder-o",
        "fa-folder-open",
        "fa-folder-open-o",
        "fa-font",
        "fa-font-awesome",
        "fa-fonticons",
        "fa-fort-awesome",
        "fa-forumbee",
        "fa-forward",
        "fa-foursquare",
        "fa-free-code-camp",
        "fa-frown-o",
        "fa-futbol-o",
        "fa-gamepad",
        "fa-gavel",
        "fa-gbp",
        "fa-ge",
        "fa-gear",
        "fa-gears",
        "fa-genderless",
        "fa-get-pocket",
        "fa-gg",
        "fa-gg-circle",
        "fa-gift",
        "fa-git",
        "fa-git-square",
        "fa-github",
        "fa-github-alt",
        "fa-github-square",
        "fa-gitlab",
        "fa-gittip",
        "fa-glass",
        "fa-glide",
        "fa-glide-g",
        "fa-globe",
        "fa-google",
        "fa-google-plus",
        "fa-google-plus-circle",
        "fa-google-plus-official",
        "fa-google-plus-square",
        "fa-google-wallet",
        "fa-graduation-cap",
        "fa-gratipay",
        "fa-grav",
        "fa-group",
        "fa-h-square",
        "fa-hacker-news",
        "fa-hand-grab-o",
        "fa-hand-lizard-o",
        "fa-hand-o-down",
        "fa-hand-o-left",
        "fa-hand-o-right",
        "fa-hand-o-up",
        "fa-hand-paper-o",
        "fa-hand-peace-o",
        "fa-hand-pointer-o",
        "fa-hand-rock-o",
        "fa-hand-scissors-o",
        "fa-hand-spock-o",
        "fa-hand-stop-o",
        "fa-handshake-o",
        "fa-hard-of-hearing",
        "fa-hashtag",
        "fa-hdd-o",
        "fa-header",
        "fa-headphones",
        "fa-heart",
        "fa-heart-o",
        "fa-heartbeat",
        "fa-history",
        "fa-home",
        "fa-hospital-o",
        "fa-hotel",
        "fa-hourglass",
        "fa-hourglass-1",
        "fa-hourglass-2",
        "fa-hourglass-3",
        "fa-hourglass-end",
        "fa-hourglass-half",
        "fa-hourglass-o",
        "fa-hourglass-start",
        "fa-houzz",
        "fa-html5",
        "fa-i-cursor",
        "fa-id-badge",
        "fa-id-card",
        "fa-id-card-o",
        "fa-ils",
        "fa-image",
        "fa-imdb",
        "fa-inbox",
        "fa-indent",
        "fa-industry",
        "fa-info",
        "fa-info-circle",
        "fa-inr",
        "fa-instagram",
        "fa-institution",
        "fa-internet-explorer",
        "fa-intersex",
        "fa-ioxhost",
        "fa-italic",
        "fa-joomla",
        "fa-jpy",
        "fa-jsfiddle",
        "fa-key",
        "fa-keyboard-o",
        "fa-krw",
        "fa-language",
        "fa-laptop",
        "fa-lastfm",
        "fa-lastfm-square",
        "fa-leaf",
        "fa-leanpub",
        "fa-legal",
        "fa-lemon-o",
        "fa-level-down",
        "fa-level-up",
        "fa-life-bouy",
        "fa-life-buoy",
        "fa-life-ring",
        "fa-life-saver",
        "fa-lightbulb-o",
        "fa-line-chart",
        "fa-link",
        "fa-linkedin",
        "fa-linkedin-square",
        "fa-linode",
        "fa-linux",
        "fa-list",
        "fa-list-alt",
        "fa-list-ol",
        "fa-list-ul",
        "fa-location-arrow",
        "fa-lock",
        "fa-long-arrow-down",
        "fa-long-arrow-left",
        "fa-long-arrow-right",
        "fa-long-arrow-up",
        "fa-low-vision",
        "fa-magic",
        "fa-magnet",
        "fa-mail-forward",
        "fa-mail-reply",
        "fa-mail-reply-all",
        "fa-male",
        "fa-map",
        "fa-map-marker",
        "fa-map-o",
        "fa-map-pin",
        "fa-map-signs",
        "fa-mars",
        "fa-mars-double",
        "fa-mars-stroke",
        "fa-mars-stroke-h",
        "fa-mars-stroke-v",
        "fa-maxcdn",
        "fa-meanpath",
        "fa-medium",
        "fa-medkit",
        "fa-meetup",
        "fa-meh-o",
        "fa-mercury",
        "fa-microchip",
        "fa-microphone",
        "fa-microphone-slash",
        "fa-minus",
        "fa-minus-circle",
        "fa-minus-square",
        "fa-minus-square-o",
        "fa-mixcloud",
        "fa-mobile",
        "fa-mobile-phone",
        "fa-modx",
        "fa-money",
        "fa-moon-o",
        "fa-mortar-board",
        "fa-motorcycle",
        "fa-mouse-pointer",
        "fa-music",
        "fa-navicon",
        "fa-neuter",
        "fa-newspaper-o",
        "fa-object-group",
        "fa-object-ungroup",
        "fa-odnoklassniki",
        "fa-odnoklassniki-square",
        "fa-opencart",
        "fa-openid",
        "fa-opera",
        "fa-optin-monster",
        "fa-outdent",
        "fa-pagelines",
        "fa-paint-brush",
        "fa-paper-plane",
        "fa-paper-plane-o",
        "fa-paperclip",
        "fa-paragraph",
        "fa-paste",
        "fa-pause",
        "fa-pause-circle",
        "fa-pause-circle-o",
        "fa-paw",
        "fa-paypal",
        "fa-pencil",
        "fa-pencil-square",
        "fa-pencil-square-o",
        "fa-percent",
        "fa-phone",
        "fa-phone-square",
        "fa-photo",
        "fa-picture-o",
        "fa-pie-chart",
        "fa-pied-piper",
        "fa-pied-piper-alt",
        "fa-pied-piper-pp",
        "fa-pinterest",
        "fa-pinterest-p",
        "fa-pinterest-square",
        "fa-plane",
        "fa-play",
        "fa-play-circle",
        "fa-play-circle-o",
        "fa-plug",
        "fa-plus",
        "fa-plus-circle",
        "fa-plus-square",
        "fa-plus-square-o",
        "fa-podcast",
        "fa-power-off",
        "fa-print",
        "fa-product-hunt",
        "fa-puzzle-piece",
        "fa-qq",
        "fa-qrcode",
        "fa-question",
        "fa-question-circle",
        "fa-question-circle-o",
        "fa-quora",
        "fa-quote-left",
        "fa-quote-right",
        "fa-ra",
        "fa-random",
        "fa-ravelry",
        "fa-rebel",
        "fa-recycle",
        "fa-reddit",
        "fa-reddit-alien",
        "fa-reddit-square",
        "fa-refresh",
        "fa-registered",
        "fa-remove",
        "fa-renren",
        "fa-reorder",
        "fa-repeat",
        "fa-reply",
        "fa-reply-all",
        "fa-resistance",
        "fa-retweet",
        "fa-rmb",
        "fa-road",
        "fa-rocket",
        "fa-rotate-left",
        "fa-rotate-right",
        "fa-rouble",
        "fa-rss",
        "fa-rss-square",
        "fa-rub",
        "fa-ruble",
        "fa-rupee",
        "fa-s15",
        "fa-safari",
        "fa-save",
        "fa-scissors",
        "fa-scribd",
        "fa-search",
        "fa-search-minus",
        "fa-search-plus",
        "fa-sellsy",
        "fa-send",
        "fa-send-o",
        "fa-server",
        "fa-share",
        "fa-share-alt",
        "fa-share-alt-square",
        "fa-share-square",
        "fa-share-square-o",
        "fa-shekel",
        "fa-sheqel",
        "fa-shield",
        "fa-ship",
        "fa-shirtsinbulk",
        "fa-shopping-bag",
        "fa-shopping-basket",
        "fa-shopping-cart",
        "fa-shower",
        "fa-sign-in",
        "fa-sign-language",
        "fa-sign-out",
        "fa-signal",
        "fa-signing",
        "fa-simplybuilt",
        "fa-sitemap",
        "fa-skyatlas",
        "fa-skype",
        "fa-slack",
        "fa-sliders",
        "fa-slideshare",
        "fa-smile-o",
        "fa-snapchat",
        "fa-snapchat-ghost",
        "fa-snapchat-square",
        "fa-snowflake-o",
        "fa-soccer-ball-o",
        "fa-sort",
        "fa-sort-alpha-asc",
        "fa-sort-alpha-desc",
        "fa-sort-amount-asc",
        "fa-sort-amount-desc",
        "fa-sort-asc",
        "fa-sort-desc",
        "fa-sort-down",
        "fa-sort-numeric-asc",
        "fa-sort-numeric-desc",
        "fa-sort-up",
        "fa-soundcloud",
        "fa-space-shuttle",
        "fa-spinner",
        "fa-spoon",
        "fa-spotify",
        "fa-square",
        "fa-square-o",
        "fa-stack-exchange",
        "fa-stack-overflow",
        "fa-star",
        "fa-star-half",
        "fa-star-half-empty",
        "fa-star-half-full",
        "fa-star-half-o",
        "fa-star-o",
        "fa-steam",
        "fa-steam-square",
        "fa-step-backward",
        "fa-step-forward",
        "fa-stethoscope",
        "fa-sticky-note",
        "fa-sticky-note-o",
        "fa-stop",
        "fa-stop-circle",
        "fa-stop-circle-o",
        "fa-street-view",
        "fa-strikethrough",
        "fa-stumbleupon",
        "fa-stumbleupon-circle",
        "fa-subscript",
        "fa-subway",
        "fa-suitcase",
        "fa-sun-o",
        "fa-superpowers",
        "fa-superscript",
        "fa-support",
        "fa-table",
        "fa-tablet",
        "fa-tachometer",
        "fa-tag",
        "fa-tags",
        "fa-tasks",
        "fa-taxi",
        "fa-telegram",
        "fa-television",
        "fa-tencent-weibo",
        "fa-terminal",
        "fa-text-height",
        "fa-text-width",
        "fa-th",
        "fa-th-large",
        "fa-th-list",
        "fa-themeisle",
        "fa-thermometer",
        "fa-thermometer-0",
        "fa-thermometer-1",
        "fa-thermometer-2",
        "fa-thermometer-3",
        "fa-thermometer-4",
        "fa-thermometer-empty",
        "fa-thermometer-full",
        "fa-thermometer-half",
        "fa-thermometer-quarter",
        "fa-thermometer-three-quarters",
        "fa-thumb-tack",
        "fa-thumbs-down",
        "fa-thumbs-o-down",
        "fa-thumbs-o-up",
        "fa-thumbs-up",
        "fa-ticket",
        "fa-times",
        "fa-times-circle",
        "fa-times-circle-o",
        "fa-times-rectangle",
        "fa-times-rectangle-o",
        "fa-tint",
        "fa-toggle-down",
        "fa-toggle-left",
        "fa-toggle-off",
        "fa-toggle-on",
        "fa-toggle-right",
        "fa-toggle-up",
        "fa-trademark",
        "fa-train",
        "fa-transgender",
        "fa-transgender-alt",
        "fa-trash",
        "fa-trash-o",
        "fa-tree",
        "fa-trello",
        "fa-tripadvisor",
        "fa-trophy",
        "fa-truck",
        "fa-try",
        "fa-tty",
        "fa-tumblr",
        "fa-tumblr-square",
        "fa-turkish-lira",
        "fa-tv",
        "fa-twitch",
        "fa-twitter",
        "fa-twitter-square",
        "fa-umbrella",
        "fa-underline",
        "fa-undo",
        "fa-universal-access",
        "fa-university",
        "fa-unlink",
        "fa-unlock",
        "fa-unlock-alt",
        "fa-unsorted",
        "fa-upload",
        "fa-usb",
        "fa-usd",
        "fa-user",
        "fa-user-circle",
        "fa-user-circle-o",
        "fa-user-md",
        "fa-user-o",
        "fa-user-plus",
        "fa-user-secret",
        "fa-user-times",
        "fa-users",
        "fa-vcard",
        "fa-vcard-o",
        "fa-venus",
        "fa-venus-double",
        "fa-venus-mars",
        "fa-viacoin",
        "fa-viadeo",
        "fa-viadeo-square",
        "fa-video-camera",
        "fa-vimeo",
        "fa-vimeo-square",
        "fa-vine",
        "fa-vk",
        "fa-volume-control-phone",
        "fa-volume-down",
        "fa-volume-off",
        "fa-volume-up",
        "fa-warning",
        "fa-wechat",
        "fa-weibo",
        "fa-weixin",
        "fa-whatsapp",
        "fa-wheelchair",
        "fa-wheelchair-alt",
        "fa-wifi",
        "fa-wikipedia-w",
        "fa-window-close",
        "fa-window-close-o",
        "fa-window-maximize",
        "fa-window-minimize",
        "fa-window-restore",
        "fa-windows",
        "fa-won",
        "fa-wordpress",
        "fa-wpbeginner",
        "fa-wpexplorer",
        "fa-wpforms",
        "fa-wrench",
        "fa-xing",
        "fa-xing-square",
        "fa-y-combinator",
        "fa-y-combinator-square",
        "fa-yahoo",
        "fa-yc",
        "fa-yc-square",
        "fa-yelp",
        "fa-yen",
        "fa-yoast",
        "fa-youtube",
        "fa-youtube-play",
        "fa-youtube-square",
    ];
    for (var i = 0; i < res.length; ++i) {
        //Thêm icon vào container
        var element = ` <div class="fa-hover col-md-3 col-sm-4 faIcon ntsIcon"> <a data="fa ${res[i]}"><i class="fa ${res[i]}"></i>&ensp;${res[i]}</a> </div>`;
        $(".box").append(element);
    }
}

/**
 * formaterbtnThaoTacMeNuHT
 * ------------------------
 * Mô tả:
 *   Sinh HTML chuỗi cho các nút Sửa và Xoá trong một cell của bảng.
 *
 * Tham số:
 *   @param {string} ID    — Giá trị ID bản ghi.
 *   @param {string} IDCha — Giá trị ID cha (nếu có).
 *
 * Trả về:
 *   {string} — HTML chứa 2 nút <a> với icon pencil và trash.
 *
 * Ví dụ:
 *   formaterbtnThaoTacMeNuHT("123", "0")
 */
function formaterbtnThaoTacMeNuHT(ID, IDCha) {
    return `<div class="show-or-hide col-md-12" style="padding-right: 0px; padding-left: 0px;"><a class='text-primary btnSuaGrid1 nts-btn-sua' title="Sửa" data='${ID}' data-menucha-id='${IDCha}'><i class='fa fa-pencil'></i></a></b>&ensp;<a class='text-danger btnXoaGrid1 nts-btn-xoa' title="Xoá" data='${ID}'><i class='fa fa-trash'></i></a></div>`;
}

/**
 * loadDataCombos
 * --------------
 * Mô tả:
 *   Tải dữ liệu cho nhiều dropdown (combo) bất đồng bộ, chờ tất cả xong mới tiếp tục.
 *
 * Tham số:
 *   @param {Array<Object>} configs — Mỗi config gồm:
 *     • name      — selector hoặc id của element combo
 *     • ajaxUrl   — URL gọi GET
 *     • columns   — (nếu cần) cấu hình cho NTS.loadDataComboAsync
 *
 * Trả về:
 *   Promise<void> sau khi tất cả request hoàn thành.
 *
 * Ví dụ:
 *   await loadDataCombos([
 *     { name: "#PhongBanID", ajaxUrl: "/api/phongban", columns: 2, indexValue:0, ... },
 *     { name: "#ChucVuID", ajaxUrl: "/api/chucvu", ... },
 *   ]);
 */
async function loadDataCombos(configs) {
    const promises = configs.map((cfg) => NTS.loadDataComboAsync(cfg, "GET"));
    await Promise.all(promises);
}

const commonTableConfig = {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "550px",
    locale: true,
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
    HeaderVertAlign: "center",
};

// Đóng dropdown khi click vào item bất kỳ
document.addEventListener("click", function (e) {
    const isDropdown = e.target.closest(".custom-dropdown-menu");
    const isButton = e.target.closest(".dropdown-toggle-hide-arrow");

    // Nếu không phải dropdown và không phải nút toggle → đóng
    if (!isDropdown && !isButton) {
        document
            .querySelectorAll(".custom-dropdown-menu")
            .forEach((el) => el.remove());
    }

    // Nếu là item bên trong dropdown → xử lý rồi đóng
    if (isDropdown && e.target.closest(".dropdown-item")) {
        const item = e.target.closest(".dropdown-item");
        const id = item.getAttribute("data");

        // Xử lý tương ứng
        // if (item.classList.contains("btnSuaGrid1")) {
        //     console.log("Sửa ID:", id);
        // }

        // ... các logic khác

        // Đóng dropdown
        setTimeout(() => {
            document
                .querySelectorAll(".custom-dropdown-menu")
                .forEach((el) => el.remove());
        }, 0);
    }
});
