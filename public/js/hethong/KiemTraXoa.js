var tempthem = "them";
///////// PHÍM TẮT /////////
var hotKey = 0; // 1 thêm
$(document).on("keydown", function (e) {
    switch (e.keyCode) {
        case 113:
            if (hotKey == 0) $("#btnThemMoi").trigger("click");
            e.preventDefault();
            break;
        case 114:
            if (hotKey == 0) $(".nav-search-input").focus();
            e.preventDefault();
            break;
        case 115:
            if (hotKey == 1) $("#mdThemMoi").modal("hide");
            e.preventDefault();
            break;
        case 120:
            if (hotKey == 1) $("#btnLuuVaDong").trigger("click");
            e.preventDefault();
            break;
    }
});
$(document).on("shown.bs.modal", "#mdThemMoi", function () {
    hotKey = 1;
});
$(document).on("hidden.bs.modal", "#mdThemMoi", function () {
    hotKey = 0;
});
checkDangSD(".checkDangSD", "loai_dia_ban_h_c_s", "TrangThai");
//////////////////////////
$(async function () {
    LoadDataTable();
});

$("#TrangThai").on("change", function () {
    UpdateLabelDangSD(this);
});
$(document).on("click", "#btnThemMoi", function () {
    ThemDuLieu();
});
$(document).on("click", ".btnSuaGrid1", function () {
    $("#KiemTraXoaID").val($(this).attr("data"));
    SuaDuLieu($(this).attr("data"));
});
$(document).on("click", ".btnXoaGrid1", function () {
    $("#KiemTraXoaID").val($(this).attr("data"));
    XoaDuLieu($(this).attr("data"));
});

async function SuaDuLieu(ID) {
    if (!QuyenSua()) {
        return false;
    }
    $("#tieuDeModal").text("Cập nhật thông tin kiểm tra xóa");
    var result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/loaddulieusua",
        { id: ID }
    );
    if (!result.Err) {
        let data = result.Result[0];
        $("#TenModel").value(data.TenModel);
        $("#TenChucNang").value(data.TenChucNang);
        $("#Colums").value(data.Colums);
        $("#ModelKiemTra").value(data.ModelKiemTra);

        $("#mdThemMoi").modal("show");
        tempthem = "sua";
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}

async function ThemDuLieu() {
    resetForm("#mdThemMoi");
    $("#mdThemMoi").modal("show");
    $("#tieuDeModal").text("Thêm mới thông tin kiểm tra xóa");
    $("#KiemTraXoaID").val("");
    tempthem = "them";
}
var fmThaoTac = function (cell) {
    return formaterbtnThaoTac(cell.getData().id);
};
var fmDangSD = function (cell) {
    return formaterDangSD(cell.getValue(), cell.getData().id);
};
var table = new Tabulator("#Grid1", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "550",
    HeaderVertAlign: "center",
    groupBy: "TenChucNang",
    columns: [
        {
            title: '<i class="fa fa-ellipsis-h"></i>',
            headerHozAlign: "center",
            hozAlign: "center",
            formatter: fmThaoTac,
            width: 60,
            headerSort: false,
            frozen: true,
            vertAlign: "middle",
            print: false,
        },
        {
            title: "Tên Model",
            field: "TenModel",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 250,
        },
        {
            title: "Tên cột",
            field: "Colums",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 250,
        },
        {
            title: "Tên chức năng",
            field: "TenChucNang",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            minWidth: 250,
        },

        {
            title: "Tên model kiểm tra",
            field: "ModelKiemTra",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 250,
        },
    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});
table.on("rowDblClick", function (e, row) {
    var rowData = row.getData();
    SuaDuLieu(rowData.id);
});
$(document).on("keyup", "#timKiem", function (e) {
    var data = $(this).val();
    if (e.keyCode == "13") {
        if (data == "" || data == undefined) {
            data = $("#timKiem").value();
        }
        dulieuloc = data;
        table.setFilter([
            [
                {
                    field: "TenModel",
                    type: "like",
                    value: data,
                },
                {
                    field: "Colums",
                    type: "like",
                    value: data,
                },
                {
                    field: "TenChucNang",
                    type: "like",
                    value: data,
                },
            ],
        ]);
    }
});

async function LoadDataTable() {
    table.clearData();
    var result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/getall",
        {}
    );
    if (!result.Err) {
        table.setData(result.result);
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}

$("#btnLuuVaDong").on("click", async function () {
    const validate = new NTSValidate("#mdThemMoi");
    if (!validate.trim().check()) return false;
    const payload = {
        TenModel: $("#TenModel").value(),
        TenChucNang: $("#TenChucNang").value(),
        Colums: $("#Colums").value(),
        ModelKiemTra: $("#ModelKiemTra").value(),
        KiemTraXoaID: $("#KiemTraXoaID").value(),
    };
    var met = "POST";
    if (tempthem == "them") {
        met = "POST";
    } else {
        met = "PUT";
    }
    var result = await NTS.getAjaxAPIAsync(
        met,
        window.location.pathname + "/luuthongtin",
        payload
    );
    if (!result.Err) {
        LoadDataTable();
        NTS.thanhcong(result.Msg);
        $("#mdThemMoi").modal("hide");
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        return false;
    }
});

async function XoaDuLieu(ID) {
    CanhBaoXoa(async () => {
        var result = await NTS.getAjaxAPIAsync(
            "DELETE",
            window.location.pathname + "/xoa",
            { ma: ID }
        );
        if (!result.Err) {
            LoadDataTable();
            NTS.thanhcong(result.Msg);
        } else {
            result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        }
    });
    return false;
}
