$(function () {
    LoadCombo();
    loadData();
    initImageUpload2();
    $("#DangNgayBaoCao").select2({ width: "100%" });
    $("#DangNgayBaoCao").on("change", function () {
        if ($("#DangNgayBaoCao").value() == "1") {
            $("#NgayLapBaoCao1").value(new Date());
            $("#NgayLapBaoCao1").attr("disabled", true);
            $("#NgayLapBaoCao2").attr("disabled", true);
        }
        if ($("#DangNgayBaoCao").value() == "2") {
            $("#NgayLapBaoCao1").attr("disabled", false);
            $("#NgayLapBaoCao2").attr("disabled", true);
        }
        if ($("#DangNgayBaoCao").value() == "3") {
            $("#NgayLapBaoCao1").attr("disabled", true);
            $("#NgayLapBaoCao2").attr("disabled", false);
            $("#NgayLapBaoCao2").value("ngày... tháng...năm...");
        }
        return false;
    });

    var result = NTS.getAjaxAPI(
        "GET",
        window.location.pathname + "/getdata",
        {}
    );

    if (!result.Err) {
        let Data = result.Result[0];
        $("#DonViCapTren").value(Data.DonViCapTren);
        $("#DonViBaoCao").value(Data.DonViBaoCao);
        $("#KyThay").value(Data.KyThay);
        $("#NguoiKT").val(Data.NguoiKT);
        $("#ChucDanhNguoiKT").value(Data.ChucDanhNguoiKT);
        $("#NguoiKy").value(Data.NguoiKy);
        $("#ChucDanhNguoiKy").value(Data.ChucDanhNguoiKy);
        $("#NguoiLap").value(Data.NguoiLap);
        $("#ChucDanhNguoiLap").value(Data.ChucDanhNguoiLap);
        $("#DiaDanh").value(Data.DiaDanh);

        $("#DangNgayBaoCao").value(Data.DangNgayBaoCao);
        $("#NgayLapBaoCao1").value(Data.NgayLapBaoCao1);
        $("#NgayLapBaoCao2").value(Data.NgayLapBaoCao2);
        $("#ChucDanhNguoiKy").value(Data.ChucDanhNguoiKy);

        $("#TinhID").value(Data.DiaBanHCID_Tinh);
        $("#XaID").value(Data.DiaBanHCID_Xa);
        $("#ThonID").value(Data.DiaBanHCID_Thon);
    } else {
        LuuThongTin();
    }
    KiemTraLoaiNgayBC();
    ///////// PHÍM TẮT /////////
    $(document).on("keydown", function (e) {
        switch (e.keyCode) {
            case 120:
                $("#btnLuuVaDong").trigger("click");
                e.preventDefault();
                break;
        }
    });
    $.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
});
});

//load dữ liệu khi người dùng vào trang
function LoadCombo() {
    NTS.loadDataCombo({
        name: "#TinhID",
        type: "POST",
        ajaxUrl: window.Laravel.thietlaphethong.GetDSDiaBanHC_Tinh,
        ajaxParam: {},
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
}
$("#TinhID").on("change", async function () {
    NTS.loadDataCombo({
        name: "#XaID",
        type: "POST",
        ajaxUrl: window.Laravel.thietlaphethong.GetDSDiaBanHC_ByIDCha,
        ajaxParam: { DiaBanHCID_Cha: $('#TinhID').value(), LoaiDiaBan: "02" },
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
});
$("#XaID").on("change", async function () {
    NTS.loadDataCombo({
        name: "#ThonID",
        type: "POST",
        ajaxUrl: window.Laravel.thietlaphethong.GetDSDiaBanHC_ByIDCha,
        ajaxParam: { DiaBanHCID_Cha: $('#XaID').value(), LoaiDiaBan: "03" },
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
});
//lưu dữ liệu
function btnLuuDuLieu() {
    LuuThongTin();
    return false;
}

function btnLuuDuLieuCauHinh() {
    LuuThongTin();
    return false;
}

function LuuThongTin() {
    const payload = {
        DonViCapTren: $("#DonViCapTren").value(),
        DonViBaoCao: $("#DonViBaoCao").value(),
        KyThay: $("#KyThay").value(),
        ChucDanhNguoiKy: $("#ChucDanhNguoiKy").value(),
        NguoiKT: $("#NguoiKT").value(),
        ChucDanhNguoiKT: $("#ChucDanhNguoiKT").value(),
        NguoiLap: $("#NguoiLap").value(),
        ChucDanhNguoiLap: $("#ChucDanhNguoiLap").value(),
        DiaDanh: $("#DiaDanh").value(),
        DangNgayBaoCao: $("#DangNgayBaoCao").value(),
        NgayLapBaoCao1: $("#NgayLapBaoCao1").value(),
        NgayLapBaoCao2: $("#NgayLapBaoCao2").value(),
        TinhID: $("#TinhID").value(),
        XaID: $("#XaID").value(),
        ThonID: $("#ThonID").value(),
        NguoiKy: $("#NguoiKy").value(),
    };
    var result = NTS.getAjaxAPI(
        "POST",
        window.location.pathname + "/luuthongtin",
        payload
    );

    if (!result.Err) {
        NTS.thanhcong(result.Msg);
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        return false;
    }
}

function KiemTraLoaiNgayBC() {
    if ($("#DangNgayBaoCao").value() == "1") {
        $("#NgayLapBaoCao1").attr("disabled", true);
        $("#NgayLapBaoCao2").attr("disabled", true);
    }
    if ($("#DangNgayBaoCao").value() == "2") {
        $("#NgayLapBaoCao1").attr("disabled", false);
        $("#NgayLapBaoCao2").attr("disabled", true);
    }
    if ($("#DangNgayBaoCao").value() == "3") {
        $("#NgayLapBaoCao1").attr("disabled", true);
        $("#NgayLapBaoCao2").attr("disabled", false);
        if ($("#NgayLapBaoCao2").value() != "" && $("#NgayLapBaoCao2").value() != null) {
        } else {
            $("#NgayLapBaoCao2").value("ngày... tháng...năm...");
        }
    }
    return false;
}

$(document).on('submit', '#Tab2_View form', function(e) {
    e.preventDefault();

    const faviconTemp = $("#FaviconUrl").val() || '';
    const logoTemp = $("#LogoUrl").val() || '';
    const anhDaiDienTemp = $("#AnhDaiDienBaiViet").val() || '';
    const bannerNhoTemp = $("#BannerLoginNho").val() || '';
    const bannerMoiTemp = $("#BannerLoginMoi").val() || '';

    const allPaths = [faviconTemp, logoTemp, anhDaiDienTemp, bannerNhoTemp, bannerMoiTemp].join('|');

    const newPaths = moveFileToPermanent(allPaths);

    const [faviconUrl, logoUrl, anhDaiDienUrl, bannerNhoUrl, bannerMoiUrl] = newPaths.split('|');

    clearTempFiles();

    const payload = {
        TieuDeWebsite: $("#TieuDeWebsite").val(), 
        NoiDungChanTrang: $("#NoiDungChanTrang").val(),   
        FaviconUrl: faviconUrl || '',
        LogoUrl: logoUrl || '',
        DiaChi: $("#ThongTinLienHe-DiaChi").val(),
        SoDienThoai: $("#ThongTinLienHe-DienThoai").val(),
        Email: $("#ThongTinLienHe-Email").val(),
        Website: $("#ThongTinLienHe-Website").val(),
        AnhDaiDienBaiViet: anhDaiDienUrl || '',
        BannerLoginNho: bannerNhoUrl || '',
        BannerLoginMoi: bannerMoiUrl || '',

        YeuCauKiemDuyet: $("#ck_YeuCauKiemDuyet").is(":checked"),
        QuanTriNgayTrenWebsite: $("#ck_QuanTriNgayTrenWebsite").is(":checked"),
        KhongHoiKhiRoiTrangChinh: $("#ck_KhongHoiKhiRoiTrangChinh").is(":checked"),
        KhoaKhongChoCopyNoiDung: $("#ck_KhoaKhongChoCopyNoiDung").is(":checked"),
        XacThucTaiKhoan: $("#ck_XacThucTaiKhoan").is(":checked"),
        TamNgungTaiKhoan: $("#ck_TamNgungTaiKhoan").is(":checked"),
        TuDongDuyetBai: $("#ck_TuDongDuyetBai").is(":checked"),
        KiemDuyetBinhLuan: $("#ck_KiemDuyetBinhLuan").is(":checked"),
        CoDinhKhungWebsite: $("#ck_CoDinhKhungWebsite").is(":checked"),
        KhoaBaiDang: $("#ck_KhoaBaiDang").is(":checked"),
        CoNutLenDauTrang: $("#ck_CoNutLenDauTrang").is(":checked"),
        AdminTimeoutValue: $("#AdminTimeout").val()
    };

    var result = NTS.getAjaxAPI(
        "POST",
        window.location.pathname + "/luuthongtinCauHinh",
        payload
    );

    if (!result.Err) {
        NTS.thanhcong(result.Msg);
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        return false;
    }
});

function loadData() {
    var result = NTS.getAjaxAPI(
        "GET",
        window.location.pathname + "/getdataCauHinh",
        {}
    );
    if (!result.err) {
        const data = result.Result[0];

        // Các trường thông tin cơ bản
        $("#TieuDeWebsite").val(data.TieuDeWebsite);
        $("#NoiDungChanTrang").val(data.NoiDungChanTrang);
        $("#ThongTinLienHe-DiaChi").val(data.DiaChi);
        $("#ThongTinLienHe-DienThoai").val(data.SoDienThoai);
        $("#ThongTinLienHe-Email").val(data.Email);
        $("#ThongTinLienHe-Website").val(data.Website);
        $("#ThietLapWebsiteID").val(data.id);

        // Duyệt từng field upload để xử lý tự động
        uploadFields.forEach(field => {
            const url = data[field.inputId];
            const fileName = url?.split('/').pop() || '';

            // Gán URL vào input ẩn
            $('#' + field.inputId).val(url);

            // Gán URL vào ảnh xem trước
            $('#' + field.imgPreviewId).attr('src', url).show();

            // Hiện tên file
            $('#' + field.tenFileId).text(fileName);

            // Hiện khung preview
            $('#' + field.previewDivId).show();
        });
    } else {
        result.canhbao ? NTS.canhbao(result.msg) : NTS.loi(result.msg);
    }
}


function initSingleImageUpload(field) {
    // field: {inputId, fileId, btnChonId, previewDivId, imgPreviewId, btnXemId, btnXoaId, tenFileId, accept}
    $('#' + field.btnChonId).on('click', function() {
        $('#' + field.fileId).click();
    });

    $('#' + field.fileId).on('change', async function() {
        const file = this.files[0];
        if (file) {
            // Validate file type
            const allowedTypes = field.accept || ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
            if (!allowedTypes.includes(file.type) && !(file.type === 'image/x-icon' && field.inputId === 'FaviconUrl')) {
                NTS.canhbao('Vui lòng chọn file ảnh hợp lệ!');
                this.value = '';
                return;
            }
            // Validate file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                NTS.canhbao('Kích thước file không được vượt quá 5MB!');
                this.value = '';
                return;
            }
            NTS.loadding();
            const formData = new FormData();
            formData.append('file', file);
            formData.append('thuMucLuu', 'thietlapwebsite');
            try {
                const res = await $.ajax({
                    url: "/api/dungchung/files/uploadFileTemp",
                    type: "POST",
                    data: formData,
                    contentType: false,
                    processData: false,
                });
                if (res.success && res.url && res.url[0]) {
                    $('#' + field.inputId).val(res.url[0]);
                    $('#' + field.imgPreviewId).attr('src', res.url[0]).show();
                    $('#' + field.tenFileId).text(file.name);
                    $('#' + field.previewDivId).show();
                } else {
                    NTS.loi('Upload ảnh thất bại!');
                }
            } catch (e) {
                NTS.loi('Upload ảnh thất bại!');
            } finally {
                NTS.unloadding();
            }
        }
    });

    $('#' + field.btnXemId).on('click', function() {
        const imgSrc = $('#' + field.imgPreviewId).attr('src');
        if (imgSrc) {
            $('#' + field.imgPreviewId).toggle();
        }
    });

    $('#' + field.btnXoaId).on('click', function() {
        $('#' + field.inputId).val('');
        $('#' + field.fileId).val('');
        $('#' + field.previewDivId).hide();
        $('#' + field.imgPreviewId).attr('src', '').hide();
        $('#' + field.tenFileId).text('');  
    });
}

const uploadFields = [
    {
        inputId: 'FaviconUrl',
        fileId: 'fileFaviconUrl',
        btnChonId: 'btnChonFaviconUrl',
        previewDivId: 'previewFaviconUrl',
        imgPreviewId: 'imgPreviewFaviconUrl',
        btnXemId: 'btnXemFaviconUrl',
        btnXoaId: 'btnXoaFaviconUrl',
        tenFileId: 'tenFileFaviconUrl'
    },
    {
        inputId: 'LogoUrl',
        fileId: 'fileLogoUrl',
        btnChonId: 'btnChonLogoUrl',
        previewDivId: 'previewLogoUrl',
        imgPreviewId: 'imgPreviewLogoUrl',
        btnXemId: 'btnXemLogoUrl',
        btnXoaId: 'btnXoaLogoUrl',
        tenFileId: 'tenFileLogoUrl'
    },
    {
        inputId: 'AnhDaiDienBaiViet',
        fileId: 'fileAnhDaiDienBaiViet',
        btnChonId: 'btnChonAnhDaiDienBaiViet',
        previewDivId: 'previewAnhDaiDienBaiViet',
        imgPreviewId: 'imgPreviewAnhDaiDienBaiViet',
        btnXemId: 'btnXemAnhDaiDienBaiViet',
        btnXoaId: 'btnXoaAnhDaiDienBaiViet',
        tenFileId: 'tenFileAnhDaiDienBaiViet'
    },
    {
        inputId: 'BannerLoginNho',
        fileId: 'fileBannerLoginNho',
        btnChonId: 'btnChonBannerLoginNho',
        previewDivId: 'previewBannerLoginNho',
        imgPreviewId: 'imgPreviewBannerLoginNho',
        btnXemId: 'btnXemBannerLoginNho',
        btnXoaId: 'btnXoaBannerLoginNho',
        tenFileId: 'tenFileBannerLoginNho'
    },
    {
        inputId: 'BannerLoginMoi',
        fileId: 'fileBannerLoginMoi',
        btnChonId: 'btnChonBannerLoginMoi',
        previewDivId: 'previewBannerLoginMoi',
        imgPreviewId: 'imgPreviewBannerLoginMoi',
        btnXemId: 'btnXemBannerLoginMoi',
        btnXoaId: 'btnXoaBannerLoginMoi',
        tenFileId: 'tenFileBannerLoginMoi'
    }
];

function initImageUpload2() {
    initSingleImageUpload({
        inputId: 'AnhDaiDienBaiViet',
        fileId: 'fileAnhDaiDienBaiViet',
        btnChonId: 'btnChonAnhDaiDienBaiViet',
        previewDivId: 'previewAnhDaiDienBaiViet',
        imgPreviewId: 'imgPreviewAnhDaiDienBaiViet',
        btnXemId: 'btnXemAnhDaiDienBaiViet',
        btnXoaId: 'btnXoaAnhDaiDienBaiViet',
        tenFileId: 'tenFileAnhDaiDienBaiViet',
        accept: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
    });
    initSingleImageUpload({
        inputId: 'FaviconUrl',
        fileId: 'fileFaviconUrl',
        btnChonId: 'btnChonFaviconUrl',
        previewDivId: 'previewFaviconUrl',
        imgPreviewId: 'imgPreviewFaviconUrl',
        btnXemId: 'btnXemFaviconUrl',
        btnXoaId: 'btnXoaFaviconUrl',
        tenFileId: 'tenFileFaviconUrl',
        accept: ['image/x-icon', 'image/png', 'image/jpeg', 'image/gif', 'image/webp']
    });
    initSingleImageUpload({
        inputId: 'LogoUrl',
        fileId: 'fileLogoUrl',
        btnChonId: 'btnChonLogoUrl',
        previewDivId: 'previewLogoUrl',
        imgPreviewId: 'imgPreviewLogoUrl',
        btnXemId: 'btnXemLogoUrl',
        btnXoaId: 'btnXoaLogoUrl',
        tenFileId: 'tenFileLogoUrl',
        accept: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
    });
    initSingleImageUpload({
        inputId: 'BannerLoginNho',
        fileId: 'fileBannerLoginNho',
        btnChonId: 'btnChonBannerLoginNho',
        previewDivId: 'previewBannerLoginNho',
        imgPreviewId: 'imgPreviewBannerLoginNho',
        btnXemId: 'btnXemBannerLoginNho',
        btnXoaId: 'btnXoaBannerLoginNho',
        tenFileId: 'tenFileBannerLoginNho',
        accept: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
    });
    initSingleImageUpload({
        inputId: 'BannerLoginMoi',
        fileId: 'fileBannerLoginMoi',
        btnChonId: 'btnChonBannerLoginMoi',
        previewDivId: 'previewBannerLoginMoi',
        imgPreviewId: 'imgPreviewBannerLoginMoi',
        btnXemId: 'btnXemBannerLoginMoi',
        btnXoaId: 'btnXoaBannerLoginMoi',
        tenFileId: 'tenFileBannerLoginMoi',
        accept: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
    });
}
function moveFileToPermanent(paths) {
    if (!paths) return '';
    try {
        var res = NTS.getAjaxAPI(
            "GET",
            window.location.pathname + "/moveFileToPermanent",
            { paths: paths }
        );
        if (typeof res === 'string') {
            return res;
        }
        if (res && res.paths) {
            return res.paths;
        }
        return '';
    } catch (e) {
        return '';
    }
}
async function clearTempFiles() {
    try {
        await $.ajax({
            url: '/api/dungchung/files/clearTemp',
            type: 'POST'
        });
    } catch (e) {}
}

// $("#ck_YeuCauKiemDuyet").on("change", function () {
//     UpdateLabelDangSD3(this);
// });