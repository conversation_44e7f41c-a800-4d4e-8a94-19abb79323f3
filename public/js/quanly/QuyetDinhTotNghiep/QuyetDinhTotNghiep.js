const btnThaoTac = function (cell) {
    return formaterbtnThaoTac(cell.getData().id);
};

const fmTrangThai = function (cell) {
    return formaterTrangThai(cell.getValue(), cell.getData().id);
};

//Flags
var viewGridCard = true;
var thaoTac = "them";
var selectedId;
var selectedHocSinh;
var selectedTruongHoc;

//Grids
var grid1;
var gridSelectDonVi;
var tabGridHocSinh;
var gridTruongHoc;
var gridChonHocSinh;

var modeThemHS = 0;
const enumModeThemHS = Object.freeze({
    Them1: 0,
    ThemNhieu: 1,
});

//Configs

const commonColumnConfig = {
    headerHozAlign: "center",
    HeaderVertAlign: "center",
    vertAlign: "middle",
};
/**
 * Cấu hình chung cho combo box.
 *
 * @function commonComboConfig
 * @param {number} [numCol=2] - Số cột trong combo box. Mặc định là 2.
 * @returns {Object} Cấu hình combo box.
 * @property {number} columns - Số cột trong combo box.
 * @property {number} indexValue - Chỉ số giá trị trong combo box.
 * @property {number} indexText - Chỉ số văn bản trong combo box.
 * @property {number} [indexText1] - Chỉ số văn bản thứ hai (chỉ có khi numCol là 2).
 * @property {string} textShowTatCa - Văn bản hiển thị cho tùy chọn "Tất cả".
 * @property {boolean} showTatCa - Xác định có hiển thị tùy chọn "Tất cả" hay không.
 */
const commonComboConfig = function (numCol = 2) {
    if ((numCol = 2))
        return {
            columns: 2, // indexValue=0, indexText=1
            indexValue: 0,
            indexText: 1,
            indexText1: 2,
            textShowTatCa: "-Chọn-",
            showTatCa: true,
        };
    else {
        return {
            columns: 1, // indexValue=0, indexText=1
            indexValue: 0,
            indexText: 1,
            textShowTatCa: "-Chọn-",
            showTatCa: true,
        };
    }
};
/**
 * Tùy chọn cấu hình cho Tabulator với xử lý phản hồi và lỗi từ AJAX.
 *
 * @typedef {Object} tabuLatorAjaxOptions
 *
 * @property {Function} ajaxResponse - Hàm xử lý phản hồi từ AJAX.
 * Nhận vào các tham số `url`, `params`, và `response`.
 * Kiểm tra lỗi trong phản hồi và hiển thị thông báo lỗi nếu có.
 * Trả về dữ liệu kết quả hoặc mảng rỗng nếu có lỗi.
 *
 * @property {Function} ajaxError - Hàm xử lý lỗi kết nối hoặc lỗi từ server.
 * Nhận vào các tham số `xhr`, `textStatus`, và `errorThrown`.
 * Hiển thị thông báo lỗi kết nối hoặc lỗi server và trả về mảng rỗng.
 */
const tabuLatorAjaxOptions = {
    ajaxResponse: function (url, params, response) {
        if (response.err || response.Err) {
            let msg =
                response.msg ||
                response.Msg ||
                "Có lỗi xảy ra khi tải dữ liệu!";
            if (typeof NTS !== "undefined" && NTS.loi) {
                NTS.loi(msg);
            } else {
                NTS.canhbao(msg);
            }
            return [];
        }
        return response.result || response.Result || response;
    },
    ajaxError: function (xhr, textStatus, errorThrown) {
        let msg =
            "Lỗi kết nối hoặc server: " + (xhr.responseText || textStatus);
        if (typeof NTS !== "undefined" && NTS.loi) {
            NTS.loi(msg);
        } else {
            NTS.canhbao(msg);
        }
        return [];
    },
};

/**
 * Cấu hình chung cho Tabulator.
 *
 * @type {Object}
 * @property {string} layout - Cách bố trí bảng, "fitColumns" để các cột vừa với chiều rộng bảng.
 * @property {boolean} pagination - Bật/tắt phân trang.
 * @property {number} paginationSize - Số lượng hàng hiển thị trên mỗi trang.
 * @property {Array<number|boolean>} paginationSizeSelector - Các tùy chọn số lượng hàng trên mỗi trang, bao gồm các giá trị số và tùy chọn "true" để hiển thị tất cả.
 * @property {boolean} locale - Bật/tắt ngôn ngữ địa phương.
 * @property {Object} langs - Cấu hình ngôn ngữ cho Tabulator, sử dụng đối tượng `TabulatorLangsVi`.
 * @property {string} placeholder - Văn bản hiển thị khi không có dữ liệu, ví dụ: "Không có dữ liệu".
 * @property {string} paginationCounter - Kiểu hiển thị bộ đếm phân trang, "rows" để hiển thị số hàng.
 * @property {string} headerVertAlign - Căn chỉnh dọc tiêu đề, "center" để căn giữa.
 * @property {Object} tabuLatorAjaxOptions - Các tùy chọn bổ sung cho Tabulator liên quan đến AJAX.
 */
const commonTabulatorConfig = {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    locale: true,
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
    paginationCounter: "rows",
    headerVertAlign: "center",
    ...tabuLatorAjaxOptions,
};

gridTruongHoc = new Tabulator("#gridTruongHoc", {
    height: "420px",
    layout: "fitColumns",
    placeholder: "Không có dữ liệu trường học",

    ...tabuLatorAjaxOptions,
    headerVisible: false,
    columns: [
        {
            field: "action",
            width: "30",
            formatter: function () {
                return '<i class="fas fa-hand-point-right text-nts-primary"></i>';
            },
            ...commonColumnConfig,
        },
        {
            title: "Tên trường",
            field: "name", // use 'name' since that's your data key
            ...commonColumnConfig,

            formatter: function (cell) {
                const data = cell.getData();
                const name = data.name || "";
                const count = data.count || 0;

                // Badge style using your --main-color (rgb(247 109 35))
                const badgeStyle = `
                    background-color: rgb(247, 109, 35);
                    color: white;
                    border-radius: 50%;
                    padding: 0.25em 0.6em;
                    font-size: 0.75rem;
                    font-weight: 600;
                    margin-left: 8px;
                    display: inline-block;
                    min-width: 22px;
                    text-align: center;
                    line-height: 1;
                    vertical-align: middle;
                `;

                return `
                    <span>${name}</span>
                    <span style="${badgeStyle}">${count}</span>
                `;
            },
        },
    ],
    rowClick: function (e, row) {
        // Load students for clicked school row
        var donViId = row.getData().id;
        loadStudentsByDonViId(donViId);
    },
});
gridTruongHoc.on("rowClick", async (e, row) => {
    // Load students for clicked school row
    let donViId = row.getData().id;
    selectedTruongHoc = row.getData();

    loadStudentsByDonViId(donViId);
});

// Right side - Selected students grid
function initGridChonHocSinh(mode) {
    return new Tabulator("#gridChonHocSinh", {
        height: "420px",
        layout: "fitColumns",
        placeholder: "Không có học sinh được chọn",
        selectable: mode == enumModeThemHS.ThemNhieu ? true : 1,
        ...tabuLatorAjaxOptions,
        columns: [
            {
                formatter: "rowSelection",
                titleFormatter: "rowSelection",
                hozAlign: "center",
                headerSort: false,
                width: 50,
                selectable: true,
                cellClick: function (e, cell) {
                    cell.getRow().toggleSelect();
                },
                ...commonColumnConfig,
            },
            {
                title: "Mã học sinh",
                field: "MaDoiTuong",
                width: 120,
                ...commonColumnConfig,
            },
            {
                title: "Họ và tên",
                field: "Hovaten",
                width: 150,
                formatter: "textarea",
                ...commonColumnConfig,
            },
            {
                title: "Số CMND/CCCD",
                field: "CCCD",
                width: 150,
                ...commonColumnConfig,
            },
            {
                title: "Ngày sinh",
                field: "Ngaysinh",
                sorter: "date",
                hozAlign: "center",
                width: 120,
                formatter: function (cell) {
                    const val = cell.getValue();
                    if (!val) return "";
                    const date = new Date(val);
                    return date.toLocaleDateString(); // format as needed
                },
                ...commonColumnConfig,
            },
            {
                title: "Giới tính",
                field: "Gioitinh",
                hozAlign: "center",
                width: 100,
                ...commonColumnConfig,
            },
            {
                title: "Dân tộc",
                field: "dan_toc.tenDanToc", // Use nested related field here
                width: 120,
                ...commonColumnConfig,
            },
            {
                title: "Nơi sinh",
                field: "Noisinh",
                width: 150,
                ...commonColumnConfig,
            },
            {
                title: "Lớp học",
                field: "lopHoc", // Note: make sure API returns this field, or remove if not available
                width: 120,
                ...commonColumnConfig,
            },
            {
                title: "Địa chỉ",
                field: "DiaChi",
                width: 200,
                ...commonColumnConfig,
            },
            {
                title: "Đơn vị học",
                field: "don_vi_hoc.TenDonVi", // nested related field
                width: 200,
                ...commonColumnConfig,
                formatter: function (cell) {
                    return cell.getValue() || "";
                },
            },
            {
                title: "Địa bàn tỉnh",
                field: "dia_ban_tinh.TenDiaBan", // nested related field
                width: 200,
                ...commonColumnConfig,
                formatter: function (cell) {
                    return cell.getValue() || "";
                },
            },
        ],
    });
}

// Example function to load students for selected school
/**
 * Tải danh sách học sinh theo ID đơn vị.
 *
 * Hàm này gửi yêu cầu AJAX để lấy danh sách học sinh từ server dựa trên ID đơn vị được cung cấp.
 * Nếu dữ liệu được tải thành công, nó sẽ được hiển thị trên `gridChonHocSinh`.
 * Nếu xảy ra lỗi, một thông báo lỗi sẽ được hiển thị và dữ liệu trong `gridChonHocSinh` sẽ bị xóa.
 *
 * @param {number|string} donViId - ID của đơn vị để tải danh sách học sinh.
 * @returns {void}
 */
function loadStudentsByDonViId(donViId) {
    const url = Laravel.getListHS(donViId);

    NTS.getAjaxAPIAsync("GET", url, {})
        .then((res) => {
            //Xử lý kết quả trả về
            if (!res.Err) {
                gridChonHocSinh.setData(res.Result);
            } else {
                NTS.loi("Lỗi khi tải danh sách học sinh");
                gridChonHocSinh.clearData();
            }
        })
        .catch(() => {
            NTS.loi("Lỗi khi tải danh sách học sinh");
            gridChonHocSinh.clearData();
        });
}
//#region Render card
function handleFileLinks(event) {
    event.preventDefault();

    const link = event.currentTarget;
    const filesString = link.getAttribute("data-files") || "";

    // Split file string by pipe '|'
    const filesArray = filesString.split("|").filter((f) => f.trim() !== "");

    // Populate modal with these files
    populateMdXemDinhKem(filesArray);

    // Show modal
    hienMdXemDinhKem();
}

function buildTrangThaiBadge(mauNen, label) {
    return `
    <span class="badge position-absolute bottom-0 end-0 m-3 py-1 px-3" 
        style="font-weight: 600; font-size: 0.85rem; cursor: default; user-select: none;
        background-color: ${mauNen ?? "#44badc"};">
        ${label ?? "Chưa ban hành"}
    </span>`;
}
/**
 * Hàm `htmlDuLieu` tạo HTML hiển thị thông tin quyết định tốt nghiệp dựa trên dữ liệu đầu vào.
 *
 * @param {Object} cell - Đối tượng cell từ Tabulator, chứa thông tin của một hàng dữ liệu.
 * @param {Object} formatterParams - Các tham số định dạng (không sử dụng trong hàm này).
 * @param {Function} onRendered - Hàm callback được gọi sau khi nội dung được render (không sử dụng trong hàm này).
 *
 * @returns {string} - Chuỗi HTML hiển thị thông tin quyết định tốt nghiệp, bao gồm các thao tác như xem chi tiết, chỉnh sửa, ban hành, thu hồi, xóa, và các thông tin liên quan.
 *
 * @description
 * - Hàm kiểm tra trạng thái ban hành của quyết định (`tinhTrangBanHanh`) để hiển thị các thao tác phù hợp.
 * - Hiển thị thông tin như số quyết định, ngày ký, người ký, chức vụ, cơ quan ban hành, kỳ thi, cấp học, số học sinh công nhận, file đính kèm, ghi chú, ngày ban hành, người ban hành, chức vụ ban hành, và nội dung ban hành.
 * - Định dạng ngày tháng theo kiểu `dd/mm/yyyy`.
 * - Nếu có file đính kèm, hiển thị liên kết để xem file; nếu không, hiển thị thông báo "Không có file đính kèm".
 * - Hiển thị trạng thái quyết định (Đã ban hành hoặc Chưa ban hành) dưới dạng badge.
 */
function htmlDuLieu(cell, formatterParams, onRendered) {
    const data = cell.getData();

    // Determine issued state
    const daBanHanh = data.tinhTrangBanHanh;

    // Action strings
    let chuoiThaoTacBanHanh = "";
    let chuoiThaoTacXoa = "";
    let chuoiThaoTacThuHoi = "";
    let chuoiThaoTacSua = "";
    let dataTrangThai = data.trang_thai_label;
    let badgeTrangThai = buildTrangThaiBadge(
        (mauNen = dataTrangThai?.MauSac),
        (label = dataTrangThai?.TenTrangThai)
    );
    // If NOT issued, show Ban hành & Xóa
    if (!daBanHanh) {
        chuoiThaoTacBanHanh = `
            <a class="dropdown-item textsize-item btnBanHanhQD" href="javascript:void(0);" data-id="${data.id}" id="btnBanHanhQD">
                <i class="text-success fa fa-check-square-o iconsize-item"></i>&ensp; Ban hành quyết định
            </a>`;
        chuoiThaoTacXoa = `
            <a class="dropdown-item textsize-item btnXoaQD" href="javascript:void(0);" data-id="${data.id}" id="btnXoaQD">
                <i class="text-danger fa fa-trash-o iconsize-item"></i>&ensp; Xóa quyết định
            </a>`;
        chuoiThaoTacSua = `
         <a class="dropdown-item textsize-item btnChinhSuaQD" href="javascript:void(0);" data-id="${data.id}" id="btnChinhSuaQD">
            <i class="text-warning fa fa-pencil-square-o iconsize-item"></i>&ensp; Chỉnh sửa quyết định
        </a>
        `;
    } else {
        // If issued, show Thu hồi ban hành, hide Xóa & Ban hành
        chuoiThaoTacThuHoi = `
            <a class="dropdown-item textsize-item btnThuHoiBanHanhQD" href="javascript:void(0);" data-id="${data.id}" id="btnThuHoiBanHanhQD">
                <i class="text-warning fa fa-undo iconsize-item"></i>&ensp; Thu hồi ban hành
            </a>`;
    }

    const chuoiThaoTac = `
        <a class="dropdown-item textsize-item btnXemChiTietQD" href="javascript:void(0);" data-id="${data.id}" id="">
            <i class="text-primary fa fa-eye iconsize-item"></i>&ensp; Xem chi tiết quyết định
        </a>
        ${chuoiThaoTacSua}
        ${chuoiThaoTacBanHanh}
        ${chuoiThaoTacThuHoi}
        ${chuoiThaoTacXoa}
    `;

    // Format NgayKy and NgayBanHanh to dd/mm/yyyy
    function formatDate(dateStr) {
        return dateStr;
    }
    const fileLinks =
        Array.isArray(data.FileDinhKem) && data.FileDinhKem.length > 0
            ? data.FileDinhKem.join("|") // join files with a separator like pipe |
            : "";

    let html = `
    <div class="list-item col-md-12">
        <div class="card card-luoi shadow-sm">
            <div class="card-body profile-user-box mb-2" style="padding-bottom:0px !important; padding-top:6px;">
                <div class="row">
                    <div class="col-12 col-xs-6 col-md-2 text-center">
                        <div class="row mt-2">
                            <img 
                            class="d-block mx-auto w-75" 
                            src="${Laravel.linkAnhHoSo}" 
                            alt="icon quyết định"
                            >
                        </div>
                    </div>
                    <div class="col-md-10">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <p class="fs-big mb-0">Số quyết định: <b>${
                                    data.SoQuyetDinh || ""
                                }</b></p>
                            </div>
                            <div class="col-md-3">
                            <p class="fs-big mb-0">Ngày ký: <b>${formatDate(
                                data.NgayKy
                            )}</b></p>
                            </div>
                            <div class="col-md-3">
                            <p class="fs-big mb-0">Người ký: <b>${
                                data.NguoiKy || ""
                            }</b></p>
                            </div>
                            <div class="col-md-3 d-flex justify-content-between align-items-center">
                            <p class="fs-big mb-0">Chức vụ: <b>${
                                data.chuc_vu?.tenChucVu || ""
                            }</b></p>
                            <div class="dropdown d-inline text-end">
                                <button class="btn btn-sm btn-white dropdown-toggle-hide-arrow" type="button" style="font-size:18px; height:33.4px;" id="dropdownMenuQD" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i style="color:#07a607" class="blue fa fa-ellipsis-h"></i>
                                </button>
                                <div class="dropdown-menu dropdown-menu-lg-end w-auto dropdown-thao-tac" style="margin-left:-175px; inset:0 auto auto auto; position:fixed !important;">
                                ${chuoiThaoTac}
                                </div>
                            </div>
                            </div>
                        </div>
                        <hr style="border-top: 2px solid #f76707; margin: 0 0 0.5rem 0;" />
                        <div class="row align-items-center">
                        <div class="row">
                            <div class="col-12">
                            <p class="fs-big mb-0">Trích yếu/nội dung: <strong>${
                                data.TrichYeu || ""
                            }</strong></p>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                            <p class="fs-big mb-0">Cơ quan ban hành: <b>${
                                data.CoQuanBanHanh || ""
                            }</b></p>
                            </div>
                            <div class="col-md-6">
                            <p class="fs-big mb-0">Số học sinh công nhận: <b>${
                                Array.isArray(data.HocSinhTN)
                                    ? data.HocSinhTN?.length
                                    : 0
                            }</b></p>
                           
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <p class="fs-big mb-0">Kỳ thi: <b>${
                                    data.ky_thi?.TenKyThi || ""
                                }</b></p>
                            </div>
                             <div class="col-3">
                                <p class="fs-big mb-0">Cấp học: <b>${
                                    data.cap_hoc?.tenCapHoc || ""
                                }</b></p>
                            </div>
                             <div class="col-3">
                                <p class="fs-big mb-0">Đính kèm:  ${
                                    fileLinks
                                        ? `<a href="#" class="file-links" data-files="${fileLinks}" onclick="handleFileLinks(event)">📎 Xem đính kèm</a>`
                                        : "Không có file đính kèm"
                                }</p>
                            </div>
                        </div>
                       
                        <div class="row">
                            <div class="col-12">
                            <p class="fs-big mb-0">Ghi chú: ${
                                data.GhiChu || ""
                            }</p>
                            </div>
                        </div>
                        </div>
                        
                        <hr style="border-top: 2px solid #f76707; margin: 0 0 0.5rem 0; margin-top:0.5rem;" />
                        <div class="row">
                            <div class="col-md-3">
                                <p class="fs-big mb-0">Ngày ban hành: <b>${formatDate(
                                    data.NgayBanHanh || ""
                                )}</b></p>
                            </div>
                            <div class="col-md-3">
                                <p class="fs-big mb-0">Người ban hành: <b>${
                                    data.TenNguoiBH || ""
                                }</b></p>
                            </div>
                            <div class="col-md-3">
                                <p class="fs-big mb-0">Chức vụ ban hành: <b>${
                                    data.ChucVuNguoiBH || ""
                                }</b></p>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <p class="fs-big mb-0">Nội dung ban hành: <b>${
                                    data.NoiDungBH ?? ""
                                }</b></p>
                            </div>
                        </div>
                        ${badgeTrangThai}
                    </div>
                </div>
            </div>
        </div>
    </div>
    `;

    return html;
}
//#endregion

/**
 * Dùng để chuyển từ view lưới sang view card
 *
 * Hàm này kiểm tra xem cột được chỉ định có phải là cột duy nhất đang hiển thị hay không.
 * Nếu đúng, nó sẽ hiển thị tất cả các cột. Nếu không, nó sẽ ẩn tất cả các cột ngoại trừ cột được chỉ định.
 *
 * @param {Object} table - Đối tượng bảng (table) cần thao tác.
 * @param {string} fieldName - Tên trường của cột cần hiển thị.
 */
function showCard(tableId, cardField, show) {
    const table = Tabulator.findTable(`#${tableId}`)[0];
    if (!table) {
        console.error(`Tabulator table with id '${tableId}' not found`);
        return;
    }

    const columns = table.getColumns();

    if (show) {
        // Hide all except the cardField column
        columns.forEach((col) => {
            if (col.getField() === cardField) {
                col.show();
            } else {
                col.hide();
            }
        });
    } else {
        // Show all columns again
        columns.forEach((col) => col.show());
    }

    table.redraw(true);
}
function resetMdThemMoi() {
    showStep(1);
    resetFileInput();
}
function prepareThemMoiData() {
    return Promise.all([gridSelectDonVi.setData(), LoadComboMdThemMoi()]);
}
document.addEventListener("DOMContentLoaded", function () {
    //#region Sự kiện
    document
        .getElementById("btnThemMoi")
        .addEventListener("click", function () {
            if (!QuyenThem()) return;
            //Mở modal thêm mới
            selectedId = null;
            resetMdThemMoi();
            thaoTac = "them";

            $("#lblTieuDeMultiStep").text(
                "THÊM MỚI QUYẾT ĐỊNH CÔNG NHẬN TỐT NGHIỆP"
            );
            prepareThemMoiData()
                .then(() => {
                    //Chờ load xong  grid và combo mới hiển thị
                    document.getElementById("mdThemMoi").style.display =
                        "block";
                })
                .catch((err) => {
                    console.error("Error preparing data:", err);
                });
        });

    document
        .querySelector("#mdThemMoi .btn-close")
        .addEventListener("click", function () {
            document.getElementById("mdThemMoi").style.display = "none";
        });
    document
        .querySelector("#mdThemMoi #btnKetThuc")
        .addEventListener("click", function () {
            document.getElementById("mdThemMoi").style.display = "none";
        });

    $(document).on("click", ".btnChinhSuaQD", async function () {
        if (!QuyenSua()) {
            return;
        }
        let id = $(this).data("id"); //Mở modal chỉnh sửa
        resetMdThemMoi();
        selectedId = id;

        prepareThemMoiData()
            .then(() => {
                //Chờ load xong grid và combo mới hiển thị

                SuaDuLieu(id);
                document.getElementById("mdThemMoi").style.display = "block";
            })
            .catch((err) => {
                console.error("Error preparing data:", err);
            });

        thaoTac = "sua";
    });

    $(document).on("click", ".btnXemChiTietQD", async function () {
        document.getElementById("mdThemMoi").style.display = "none";
    });

    $(document).on("click", ".btnXoaQD", async function () {
        if (!QuyenXoa()) return;
        const id = $(this).data("id"); // gets data-id attribute properly
        XoaDuLieu(id);
    });
    //#region Xoá dữ liệu
    async function XoaDuLieu(ID) {
        var result_ktxoa = await NTS.getAjaxAPIAsync(
            "GET",
            window.Laravel.layouts.KiemTraXoa,
            {
                ma: ID,
                model: "QuanLy\\QuyetDinh",
            }
        );

        if (!result_ktxoa.Err) {
            CanhBaoXoa(async () => {
                var result = await NTS.getAjaxAPIAsync(
                    "DELETE",
                    window.location.pathname + "/xoa",
                    { ma: ID }
                );
                if (!result.Err) {
                    // grid1.setData();
                    SetMainGridData();
                    NTS.thanhcong(result.Msg);
                } else {
                    result.canhbao
                        ? NTS.canhbao(result.Msg)
                        : NTS.loi(result.Msg);
                }
            });
            return false;
        } else {
            NTS.canhbao(result_ktxoa.Msg);
            return false;
        }
    }

    $(document).on("click", "#btnTiepTuc", async function () {
        const soQuyetDinh = $("#SoQuyetDinh").val().trim();
        if (!soQuyetDinh) {
            NTS.canhbao("Vui lòng nhập Số quyết định.");
            $("#SoQuyetDinh").focus();
            return false;
        }

        // Collect basic input/select/textarea fields inside the fieldset
        const dinhKemQDValue = $("#dinhKemQD").val(); // get the string value
        const filesArray = dinhKemQDValue ? dinhKemQDValue.split("|") : [];
        const dataThongTinChung = {
            SoQuyetDinh: $("#SoQuyetDinh").val(),
            NguoiKy: $("#NguoiKy").val(),
            ChucVu: $("#ChucVu").val(), // select
            NgayKy: $("#NgayKy").val(),
            CoQuanBanHanh: $("#CoQuanBanHanh").val(),
            KyThi: $("#KyThi").val(),
            HinhThucDaoTao: $("#HinhThucDaoTao").val(),
            HoiDong: $("#HoiDong").val(),
            TrichYeu: $("#TrichYeu").val(),
            DinhKemQD: filesArray, // hidden input
        };

        // Get selected row from Tabulator gridDonVi
        const selectedRows = gridSelectDonVi.getSelectedData();

        // Assuming only 1 selection allowed
        dataThongTinChung.DonVi = selectedRows[0];
        let method = "POST";

        if (typeof selectedId !== "undefined" && selectedId) {
            method = "PUT"; // or "PATCH"
            dataThongTinChung.QuyetDinhID = selectedId; // match your Laravel controller expectation
        }

        var result = await NTS.getAjaxAPIAsync(
            method,
            window.Laravel.luuThongTin,
            dataThongTinChung
        );
        if (!result.Err) {
            NTS.thanhcong(result.Msg);

            if (method === "POST") {
                selectedId = result.Data.id;
            }
        } else {
            result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
            return false;
        }

        NTS.getAjaxAPIAsync("GET", Laravel.loadDuLieuSua, {
            id: selectedId,
        }).then((result) => {
            if (result.Err) {
                NTS.loi("Lỗi khi tải dữ liệu: " + (result.Msg || ""));
                return;
            }

            const data = result.Result;

            // Format NgayBanHanh to dd/mm/yyyy if needed
            let ngayBanHanh = data.NgayBanHanh;
            if (ngayBanHanh) {
                const dt = new Date(ngayBanHanh);
                ngayBanHanh = dt.toLocaleDateString("vi-VN");
            }

            const soQuyetDinh = data.SoQuyetDinh || "";
            const coQuanBanHanh = data.CoQuanBanHanh || "";
            const donVi = data.don_vi?.TenDonVi || "NTSOFT"; // fallback to NTSOFT

            const message = `Bạn đang thực hiện cập nhật danh sách học sinh/sinh viên được công nhận tốt nghiệp tại quyết định số: <strong>${soQuyetDinh}</strong> được ký ngày <strong>${ngayBanHanh}</strong> của <strong>${coQuanBanHanh}</strong> về việc công nhận học sinh sinh viên tốt nghiệp THPT năm học 2024`;

            // Now you can use `message` where needed, e.g.
            $("#msgCapNhatDSHS").html(message);
        });
        showStep(2);
        tabGridHocSinh.setData(Laravel.getListHSByQD(result.Data.id));
        setTimeout(() => {
            tabGridHocSinh.redraw(true);
        }, "300");
        // grid1.setData();
        SetMainGridData();
    });

    //#region Chọn học sinh
    $(document).on("click", "#btnChonVaDong", async function () {
        // Get selected rows data
        let selectedRows = gridChonHocSinh.getSelectedData();

        if (selectedRows.length === 0) {
            NTS.canhbao("Vui lòng chọn ít nhất một học sinh.");
            return;
        }
        const selectedOptionElement = document.getElementById("selectedOption");
        const student = selectedRows[0];
        const ma = student.MaDoiTuong || "";
        const ten = student.Hovaten || "";
        const cccd = student.CCCD || "N/A";
        const ngaySinhRaw = student.Ngaysinh || "";
        const ngaySinh = ngaySinhRaw
            ? new Date(ngaySinhRaw).toLocaleDateString("vi-VN")
            : "";

        const displayStr = `Họ và tên: <strong>${ma} - ${ten}</strong>; CMND/CCCD: <strong>${cccd}</strong>; Ngày sinh: <strong>${ngaySinh}</strong>`;

        // Save to your variable (or do whatever you want with selected data)
        selectedHocSinh = selectedRows;
        selectedOptionElement.innerHTML = `${displayStr}`;
        $("#tenTruong").val(selectedTruongHoc.name || "");
        $("#mdChonHocSinh").modal("hide");
    });
    //#endregion
    //#endregion

    //#region Main grid QĐ
    grid1 = new Tabulator("#Grid1", {
        ajaxURL: Laravel.getListQD, // Replace with your actual data URL
        ...commonTabulatorConfig,
        height: "80vh",
        headerVisible: false,
        columns: [
            {
                title: "Thông tin",
                field: "QuyetDinhID",
                formatter: htmlDuLieu,
                visible: true,
            },
            {
                title: "Số Quyết Định",
                field: "SoQuyetDinh",
                ...commonColumnConfig,
            },
            { title: "Người Ký", field: "NguoiKy", ...commonColumnConfig },
            {
                title: "Cấp Học",
                field: "CapHocID",
                hozAlign: "center",
                ...commonColumnConfig,
            },
            {
                title: "Ngày Ký",
                field: "NgayKy",
                hozAlign: "center",
                sorter: "date",
                ...commonColumnConfig,
            },

            {
                title: "Học Sinh TN Count",
                field: "HocSinhTN",
                hozAlign: "center",
                formatter: function (cell) {
                    let val = cell.getValue();
                    return Array.isArray(val) ? val.length : 0;
                },
                ...commonColumnConfig,
            },
            { title: "Ghi Chú", field: "GhiChu", ...commonColumnConfig },
            {
                title: "Trạng Thái",
                field: "TrangThai",
                hozAlign: "center",
                formatter: fmTrangThai,
                ...commonColumnConfig,
            },
        ],
    });
    grid1.on("dataLoaded", function (data) {
        showCard("Grid1", "QuyetDinhID", viewGridCard);
    });

    //#endregion

    //#region Grid sel ĐV
    gridSelectDonVi = new Tabulator("#gridDonVi", {
        layout: "fitColumns",
        ...commonTabulatorConfig,

        maxHeight: "200px",
        minHeight: "150px",
        ajaxURL: Laravel.getListDonVi,
        ajaxParams: {},
        headerVisible: false, //hide column headers

        // -------- GROUP MODE -----------
        groupBy: "tenDonVi_cha",
        groupStartOpen: true, // optional: open groups by default
        groupHeader: function (value, count, data) {
            return `<span class="text-bold" style="color:#1f4e79;">${
                value || "Không có phụ thuộc"
            } (${count} dòng)</span>`;
        },

        selectable: 1,
        selectRowOnClick: false,

        columns: [
            {
                title: "",
                field: "select",
                formatter: "rowSelection",
                titleFormatter: "rowSelection",
                hozAlign: "center",
                headerHozAlign: "center",
                headerSort: false,
                minWidth: 40,
                vertAlign: "middle",
                maxWidth: 100,
                frozen: true,
            },
            {
                title: "Tên đơn vị",
                field: "name",
                headerSort: false,
                vertAlign: "middle",
            },
        ],
    });

    gridSelectDonVi.on("cellClick", (e, cell) => {
        // Only toggle for the "name" column
        if (cell.getColumn().getField() === "select") {
            cell.getRow().toggleSelect();
            return;
        }

        const data = cell.getData();
        const children = data.donvi_tructhuoc;
        if (
            children &&
            children.length > 0 &&
            cell.getColumn().getField() === "name"
        ) {
            cell.getRow().treeToggle(); // or toggleTree() depending on Tabulator version
        }
    });
    //#endregion

    //#region Grid HS
    tabGridHocSinh = new Tabulator("#tabGridHocSinh", {
        ajaxParams: {},

        ...tabuLatorAjaxOptions,

        layout: "fitColumns",
        height: "70vh",
        maxHeight: "80vh",
        placeholder: "Không có dữ liệu",
        columns: [
            {
                title: "<i class='fa fa-ellipsis-h'></i>",
                formatter: btnThaoTac,
                hozAlign: "center",
                headerSort: false,
                width: 40,
                ...commonColumnConfig,
            },
            {
                title: "Mã học sinh",
                field: "DoiTuong.MaDoiTuong",
                headerSort: false,
                ...commonColumnConfig,
            },
            {
                title: "Họ và tên",
                field: "DoiTuong.Hovaten",
                headerSort: false,
                ...commonColumnConfig,
                formatter: "textarea",
            },
            {
                title: "Số CMND/CCCD",
                field: "DoiTuong.CCCD",
                headerSort: false,
                ...commonColumnConfig,
            },
            {
                title: "Ngày sinh",
                field: "DoiTuong.Ngaysinh",
                headerSort: false,
                ...commonColumnConfig,
                formatter: function (cell) {
                    let val = cell.getValue();
                    if (!val) return "";
                    let d = new Date(val);
                    return d.toLocaleDateString("vi-VN");
                },
            },
            {
                title: "Giới tính",
                field: "DoiTuong.Gioitinh",
                headerSort: false,
                ...commonColumnConfig,
            },
            {
                title: "Nơi sinh",
                field: "DoiTuong.Noisinh",
                headerSort: false,
                ...commonColumnConfig,
            },
            {
                title: "Kết quả đánh giá",
                columns: [
                    {
                        title: "Rèn luyện",
                        field: "xep_loai_ren_luyen.tenXepLoai",
                        headerSort: false,
                        ...commonColumnConfig,
                    },
                    {
                        title: "Học tập",
                        field: "xep_loai_hoc_tap.tenXepLoai",
                        headerSort: false,
                        ...commonColumnConfig,
                    },
                ],
                ...commonColumnConfig,
            },
            {
                title: "Ưu tiên",
                field: "dien_uu_tien.TenDienUuTien",
                headerSort: false,
                ...commonColumnConfig,
            },
            {
                title: "Kết quả tốt nghiệp",
                field: "xep_loai_tot_nghiep.tenXepLoai",
                headerSort: false,
                ...commonColumnConfig,
            },
            {
                title: "Ghi chú",
                field: "GhiChu",
                headerSort: false,
                ...commonColumnConfig,
                formatter: "textarea",
            },
        ],
    });

    $(document).on("click", "#tabGridHocSinh  .btnSuaGrid1", async function () {
        await LoadComboMdThemMoiHocSinh();
        SuaDuLieu_HS($(this).attr("data"));
    });

    $(document).on("click", "#tabGridHocSinh .btnXoaGrid1", function () {
        if (!QuyenXoa()) return;

        XoaDuLieu_HS($(this).attr("data"));
    });

    //#region Thêm mới học sinh
    //1 học sinh
    $("#btnThemMoiHS").on("click", function () {
        LoadComboMdThemMoiHocSinh();
        $("#mdThemMoiHocSinh").modal("show");
        thaotacHocSinhTN = "them";
        modeThemHS = enumModeThemHS.Them1;

        $("#mdThemMoiHocSinh h5.modal-title").text(
            "Thêm mới học sinh/sinh viên"
        );
        $("#selectedOption").html("Chọn học sinh");
    });
    //Nhiều học sinh
    $("#btnNhapNhieuHS").on("click", function () {
        LoadComboMdThemMoiHocSinh();
        $("#mdThemMoiHocSinh").modal("show");
        thaotacHocSinhTN = "them";
        modeThemHS = enumModeThemHS.ThemNhieu;

        $("#mdThemMoiHocSinh h5.modal-title").text(
            "Thêm mới học sinh/sinh viên"
        );
        $("#selectedOption").html("Chọn học sinh");
    });
    //#endregion
    //#endregion
});
var thaotacHocSinhTN;

function SuaDuLieu_HS(ID) {
    thaotacHocSinhTN = "sua";
    $("#mdThemMoiHocSinh h5.modal-title").text("Sửa học sinh/sinh viên");

    // Construct URL by replacing placeholder or just append ID
    const url = Laravel.getHSByID.replace("ID", ID);
    NTS.getAjaxAPIAsync("GET", url, {})
        .then((response) => {
            if (response.Err) {
                NTS.loi(
                    "Lỗi khi tải dữ liệu học sinh: " + (response.Msg || "")
                );
                return;
            }
            debugger;
            const hsData = response.Result;

            // Save selected HocSinhTN data for later use if needed
            selectedHocSinh = [hsData.hoc_sinh] || [];

            // Set input values properly using jQuery val()
            $("#hocSinhID").val(hsData.id || ""); // HocSinhTN document id
            $("#ketQuaRenLuyen").value(hsData.KetQua_RL);
            $("#ketQuaHocTap").value(hsData.KetQua_HT);
            $("#chkUuTien").value(hsData.ThuocDienUuTien);
            $("#ketQuaTotNghiep").value(hsData.KetQuaTN);
            $("#ghiChu").val(hsData.GhiChu || "");

            // Optionally, fill some hoc_sinh info if needed:
            // e.g. $("#hoVaTen").val(hsData.hoc_sinh?.Hovaten || "");
            const selectedOptionElement =
                document.getElementById("selectedOption");

            const student = hsData.hoc_sinh;
            const ma = student.MaDoiTuong || "";
            const ten = student.Hovaten || "";
            const cccd = student.CCCD || "N/A";
            const ngaySinhRaw = student.Ngaysinh || "";
            const ngaySinh = ngaySinhRaw
                ? new Date(ngaySinhRaw).toLocaleDateString("vi-VN")
                : "";

            const displayStr = `Họ và tên: <strong>${ma} - ${ten}</strong>; CMND/CCCD: <strong>${cccd}</strong>; Ngày sinh: <strong>${ngaySinh}</strong>`;

            selectedOptionElement.innerHTML = `${displayStr}`;

            // Open modal
            $("#mdThemMoiHocSinh").modal("show");
        })
        .catch((err) => {
            console.error("Lỗi khi gọi API getHocSinhTNById:", err);
            NTS.loi("Lỗi hệ thống khi tải dữ liệu học sinh.");
        });
}

async function XoaDuLieu_HS(ID) {
    var result_ktxoa = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.layouts.KiemTraXoa,
        {
            ma: ID,
            model: "QuanLy\\HocSinhTN",
        }
    );

    if (!result_ktxoa.Err) {
        CanhBaoXoa(async () => {
            var result = await NTS.getAjaxAPIAsync("DELETE", Laravel.xoaHSTN, {
                quyetDinhId: selectedId,
                ma: ID,
            });
            if (!result.Err) {
                NTS.thanhcong(result.Msg);
                tabGridHocSinh.setData(Laravel.getListHSByQD(selectedId));
            } else {
                result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
            }
        });
        return false;
    } else {
        NTS.canhbao(result_ktxoa.Msg);
        return false;
    }
}
async function LoadComboMdThemMoi() {
    loadDataCombos([
        // 1. Chức vụ
        {
            name: "#ChucVu",
            ajaxUrl: Laravel.getListChucVu,
            ...commonComboConfig(),
        },
        // 2. Kỳ thi
        {
            name: "#KyThi",
            ajaxUrl: Laravel.getListKyThi,
            ...commonComboConfig(),
        },
        // 3. Hình thức đào tạo
        {
            name: "#HinhThucDaoTao",
            ajaxUrl: Laravel.getListHTDT,
            ...commonComboConfig(),
        },
        // 4. Hội đồng
        {
            name: "#HoiDong",
            ajaxUrl: Laravel.getListHoiDong,
            ...commonComboConfig(),
        },
    ]);
}

async function LoadComboMdThemMoiHocSinh() {
    return loadDataCombos([
        // 1. Chức vụ
        {
            name: "#ketQuaRenLuyen",
            ajaxUrl: Laravel.getListXepLoai,
            ...commonComboConfig(),
        },
        // 2. Kỳ thi
        {
            name: "#ketQuaHocTap",
            ajaxUrl: Laravel.getListXepLoai,
            ...commonComboConfig(),
        },
        // 3. Hình thức đào tạo
        {
            name: "#ketQuaTotNghiep",
            ajaxUrl: Laravel.getListXepLoai,
            ...commonComboConfig(),
        },
        {
            name: "#chkUuTien",
            ajaxUrl: Laravel.getListDienUuTien,
            ...commonComboConfig(),
        },
    ]);
}

$(document).on("click", "#btnLuuVaDong", function () {
    switch (modeThemHS) {
        case enumModeThemHS.Them1: // Thêm 1 (single save)
            {
                let data = {
                    quyetDinhId: selectedId,
                    hocSinhID: selectedHocSinh[0].id,
                    ketQuaRenLuyen: $("#ketQuaRenLuyen").val(),
                    ketQuaHocTap: $("#ketQuaHocTap").val(),
                    uuTien: $("#chkUuTien").val(),
                    ketQuaTotNghiep: $("#ketQuaTotNghiep").val(),
                    ghiChu: $("#ghiChu").val(),
                };
                let method = thaotacHocSinhTN === "sua" ? "PUT" : "POST";

                NTS.getAjaxAPIAsync(
                    method,
                    Laravel.luuThongTinHocSinh,
                    data
                ).then(() => {
                    tabGridHocSinh.setData(Laravel.getListHSByQD(selectedId));
                    setTimeout(() => {
                        tabGridHocSinh.redraw(true);
                    }, 300);

                    $("#mdThemMoiHocSinh").modal("hide");
                });
            }
            break;

        case enumModeThemHS.ThemNhieu: // Thêm nhiều (batch save)
            {
                let savePromises = selectedHocSinh.map((hs) => {
                    let data = {
                        quyetDinhId: selectedId,
                        hocSinhID: hs.id,
                        ketQuaRenLuyen: $("#ketQuaRenLuyen").val(),
                        ketQuaHocTap: $("#ketQuaHocTap").val(),
                        uuTien: $("#chkUuTien").val(),
                        ketQuaTotNghiep: $("#ketQuaTotNghiep").val(),
                        ghiChu: $("#ghiChu").val(),
                    };
                    let method = thaotacHocSinhTN === "sua" ? "PUT" : "POST";

                    return NTS.getAjaxAPIAsync(
                        method,
                        Laravel.luuThongTinHocSinh,
                        data
                    )
                        .then((response) => {
                            // Check API returned Err or not
                            if (response.Err) {
                                return Promise.reject({
                                    message: response.Msg || "Unknown error",
                                    response,
                                });
                            }
                            return { success: true, hocSinh: hs };
                        })
                        .catch((error) => ({
                            success: false,
                            hocSinh: hs,
                            error,
                        }));
                });

                Promise.all(savePromises).then((results) => {
                    const successList = results
                        .filter((r) => r.success)
                        .map((r) => r.hocSinh);
                    const failList = results.filter((r) => !r.success);

                    if (successList.length > 0) {
                        tabGridHocSinh.setData(
                            Laravel.getListHSByQD(selectedId)
                        );
                        setTimeout(() => {
                            tabGridHocSinh.redraw(true);
                        }, 300);
                    }

                    $("#mdThemMoiHocSinh").modal("hide");

                    if (failList.length === 0) {
                        // All succeeded
                        NTS.thanhcong(
                            `Đã lưu thành công ${successList.length} học sinh.`
                        );
                    } else if (successList.length === 0) {
                        // All failed
                        NTS.loi(
                            `Lưu thất bại toàn bộ ${failList.length} học sinh: ` +
                                failList
                                    .map((f) => f.hocSinh.ten || f.hocSinh.id)
                                    .join(", ")
                        );
                    } else {
                        // Partial success
                        NTS.canhbao(
                            `Đã lưu thành công ${successList.length} học sinh.\n` +
                                `Có ${failList.length} học sinh lưu thất bại: ` +
                                failList
                                    .map((f) => f.hocSinh.ten || f.hocSinh.id)
                                    .join(", ")
                        );
                    }
                });
            }
            break;
    }
});

//#endregion

//#region Chuyển step modal thêm mới
// Simple step switch logic
function showStep(step) {
    document.getElementById("step-1-content").style.display =
        step === 1 ? "" : "none";
    document.getElementById("step-2-content").style.display =
        step === 2 ? "" : "none";

    // Update step sidebar highlight
    document
        .getElementById("sidebar-step-1")
        .classList.toggle("active", step === 1);
    document
        .getElementById("sidebar-step-2")
        .classList.toggle("active", step === 2);
}

// Attach next/prev button events (call showStep with 1 or 2)
document.addEventListener("DOMContentLoaded", function () {
    // "Tiếp tục" on Step 1

    // "Quay lại" on Step 2
    document.getElementById("btnQuayLaiBuoc1").onclick = function (e) {
        e.preventDefault();
        showStep(1);
    };
    // Initialize step 1
    showStep(1);
});

//#endregion Chuyển step

//#region Sửa dữ liệu
async function SuaDuLieu(id) {
    let quyetDinhInfo = await NTS.getAjaxAPIAsync(
        "GET",
        Laravel.loadDuLieuSua,
        { id: id }
    );

    if (quyetDinhInfo.Err) {
        NTS.loi(quyetDinhInfo.Msg || "Lỗi tải dữ liệu");
        return;
    }

    const data = quyetDinhInfo.Result;
    // Populate inputs
    $("#SoQuyetDinh").val(data.SoQuyetDinh || "");
    $("#NguoiKy").val(data.NguoiKy || "");
    $("#ChucVu").value(data.ChucVuID_NK || "");
    $("#NgayKy").value(data.NgayKy || "");
    $("#CoQuanBanHanh").val(data.CoQuanBanHanh || "");
    $("#KyThi").value(data.KyThiID || "");
    $("#HinhThucDaoTao").value(data.HinhThucID || "");
    $("#HoiDong").value(data.HoiDongID || "");

    if (data.DonViID) {
        const row = gridSelectDonVi.getRow(data.DonViID);
        if (row) {
            gridSelectDonVi.selectRow(data.DonViID);
        }
    } else {
        console.warn("DonViID is empty or undefined.");
    }
    $("#TrichYeu").val(data.TrichYeu || "");

    // Populate attached files if needed
    if (data.FileDinhKem) {
        let dinhKemArr = data.FileDinhKem;

        dinhKemArr?.forEach((url) => {
            renderAttachment(url);
        });
        $("#dinhKemQD").val(dinhKemArr?.join("|")).trigger("change");
    }
}

//#region Chọn học sinh
const customSelect = document.getElementById("customSelect");
const dropdown = document.getElementById("customDropdown");
const selectedOption = document.getElementById("selectedOption");
const hiddenSelect = document.getElementById("hocSinhID");
const btnDropdownHS = document.getElementById("btnDropdownHS");

btnDropdownHS.addEventListener("click", function () {
    $("#mdChonHocSinh").modal("show");

    gridTruongHoc.setData(Laravel.getListTruongHoc);
    if (gridChonHocSinh && typeof gridChonHocSinh.destroy === "function") {
        gridChonHocSinh.destroy();
    }

    gridChonHocSinh = initGridChonHocSinh(modeThemHS);
});

//#endregion

//#region Shortcut
$(document).on("keydown", function (e) {
    const modalSelectors = [
        "#mdThemMoi", // full screen panel (display block)
        "#mdThemMoiHocSinh", // Bootstrap modal
        "#mdChonHocSinh", // Bootstrap modal
        "#mdQuyetDinhTotNghiep", // Bootstrap modal
    ];

    // Filter visible modals/panels
    const visibleModals = modalSelectors.filter((sel) => {
        const el = document.querySelector(sel);
        if (!el) return false;

        if (sel === "#mdThemMoi") {
            // Use computed style to check visibility
            const style = window.getComputedStyle(el);
            return (
                style.display !== "none" &&
                style.visibility !== "hidden" &&
                style.opacity !== "0"
            );
        } else {
            return el.classList.contains("show");
        }
    });

    if (visibleModals.length === 0) return; // no open modal, ignore

    // Topmost modal is last in visibleModals
    const topModalSelector = visibleModals[visibleModals.length - 1];
    const topModal = document.querySelector(topModalSelector);

    // Helper: close the top modal
    function closeTopModal() {
        if (topModalSelector === "#mdThemMoi") {
            topModal.style.display = "none";
        } else {
            // Bootstrap modal hide
            const bsModal = bootstrap.Modal.getInstance(topModal);
            if (bsModal) bsModal.hide();
        }
    }

    // Helper: trigger submit on top modal's submit button if exists
    function submitTopModal() {
        let btn = null;
        debugger;
        switch (topModalSelector) {
            case "#mdThemMoi":
                // Determine current step: step 1 or step 2
                const step1Displayed =
                    document.getElementById("step-1-content").style.display !==
                    "none";
                const step2Displayed =
                    document.getElementById("step-2-content").style.display !==
                    "none";

                // Check which step is visible
                if (step1Displayed) {
                    // Step 1 active — trigger "Tiếp tục" button
                    btn = $("#btnTiepTuc");
                } else if (step2Displayed) {
                    // Step 2 active — trigger "Lưu và đóng" button
                    btn = $("#btnKetThuc");
                }
                break;
            case "#mdThemMoiHocSinh":
                btn = topModal.querySelector("#btnLuuVaDong");
                break;
            case "#mdChonHocSinh":
                btn = topModal.querySelector("#btnChonVaDong");
                break;
            case "#mdQuyetDinhTotNghiep":
                btn = topModal.querySelector("#btnBanHanh");
                break;
        }
        if (btn) btn.click();
    }

    // Only react if focus is inside top modal
    // if (!topModal.contains(document.activeElement)) return;

    if (e.key === "Escape" || e.key === "F4") {
        e.preventDefault();
        closeTopModal();
    } else if (e.key === "F9") {
        e.preventDefault();
        submitTopModal();
    }
});
//#endregion

//#region TÌM KIẾM NÂNG CAO

$("#TimKiemNangCao").on("click", function () {
    $("#TrangThai_Loc").select2({
        data: [
            { id: "all", text: "-Tất cả-" },
            { id: "20", text: "Đã ban hành" },
            { id: "21", text: "Chưa ban hành" },
        ],
        placeholder: "-Tất cả-", // placeholder text

        allowClear: false,
        width: "100%", // force full width
    });
    loadDataCombos([
        // 1. Cap hoc
        {
            name: "#CapHoc_Loc",
            ajaxUrl: Laravel.getListToChuc,
            columns: 2, // indexValue=0, indexText=1
            indexValue: 0,
            indexText: 1,
            indexText1: 2,
            textShowTatCa: "-Chọn-",
            showTatCa: true,
        },
    ]);
});
$("#TimKiem").on("click", function () {
    SetMainGridData();
    if ($("#KhungTimKiem").css("display") == "block") {
        $("#KhungTimKiem").slideUp(200);
    } else {
        $("#KhungTimKiem").slideDown(200);
    }
});

function SetMainGridData() {
    const trangThaiLoc = ["20", "21"].includes($("#TrangThai_Loc").val())
        ? $("#TrangThai_Loc").val()
        : null;
    const data = {
        tuNgay: $("#TuNgay_Loc").val() || null,
        denNgay: $("#DenNgay_Loc").val() || null,
        capHocID: $("#CapHoc_Loc").val() || null,
        trangThai: trangThaiLoc,
    };

    // Optional: clean empty strings to null
    for (const key in data) {
        if (data[key] === "" || data[key] === undefined) {
            data[key] = null;
        }
    }
    grid1.setData(Laravel.getListQD, data);
}
//#endregion

//#region Tìm kiếm chữ
$(document).on("keyup", "#timKiem", function (e) {
    var searchValue = $(this).val().trim().toLowerCase();

    if (!searchValue) {
        grid1.clearFilter();
        return;
    }

    grid1.setFilter(function (data) {
        // Defensive checks and lowercase conversion
        const soQuyetDinh = data.SoQuyetDinh
            ? data.SoQuyetDinh.toString().toLowerCase()
            : "";
        const nguoiKy = data.NguoiKy
            ? data.NguoiKy.toString().toLowerCase()
            : "";
        const capHocID =
            data.CapHocID !== null && data.CapHocID !== undefined
                ? data.CapHocID.toString().toLowerCase()
                : "";
        const ghiChu = data.GhiChu ? data.GhiChu.toString().toLowerCase() : "";

        return (
            soQuyetDinh.includes(searchValue) ||
            nguoiKy.includes(searchValue) ||
            capHocID.includes(searchValue) ||
            ghiChu.includes(searchValue)
        );
    });
});

$(document).on("keyup", "#searchContent", function (e) {
    const searchValue = $(this).val().trim().toLowerCase();

    if (!searchValue) {
        tabGridHocSinh.clearFilter();
        return;
    }

    tabGridHocSinh.setFilter(function (data) {
        // Defensive checks and lowercase conversion for nested fields
        const maDoiTuong =
            data.DoiTuong && data.DoiTuong.MaDoiTuong
                ? data.DoiTuong.MaDoiTuong.toString().toLowerCase()
                : "";
        const hoVaTen =
            data.DoiTuong && data.DoiTuong.Hovaten
                ? data.DoiTuong.Hovaten.toString().toLowerCase()
                : "";
        const cccd =
            data.DoiTuong && data.DoiTuong.CCCD
                ? data.DoiTuong.CCCD.toString().toLowerCase()
                : "";
        const gioiTinh =
            data.DoiTuong && data.DoiTuong.Gioitinh
                ? data.DoiTuong.Gioitinh.toString().toLowerCase()
                : "";
        const ngaySinh =
            data.DoiTuong && data.DoiTuong.NgaySinh
                ? data.DoiTuong.NgaySinh.toString().toLowerCase()
                : "";
        const noiSinh =
            data.DoiTuong && data.DoiTuong.Noisinh
                ? data.DoiTuong.Noisinh.toString().toLowerCase()
                : "";
        const ghiChu = data.GhiChu ? data.GhiChu.toString().toLowerCase() : "";
        const ketQuaRenLuyen =
            data.xep_loai_ren_luyen && data.xep_loai_ren_luyen.tenXepLoai
                ? data.xep_loai_ren_luyen.tenXepLoai.toString().toLowerCase()
                : "";
        const ketQuaHocTap =
            data.xep_loai_hoc_tap && data.xep_loai_hoc_tap.tenXepLoai
                ? data.xep_loai_hoc_tap.tenXepLoai.toString().toLowerCase()
                : "";
        const ketQuaTotNghiep =
            data.xep_loai_tot_nghiep && data.xep_loai_tot_nghiep.tenXepLoai
                ? data.xep_loai_tot_nghiep.tenXepLoai.toString().toLowerCase()
                : "";
        const uuTien = data.ThuocDienUuTien ? "x" : "";

        return (
            maDoiTuong.includes(searchValue) ||
            hoVaTen.includes(searchValue) ||
            cccd.includes(searchValue) ||
            gioiTinh.includes(searchValue) ||
            noiSinh.includes(searchValue) ||
            ghiChu.includes(searchValue) ||
            ketQuaRenLuyen.includes(searchValue) ||
            ketQuaHocTap.includes(searchValue) ||
            ketQuaTotNghiep.includes(searchValue) ||
            uuTien.includes(searchValue) ||
            ngaySinh.includes(searchValue)
        );
    });
});

// Search for gridChonHocSinh triggered by #timKiemTruongHoc input
$(document).on("keyup", "#timKiemTruongHoc", function () {
    const searchValue = $(this).val().trim().toLowerCase();

    if (!searchValue) {
        gridTruongHoc.clearFilter();
        return;
    }

    gridTruongHoc.setFilter(function (data) {
        // Loop through all fields in the data row
        for (const key in data) {
            if (!Object.prototype.hasOwnProperty.call(data, key)) continue;

            let value = data[key];
            if (value === null || value === undefined) continue;

            // If the value is an object (like nested data), convert to JSON string
            if (typeof value === "object") {
                value = JSON.stringify(value);
            } else {
                value = value.toString();
            }

            if (value.toLowerCase().includes(searchValue)) {
                return true;
            }
        }
        return false;
    });
});

// Search for tabGridHocSinh triggered by #timKiemHocSinh input
$(document).on("keyup", "#timKiemHocSinh", function () {
    const searchValue = $(this).val().trim().toLowerCase();

    if (!searchValue) {
        gridChonHocSinh.clearFilter();
        return;
    }

    gridChonHocSinh.setFilter(function (data) {
        // Prepare all searchable fields with safe null checks and lowercasing
        const fieldsToSearch = [
            data.MaDoiTuong || "",
            data.Hovaten || "",
            data.CCCD || "",
            data.Gioitinh || "",
            data.Noisinh || "",
            data.lopHoc || "",
            data.DiaChi || "",
            // Nested fields (check existence)
            (data.dan_toc && data.dan_toc.tenDanToc) || "",
            (data.don_vi_hoc && data.don_vi_hoc.TenDonVi) || "",
            (data.dia_ban_tinh && data.dia_ban_tinh.TenDiaBan) || "",
        ].map((field) => field.toString().toLowerCase());

        // Return true if any field contains the search string
        return fieldsToSearch.some((field) => field.includes(searchValue));
    });
});
//#endregion

//#region Xem chi tiết

//#region Danh sách HS
var mdChiTiet_gridHS = new Tabulator("#gridHocSinh_ct", {
    ajaxParams: {},
    ...commonTabulatorConfig,
    layout: "fitData",
    height: "350px",
    maxHeight: "50vh",

    columns: [
        {
            title: "Mã học sinh",
            field: "DoiTuong.MaDoiTuong",
            headerSort: false,

            ...commonColumnConfig,
        },
        {
            title: "Họ và tên",
            field: "DoiTuong.Hovaten",
            width: 250,
            headerSort: false,
            ...commonColumnConfig,
            formatter: "textarea",
        },
        {
            title: "Số CMND/CCCD",
            field: "DoiTuong.CCCD",
            headerSort: false,
            ...commonColumnConfig,
        },
        {
            title: "Ngày sinh",
            field: "DoiTuong.Ngaysinh",
            hozAlign: "center",
            headerSort: false,
            ...commonColumnConfig,
            formatter: function (cell) {
                let val = cell.getValue();
                if (!val) return "";
                let d = new Date(val);
                return d.toLocaleDateString("vi-VN");
            },
        },
        {
            title: "Giới tính",
            field: "DoiTuong.Gioitinh",
            headerSort: false,
            ...commonColumnConfig,
        },
        {
            title: "Nơi sinh",
            field: "DoiTuong.Noisinh",
            headerSort: false,
            ...commonColumnConfig,
        },
        {
            title: "Kết quả đánh giá",
            columns: [
                {
                    title: "Rèn luyện",
                    field: "xep_loai_ren_luyen.tenXepLoai",
                    headerSort: false,
                    hozAlign: "center",
                    ...commonColumnConfig,
                },
                {
                    title: "Học tập",
                    field: "xep_loai_hoc_tap.tenXepLoai",
                    headerSort: false,
                    hozAlign: "center",

                    ...commonColumnConfig,
                },
            ],
            ...commonColumnConfig,
        },
        {
            title: "Ưu tiên",
            field: "ThuocDienUuTien",
            headerSort: false,
            ...commonColumnConfig,
            formatter: function (cell) {
                return cell.getValue() ? "x" : "";
            },
        },
        {
            title: "Kết quả tốt nghiệp",
            field: "xep_loai_tot_nghiep.tenXepLoai",
            headerSort: false,
            hozAlign: "center",

            ...commonColumnConfig,
        },
        {
            title: "Ghi chú",
            field: "GhiChu",
            minWidth: 350,
            headerSort: false,
            ...commonColumnConfig,
            formatter: "textarea",
        },
    ],
});
//#endregion

//#region Xử lý sự kiện xem chi tiết quyết định
$(document).on("click", ".btnXemChiTietQD", function () {
    let id = $(this).data("id");
    $("#mdChiTietQD").modal("show");

    NTS.getAjaxAPIAsync("GET", Laravel.loadDuLieuSua, { id: id })
        .then((quyetDinhInfo) => {
            if (quyetDinhInfo.Err) {
                NTS.loi(quyetDinhInfo.Msg || "Lỗi tải dữ liệu");
                return Promise.reject("Error loading data");
            }

            const data = quyetDinhInfo.Result;

            $("#soQuyetDinh_ct").html(
                "Số quyết định: <strong>" +
                    (data.SoQuyetDinh || "") +
                    "</strong>"
            );

            $("#nguoiKy_ct").html(
                "Người ký: <strong>" + (data.NguoiKy || "") + "</strong>"
            );

            $("#chucVu_ct").html(
                "Chức vụ: <strong>" +
                    (data.chuc_vu?.tenChucVu || "") +
                    "</strong>"
            );

            $("#ngayKy_ct").html(
                "Ngày ký: <strong>" + (data.NgayKy || "") + "</strong>"
            );

            $("#coQuanBanHanh").html(
                "Cơ quan ban hành: <strong>" +
                    (data.CoQuanBanHanh || "") +
                    "</strong>"
            );

            $("#kythi_ct").html(
                `Kỳ thi: <strong>${data.ky_thi?.TenKyThi || ""}</strong>`
            );
            $("#htdt_ct").html(
                `Hình thức đào tạo: <strong>${
                    data.hinh_thuc_dao_tao?.tenHinhThucDaoTao || ""
                }<strong>`
            );

            $("#hoidong_Ct").html(
                "Hội đồng: <strong>" +
                    (data.hoi_dong?.TenHoiDong || "") +
                    "</strong>"
            );

            if (data.don_vi) {
                let donViText = `${data.don_vi.MaDonVi || ""} - ${
                    data.don_vi.TenDonVi || ""
                }`;
                $("#donvi_ct").html(`Đơn vị:<strong> ${donViText}</strong>`);
            } else {
                $("#donvi_ct").html("");
            }

            $("#trichYeuContent").html(
                data.TrichYeu ? `<strong>${data.TrichYeu}</strong>` : ""
            );

            // Handle đính kèm link

            if (
                Array.isArray(data.FileDinhKem) &&
                data.FileDinhKem.length > 0
            ) {
                const fileLinks = data.FileDinhKem.join("|");

                $("#dinhKemLink_ct")
                    .html(
                        `
                       <p class="fs-big mb-0">Đính kèm:  ${
                           fileLinks
                               ? `<a href="#" class="file-links" data-files="${fileLinks}" onclick="handleFileLinks(event)">📎 Xem đính kèm</a>`
                               : "Không có file đính kèm"
                       }
                       </p>
                        `
                    )
                    .show();
            } else {
                $("#dinhKemLink_ct").html("<span>Không có đính kèm</span>");
            }

            if (data.DonViID) {
                // const row = gridSelectDonVi.getRow(data.DonViID);
                // if (row) {
                //     gridSelectDonVi.selectRow(data.DonViID);
                // }
            } else {
                NTS.canhbao("Không có đơn vị.");
            }
        })
        .catch((err) => {
            NTS.loi("Error loading Quyết Định data:", err);
        });

    mdChiTiet_gridHS.setData(Laravel.getListHSByQD(id)).catch((err) => {
        NTS.loi("Không thể tải danh sách học sinh");
    });

    setTimeout(() => {
        mdChiTiet_gridHS.redraw(true);
    }, 300);
});
//#endregion
