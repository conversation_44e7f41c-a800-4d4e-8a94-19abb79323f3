var tempthem = "them";
var ChuaCoThongTin = "";
const namHienTai = new Date().getFullYear();
//#region logic thao tác click
$(function () {
    $(document).on("keydown", function (e) {
        switch (e.keyCode) {
            case 113: //f2
                if (!$("#mdThemMoi").hasClass("show")) {
                    ThemMoi();
                }
                break;
            case 115: //f4
                if ($("#mdThemMoi").hasClass("show")) {
                    $("#mdThemMoi").modal("hide");
                }
                break;
            case 120: //f9
                if ($("#mdThemMoi").hasClass("show")) {
                    $("#btnLuuVaDong").trigger("click");
                }
                break;
        }
    });
    LoadDataComBo();
    LoadDataTable();
});
$(document).on("keyup", "#SearchKey", async function (e) {
    if (e.keyCode == "13") {
        await LoadDataTable();
        $("#KhungTimKiem").slideUp(200);
        return false;
    }
});
$(document).on("click", "#TimKiemNangCao", function () {
    if ($("#KhungTimKiem").css("display") == "block") {
        $("#KhungTimKiem").slideUp(200);
    } else {
        $("#KhungTimKiem").slideDown(200);
    }
    return false;
});
$(document).on("click", "#DongTimKiem", function () {
    $("#KhungTimKiem").slideUp(200);
    return false;
});
$(document).on("click", "#TimKiem", async function () {
    $("#KhungTimKiem").slideUp(200);
    await LoadDataTable();
    return false;
});
$(document).on("click", "#btn-layout-1", async function () {
    $("#grid-layout").fadeIn(200);
    $("#list-layout").hide();
    $("#list-layout").removeClass("show");
    $("#grid-layout").addClass("show");
    $("#DivDanhSach").show();
    $("#DivLuoi").hide();
    table.redraw(true);
    GridMainLuoi.redraw(true);
});
$(document).on("click", "#btn-layout-2", async function () {
    $("#grid-layout").hide();
    $("#list-layout").fadeIn(200);
    $("#list-layout").addClass("show");
    $("#grid-layout").removeClass("show");
    $("#DivDanhSach").hide();
    $("#DivLuoi").show();
    table.redraw(true);
    GridMainLuoi.redraw(true);
});
$(document).on("change", "#CbSapXep", function () {
    LoadDataTable();
});
function LoadDataComBo() {
    NTS.loadDataComboAsync({
        name: "#QuyetDinhID",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListQuyetDinh,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#QuyetDinhID_Loc",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListQuyetDinh,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText: 2,
        textShowTatCa: "(Tất cả)",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#KyThiID",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListKyThi,
        ajaxParam: {},
        columns: 2,
        indexValue: 0,
        indexText: 1,
        indexText1: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#KyThiID_Loc",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListKyThi,
        ajaxParam: {},
        columns: 2,
        indexValue: 0,
        indexText: 1,
        indexText1: 2,
        textShowTatCa: "(Tất cả)",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#KhoaThiID",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListKhoaThi,
        ajaxParam: { KyThiID: $("#KyThiID").value() },
        columns: 2,
        indexValue: 0,
        indexText: 1,
        indexText1: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#KhoaThiID_Loc",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListKhoaThi,
        ajaxParam: { KyThiID: $("#KyThiID_Loc").value() },
        columns: 2,
        indexValue: 0,
        indexText: 1,
        indexText1: 2,
        textShowTatCa: "(Tất cả)",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#NamTotNghiep",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListNam,
        columns: 1,
        indexValue: 0,
        indexText: 0,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#NamTotNghiep_Loc",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListNam,
        columns: 1,
        indexValue: 0,
        indexText: 0,
        textShowTatCa: "(Tất cả)",
        showTatCa: !0,
    });
}
// $(document).on("change", "#QuyetDinhID", function () {
//     NTS.loadDataComboAsync({
//         name: "#KyThi",
//         type: "GET",
//         ajaxUrl: window.Laravel.local.getListKyThi,
//         ajaxParam: { QuyetDinhID: $("#QuyetDinhID").value() },
//         columns: 2,
//         indexValue: 0,
//         indexText: 1,
//         indexText1: 2,
//         textShowTatCa: "-Chọn-",
//         showTatCa: !0,
//     });
// });

$(document).on("change", "#KyThiID", function () {
    NTS.loadDataComboAsync({
        name: "#KhoaThiID",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListKhoaThi,
        ajaxParam: { KyThiID: $("#KyThiID").value() },
        columns: 2,
        indexValue: 0,
        indexText: 1,
        indexText1: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
});

$(document).on("change", "#KyThiID_Loc", function () {
    NTS.loadDataComboAsync({
        name: "#KhoaThiID_Loc",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListKhoaThi,
        ajaxParam: { KyThiID: $("#KyThiID_Loc").value() },
        columns: 2,
        indexValue: 0,
        indexText: 1,
        indexText1: 2,
        textShowTatCa: "(Tất cả)",
        showTatCa: !0,
    });
});
//#region Thêm xóa sửa
$(document).on("click", "#btnThemMoi", function () {
    document.getElementById("mdThemMoi").style.display = "block";
    $("#NamTotNghiep").value(namHienTai);
    $("#QuyetDinhID").prop("disabled", false);
    $("#KyThiID").prop("disabled", false);
    $("#NamTotNghiep").prop("disabled", false);
    $("#TenTruongHoc").prop("disabled", false);
    $("#DonViID_TruongHoc").prop("disabled", false);
    $("#KhoaThiID").prop("disabled", false);
    tempthem = "them";
    showStep(1);
});
$(document).on("click", ".modal-close", function () {
    document.getElementById("mdThemMoi").style.display = "none";
    tempthem = "them";
});
$(document).on("click", "#btnKetThuc", function () {
    document.getElementById("mdThemMoi").style.display = "none";
    tempthem = "them";
    LoadDataTable();
});
async function XemThongTin(id) {
    var result1 = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/loaddulieusua",
        {
            id: id,
        }
    );
    if (!result1.Err) {
        let data = result1.Result;
        $("#lblQuyetDinh_Xem").text(data.TenQuyetDinh);
        $("#lblNamTotNghiep").text(data.NamTotNghiep);
        $("#lblTenDonVi_Xem").text(data.TenDonVi);
        $("#lblTenHinhThuc_Xem").text(data.TenHinhThuc);
        $("#lblTenKyThi_Xem").text(data.TenKyThi);
        $("#lblTenKhoaThi_Xem").text(data.TenKhoaThi);
    }

    GridXemCT.clearData();
    let result2 = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.local.getallct,
        {
            SoGocID: id,
        }
    );
    if (!result2.Err) {
        GridXemCT.setData(result2.result);
    } else {
        GridXemCT.setData(null);
    }
    $("#mdXemThongTin").modal("show");
}
async function SuaThongTin(id) {
    if (!QuyenSua()) {
        return;
    }
    resetForm("#mdThemMoi");
    $("#lblTieuDeMultiStep").text("Cập nhật thông tin sổ gốc");
    var result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/loaddulieusua",
        {
            id: id,
        }
    );
    if (!result.Err) {
        let data = result.Result;
        $("#SoGocID").value(data.id);
        $("#QuyetDinhID").value(data.QuyetDinhID);
        $("#KyThiID").value(data.KyThiID);
        $("#NamTotNghiep").value(data.NamTotNghiep);
        $("#TenTruongHoc").value(data.TenDonVi);
        $("#DonViID_TruongHoc").value(data.DonViID_TruongHoc);
        $("#GhiChu").value(data.GhiChu);

        $("#QuyetDinhID").prop("disabled", true);
        $("#KyThiID").prop("disabled", true);
        $("#NamTotNghiep").prop("disabled", true);
        $("#TenTruongHoc").prop("disabled", true);
        $("#DonViID_TruongHoc").prop("disabled", true);
        $("#KhoaThiID").prop("disabled", true);
        setTimeout(() => {
            $("#KhoaThiID").value(data.KhoaThiID);
        }, 500);

        uploadedFileUrls = data.DinhKem
            ? data.DinhKem.split("|").filter((u) => u)
            : [];

        $("#SoGoc_txtDuongDanFileVB").value(data.DinhKem);
        $("#SoGoc_list-file").empty();

        uploadedFileUrls.forEach((url) => {
            renderAttachment(url);
        });
        tempthem = "sua";
        document.getElementById("mdThemMoi").style.display = "block";
        showStep(1);
    }
}
$(document).on("click", "#btnTiepTuc", async function () {
    const validate = new NTSValidate("#mdThemMoi");
    if (!validate.trim().check()) return false;
    // if ($("#DonViID_TruongHoc").value() == "") {
    //     NTS.canhbao("Trường học không được để trống!");
    //     return false;
    // }
    const payload = {
        QuyetDinhID: $("#QuyetDinhID").value(),
        KyThiID: $("#KyThiID").value(),
        KhoaThiID: $("#KhoaThiID").value(),
        NamTotNghiep: $("#NamTotNghiep").value(),
        DonViID_TruongHoc: $("#DonViID_TruongHoc").value(),
        GhiChu: $("#GhiChu").value(),
        DuongDanFileVB: $("#SoGoc_txtDuongDanFileVB").value(),

        SoGocID: $("#SoGocID").value(),
    };

    var met = "POST";
    if (tempthem == "them") {
        met = "POST";
    } else {
        met = "PUT";
    }

    var result = await NTS.getAjaxAPIAsync(
        met,
        window.location.pathname + "/luuthongtin",
        payload
    );
    if (!result.Err) {
        $("#lblQuyetDinh").text($("#select2-QuyetDinhID-container").text());
        debugger;
        $("#SoGocID").value(result.id);
        LoadDataTableCT();
        NTS.thanhcong(result.Msg);
        showStep(2);
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        return false;
    }
});
async function XoaThongTin(id) {
    if (!QuyenXoa()) {
        return;
    }
    var result_ktxoa = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.layouts.KiemTraXoa,
        {
            ma: id,
            model: "QuanLy\\SoGoc",
        }
    );

    if (!result_ktxoa.Err) {
        CanhBaoXoa(async () => {
            var result = await NTS.getAjaxAPIAsync(
                "DELETE",
                window.location.pathname + "/xoa",
                { ma: id }
            );
            if (!result.Err) {
                LoadDataTable();
                NTS.thanhcong(result.Msg);
            } else {
                result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
            }
        });
        return false;
    } else {
        NTS.canhbao(result_ktxoa.Msg);
        return false;
    }
}
//#region Lưới 1
function htmlDuLieu(cell, formatterParams, onRendered) {
    let anhDaiDien = "";
    anhDaiDien =
        `<img src="${window.Laravel.local.imgLuoi}" alt="` +
        cell.getData().SoPhieu +
        `" class="img-thumbnail rounded lazy">`;
    return `<div class="list-item col-md-12" style="padding: 0px;">
                        <div class="card card-luoi shadow-sm mb-2 ">
                            <div id="card_${
                                cell.getData().id
                            }" class="card-body profile-user-box">
                                <div class="row">
                                    <div class="col-12 col-xs-6 col-sm-2 text-center" style="margin: auto;">
                                        <div class="profile-picture">
                                            ${anhDaiDien}
                                        </div>
                                    </div>
                                    <div class="col-sm-8">
                                        <div class="row">
                                            <div class="col-9">
                                                <p class="my-1"><a style="font-size:14px" class="cursor-pointer" onclick="XemThongTin('${
                                                    cell.getData().id
                                                }'); return false;">${
        cell.getData().TenQuyetDinh || ChuaCoThongTin
    }</a></p>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p class="fs-big my-1">Hình thức học: <b>${
                                                    cell.getData()
                                                        .TenHinhThuc ||
                                                    ChuaCoThongTin
                                                }</b></p>
                                            </div>
                                            <div class="col-md-6">
                                                <p class="fs-big my-1">Năm tốt nghiệp: <b>${
                                                    cell.getData()
                                                        .NamTotNghiep ||
                                                    ChuaCoThongTin
                                                }</b></p>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p class="fs-big my-1">Kỳ thi: <b>${
                                                    cell.getData().TenKyThi ||
                                                    ChuaCoThongTin
                                                }</b></p>
                                            </div>
                                            <div class="col-md-6">
                                                <p class="fs-big my-1">Khóa thi: <b>${
                                                    cell.getData().TenKhoaThi ||
                                                    ChuaCoThongTin
                                                }</b></p>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12">
                                                <p class="fs-big my-1">Học sinh trường: <b>${
                                                    cell.getData().TenDonVi ||
                                                    ChuaCoThongTin
                                                }</b></p>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12">
                                                <p class="fs-big my-1">Ghi chú: <b>${
                                                    cell.getData().GhiChu ||
                                                    ChuaCoThongTin
                                                }</b></p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-2">
                                        <div class="dropdown text-end" style="position: absolute; top: 10px; right: 10px">
                                            <button class="btn btn-sm btn-white dropdown-toggle-hide-arrow" type="button" style="font-size:18px" id="growthReportId" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                <i class="blue fa fa-ellipsis-h" style="color: #696cff"></i>
                                            </button>
                                            <div class="dropdown-menu dropdown-menu-end w-auto" >
                                                <a style="padding: 5px 12px" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="XemThongTin('${
                                                    cell.getData().id
                                                }'); return false;"><i class="fa fa-eye text-success" aria-hidden="true"></i>&ensp; Xem thông tin</a>
                                                <a style="padding: 5px 12px" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="SuaThongTin('${
                                                    cell.getData().id
                                                }'); return false;"><i class=" fa fa-pencil iconsize-item text-primary"></i>&ensp; Chỉnh sửa thông tin</a>
                                                <a style="padding: 5px 12px" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="XoaThongTin('${
                                                    cell.getData().id
                                                }')"><i class="fa fa-trash-o iconsize-item text-danger"></i>&ensp; Xóa thông tin</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>`;
}
var table = new Tabulator("#GridMainDS", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "600",
    HeaderVertAlign: "center",
    headerVisible: false,
    columns: [
        {
            title: "Thông tin",
            field: "",
            formatter: htmlDuLieu,
            visible: true,
            minWidth: 250,
        },
    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});

async function LoadDataTable() {
    table.clearData();
    GridMainLuoi.clearData();
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/getall",
        {
            SearchKey: $("#SearchKey").value(),
            QuyetDinhID: $("#QuyetDinhID_Loc").value(),
            NamTotNghiep: $("#NamTotNghiep_Loc").value(),
            KyThiID: $("#KyThiID_Loc").value(),
            KhoaThiID: $("#KhoaThiID_Loc").value(),
            CbSapXep: $("#CbSapXep").value(),
        }
    );
    if (!result.Err) {
        table.setData(result.result);
        GridMainLuoi.setData(result.result);
        table.redraw(true);
        GridMainLuoi.redraw(true);
    } else {
        table.setData(null);
        GridMainLuoi.setData(null);
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}

//#region Lưới 2
function actionDropdownFormatter(cell) {
    var ID = cell.getData().id;
    var button = document.createElement("button");
    button.className = "btn btn-sm btn-white dropdown-toggle-hide-arrow";
    button.innerHTML = `<i class="fa fa-ellipsis-h" style="color: var(--primary)"></i>`;
    button.style.boxShadow = "none";

    button.onclick = function (e) {
        e.stopPropagation(); // Không lan click ra ngoài

        // Đóng dropdown cũ nếu có
        document
            .querySelectorAll(".custom-dropdown-menu")
            .forEach((el) => el.remove());

        // Tạo dropdown mới
        const dropdown = document.createElement("div");
        dropdown.className =
            "custom-dropdown-menu dropdown-menu dropdown-menu-end show";
        dropdown.style.position = "absolute";
        dropdown.style.zIndex = 9999;
        dropdown.style.minWidth = "200px";
        dropdown.innerHTML =
            `
            <a  onclick="XemThongTin('${
                cell.getData().id
            }'); return false;" style="padding: 5px 12px" class="dropdown-item textsize-item btnXemGridMainLuoi" data="` +
            ID +
            `" href="javascript:void(0);" >
                    <i class="text-success fa fa-eye iconsize-item" aria-hidden="true"></i>&ensp; Xem thông tin
                </a>
            <a  onclick="SuaThongTin('${
                cell.getData().id
            }'); return false;" style="padding: 5px 12px" class="dropdown-item textsize-item btnSuaGridMainLuoi" data="` +
            ID +
            `" href="javascript:void(0);" >
                    <i class="text-primary fa fa-pencil-square-o iconsize-item" aria-hidden="true"></i>&ensp; Chỉnh sửa thông tin
                </a>
            <a  onclick="XoaThongTin('${
                cell.getData().id
            }'); return false;" style="padding: 5px 12px" class="dropdown-item textsize-item btnXoaGridMainLuoi" data="` +
            ID +
            `" href="javascript:void(0);" >
                    <i class="fa fa-trash-o iconsize-item text-danger"></i>&ensp; Xóa thông tin
                </a>
        `;

        // Tính vị trí button
        const rect = button.getBoundingClientRect();
        dropdown.style.left = `${rect.left + window.scrollX}px`;
        dropdown.style.top = `${rect.bottom + window.scrollY}px`;

        // Gắn ra body
        document.body.appendChild(dropdown);
    };

    return button;
}
var GridMainLuoi = new Tabulator("#GridMainLuoi", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "550",
    HeaderVertAlign: "center",
    columns: [
        {
            title: '<i class="fa fa-ellipsis-h"></i>',
            headerHozAlign: "center",
            hozAlign: "center",
            formatter: actionDropdownFormatter,
            width: 60,
            headerSort: false,
            frozen: true,
            vertAlign: "middle",
            print: false,
        },
        {
            title: "Quyết định",
            field: "TenQuyetDinh",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 350,
        },
        {
            title: "Hình thức học",
            field: "TenHinhThuc",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 250,
        },
        {
            title: "Năm tốt nghiệp",
            field: "NamTotNghiep",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            visible: true,
            width: 100,
        },
        {
            title: "Kỳ thi",
            field: "TenKyThi",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 300,
        },
        {
            title: "Khóa thi",
            field: "TenKhoaThi",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 300,
        },
        {
            title: "Học sinh trường",
            field: "TenDonVi",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 300,
        },
        {
            title: "Ghi chú",
            field: "GhiChu",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            minWidth: 250,
        },
    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});

//#region  Chọn đơn vị
$(document).on("click", "#btnChonDonViCha", function () {
    LoadGrid_ChonDonVi_us("");
    $("#mdChonDonVi_us").modal("show");
});
$(document).on("click", "#btnChonDonViVaDong_us", function () {
    if (Grid_ChonDonVi_us.getSelectedRows().length == 0) {
        NTS.canhbao("Vui lòng chọn 1 đơn vị!");
        return false;
    }

    var data = Grid_ChonDonVi_us.getSelectedRows()[0]._row.data;
    $("#TenTruongHoc").value(data.TenDonVi);
    $("#DonViID_TruongHoc").value(data.id);
    $("#mdChonDonVi_us").modal("hide");
});
//#endregion
//#region Chuyển step modal thêm mới
// Simple step switch logic
function showStep(step) {
    document.getElementById("step-1-content").style.display =
        step === 1 ? "" : "none";
    document.getElementById("step-2-content").style.display =
        step === 2 ? "" : "none";

    // Update step sidebar highlight
    document
        .getElementById("sidebar-step-1")
        .classList.toggle("active", step === 1);
    document
        .getElementById("sidebar-step-2")
        .classList.toggle("active", step === 2);

    // Optional: update title if needed
    document.getElementById("lblTieuDeMultiStep").textContent =
        step === 1 ? "Nhập thông tin sổ gốc" : "Thành phần sổ gốc";
}

// Attach next/prev button events (call showStep with 1 or 2)
document.addEventListener("DOMContentLoaded", function () {
    // "Tiếp tục" on Step 1
    // document.getElementById("btnTiepTuc").onclick = function (e) {
    //     e.preventDefault();
    //     showStep(2);
    // };
    // "Quay lại" on Step 2
    document.getElementById("btnQuayLaiBuoc1").onclick = function (e) {
        e.preventDefault();
        showStep(1);
    };
    // Initialize step 1
    showStep(1);
});

//#endregion Chuyển step

//#region Chi tiết
var fmThaoTac = function (cell) {
    return formaterbtnThaoTac(cell.getData().id);
};
$(document).on("click", ".btnSuaGrid1", function () {
    NTS.loadDataComboAsync({
        name: "#XepLoaiID_CT",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListXepLoai,
        columns: 1,
        indexValue: 0,
        indexText: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    $("#SoGocCT").val($(this).attr("data"));
    SuaDuLieuCT($(this).attr("data"));
});

$(document).on("click", ".btnXoaGrid1", function () {
    $("#SoGocCT").val($(this).attr("data"));
    XoaDuLieuCT($(this).attr("data"));
});
async function SuaDuLieuCT(id) {
    if (!QuyenSua()) {
        return;
    }
    resetForm("#mdThemMoiHocSinh");
    $("#lblTieuDemdThemMoiHocSinh").text(
        "Cập nhật thông tin học sinh/sinh viên"
    );
    var result = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.local.loaddulieusuact,
        {
            id: id,
        }
    );
    if (!result.Err) {
        let data = result.Result;

        $("#customSelect").text(data.TenHocSinh);
        $("#HocSinhID_CT").value(data.DoiTuongID_HocSinh);
        $("#txtDonViID_CT").value(data.TenTruong);
        $("#XepLoaiID_CT").value(data.XepLoaiID);
        $("#DiemThi_CT").value(data.DiemThi);
        $("#SoVaoSoGoc_CT").value(data.SoVaoSoGoc);
        $("#SoHieuVanBang_CT").value(data.SoHieuVanBang);
        $("#SoGocCTID").value(data.id);
        $("#GhiChu_CT").value(data.GhiChu);
        $("#mdThemMoiHocSinh").modal("show");
        tempthemCT = "sua";
    }
}
async function XoaDuLieuCT(id) {
    if (!(await QuyenXoa())) {
        return false;
    }
    CanhBaoXoa(async () => {
        var result = await NTS.getAjaxAPIAsync(
            "DELETE",
            window.Laravel.local.xoact,
            { ma: id }
        );
        if (!result.Err) {
            LoadDataTableCT();
            NTS.thanhcong(result.Msg);
        } else {
            result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        }
    });
    return false;
}
var tableCT = new Tabulator("#GridCT", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "550",
    HeaderVertAlign: "center",
    columns: [
        {
            title: '<i class="fa fa-ellipsis-h"></i>',
            headerHozAlign: "center",
            hozAlign: "center",
            formatter: fmThaoTac,
            width: 60,
            headerSort: false,
            frozen: true,
            vertAlign: "middle",
            print: false,
        },
        {
            title: "STT",
            field: "STT",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 60,
        },
        {
            title: "Họ và tên người học",
            field: "HoTenHocSinh",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 250,
        },
        {
            title: "Ngày tháng năm sinh",
            field: "txtNgaysinh",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            visible: true,
            width: 160,
        },
        {
            title: "Nơi sinh",
            field: "NoiSinh",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 300,
        },
        {
            title: "Giới tính",
            field: "Gioitinh",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 110,
        },
        {
            title: "Dân tộc",
            field: "TenDanToc",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 110,
        },
        {
            title: "Điểm thi",
            field: "DiemThi",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 100,
        },
        {
            title: "Xếp loại",
            field: "TenXepLoai",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            minWidth: 150,
        },
        {
            title: "Số hiệu văn bằng",
            field: "SoHieuVanBang",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            minWidth: 250,
        },
        {
            title: "Số vào sổ gốc cấp văn bằng",
            field: "SoVaoSoGoc",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            minWidth: 250,
        },
        {
            title: "Ghi chú",
            field: "GhiChu",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            minWidth: 250,
        },
    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});
$(document).on("keyup", "#searchContent", function (e) {
    var data = $(this).val();
    if (e.keyCode == "13") {
        if (data == "" || data == undefined) {
            data = $("#searchContent").value();
        }
        dulieuloc = data;
        tableCT.setFilter([
            [
                {
                    field: "HoTenHocSinh",
                    type: "like",
                    value: data,
                },
                {
                    field: "NoiSinh",
                    type: "like",
                    value: data,
                },
                {
                    field: "TenDanToc",
                    type: "like",
                    value: data,
                },
            ],
        ]);
    }
});
$(document).on("click", "#btnChonVaDong", function () {
    if (GridChonHocSinh.getSelectedRows().length == 0) {
        NTS.canhbao("Vui lòng chọn 1 học sinh!");
        return false;
    }
    $("#mdChonHocSinh").modal("hide");
    $("#mdThemMoiHocSinh").modal("show");

    var data = GridChonHocSinh.getSelectedRows()[0]._row.data;
    $("#customSelect").text(data.Hovaten);
    $("#HocSinhID_CT").value(data.id);
    $("#txtDonViID_CT").value(data.TenDonVi);
    $("#XepLoaiID_CT").value(data.KetQuaTN);
});
async function LoadDataTableCT() {
    tableCT.clearData();
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.local.getallct,
        {
            SoGocID: $("#SoGocID").value(),
        }
    );
    if (!result.Err) {
        tableCT.setData(result.result);
    } else {
        tableCT.setData(null);
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}

$("#btnThemMoiHS").on("click", function () {
    resetForm("#mdThemMoiHocSinh");
    $("#mdThemMoiHocSinh").modal("show");
    tempthemCT = "them";
    NTS.loadDataComboAsync({
        name: "#XepLoaiID_CT",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListXepLoai,
        columns: 1,
        indexValue: 0,
        indexText: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    $("#lblTieuDemdThemMoiHocSinh").text("Thêm mới học sinh/sinh viên");
    $("#selectedOption").html("Chọn học sinh");
});
$("#btnLuuVaDongCT").on("click", async function () {
    const validate = new NTSValidate("#mdThemMoiHocSinh");
    if (!validate.trim().check()) return false;
    const payload = {
        DoiTuongID_HocSinh: $("#HocSinhID_CT").value(),
        XepLoaiID: $("#XepLoaiID_CT").value(),
        DiemThi: $("#DiemThi_CT").value(),
        SoVaoSoGoc: $("#SoVaoSoGoc_CT").value(),
        SoHieuVanBang: $("#SoHieuVanBang_CT").value(),
        GhiChu: $("#GhiChu_CT").value(),
        SoGocID: $("#SoGocID").value(),
        SoGocCTID: $("#SoGocCTID").value(),
    };
    var met = "POST";
    if (tempthemCT == "them") {
        met = "POST";
    } else {
        met = "PUT";
    }

    var result = await NTS.getAjaxAPIAsync(
        met,
        window.Laravel.local.luuthongtinct,
        payload
    );
    if (!result.Err) {
        LoadDataTableCT();
        NTS.thanhcong(result.Msg);
        $("#mdThemMoiHocSinh").modal("hide");
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        return false;
    }
});

$("#btnDropdownHS").on("click", function () {
    $("#mdChonHocSinh").modal("show");
    $("#mdThemMoiHocSinh").modal("hide");
    LoadDataTableTruongHoc();
});

$(".close-mdChonHocSinh").on("click", function () {
    $("#mdChonHocSinh").modal("hide");
    $("#mdThemMoiHocSinh").modal("show");
});
//#region Lưới Trường Học
var GridTruongHoc = new Tabulator("#GridTruongHoc", {
    layout: "fitColumns",
    height: "350",
    selectableRows: 1,
    selectable: 1,
    HeaderVertAlign: "center",
    columns: [
        {
            field: "action",
            width: "30",
            formatter: function () {
                return '<i class="fas fa-hand-point-right text-nts-primary"></i>';
            },
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            minWidth: 30,
        },
        {
            title: "Tên trường",
            field: "name",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            minWidth: 100,
        },
    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});
$(document).on("keyup", "#timKiemTH", function (e) {
    var data = $(this).val();
    if (e.keyCode == "13") {
        if (data == "" || data == undefined) {
            data = $("#timKiemTH").value();
        }
        dulieuloc = data;
        GridTruongHoc.setFilter([
            [
                {
                    field: "name",
                    type: "like",
                    value: data,
                },
            ],
        ]);
    }
});
GridTruongHoc.on("rowClick", async function (e, row) {
    LoadDataTableHocSinh(row.getData().id);
});
async function LoadDataTableTruongHoc() {
    GridTruongHoc.clearData();
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.local.GetListDonViCoHocSinh,
        {
            QuyetDinhID: $("#QuyetDinhID").value(),
        }
    );
    if (!result.Err) {
        GridTruongHoc.setData(result.Result);
    } else {
        GridTruongHoc.setData(null);
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}
//#region Lưới Học sinh
const commonColumnConfig = {
    headerHozAlign: "center",
    headerVertAlign: "center",
    vertAlign: "middle",
};
var GridChonHocSinh = new Tabulator("#GridChonHocSinh", {
    layout: "fitColumns",
    height: "350",
    HeaderVertAlign: "center",
    columns: [
        {
            formatter: "rowSelection",
            titleFormatter: "rowSelection",
            hozAlign: "center",
            headerSort: false,
            width: 50,
            selectable: true,
            cellClick: function (e, cell) {
                cell.getRow().toggleSelect();
            },
            ...commonColumnConfig,
        },
        {
            title: "Mã học sinh",
            field: "MaDoiTuong",
            width: 120,
            ...commonColumnConfig,
        },
        {
            title: "Họ và tên",
            field: "Hovaten",
            width: 150,
            formatter: "textarea",
            ...commonColumnConfig,
        },
        {
            title: "Số CMND/CCCD",
            field: "CCCD",
            width: 150,
            ...commonColumnConfig,
        },
        {
            title: "Ngày sinh",
            field: "txtNgaysinh",
            sorter: "date",
            hozAlign: "center",
            width: 120,
            ...commonColumnConfig,
        },
        {
            title: "Giới tính",
            field: "Gioitinh",
            hozAlign: "center",
            width: 100,
            ...commonColumnConfig,
        },
        {
            title: "Dân tộc",
            field: "TenDanToc",
            width: 120,
            ...commonColumnConfig,
        },
        {
            title: "Nơi sinh",
            field: "Noisinh",
            width: 150,
            ...commonColumnConfig,
        },
        {
            title: "Địa chỉ",
            field: "DiaChi",
            width: 200,
            ...commonColumnConfig,
        },
    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});
$(document).on("keyup", "#timKiemHS", function (e) {
    var data = $(this).val();
    if (e.keyCode == "13") {
        if (data == "" || data == undefined) {
            data = $("#timKiemHS").value();
        }
        dulieuloc = data;
        GridChonHocSinh.setFilter([
            [
                {
                    field: "Hovaten",
                    type: "like",
                    value: data,
                },
                {
                    field: "CCCD",
                    type: "like",
                    value: data,
                },
                {
                    field: "MaDoiTuong",
                    type: "like",
                    value: data,
                },
            ],
        ]);
    }
});
async function LoadDataTableHocSinh(ID) {
    GridChonHocSinh.clearData();
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.local.GetListHocSinhByDonVi,
        {
            DonViID: ID,
            QuyetDinhID: $("#QuyetDinhID").value(),
        }
    );
    if (!result.Err) {
        GridChonHocSinh.setData(result.result);
    } else {
        GridChonHocSinh.setData(null);
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}

//#region Xem chi tiết
var GridXemCT = new Tabulator("#GridXemCT", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "350",
    HeaderVertAlign: "center",
    columns: [
        {
            title: "STT",
            field: "STT",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            visible: true,
            width: 60,
        },
        {
            title: "Họ và tên người học",
            field: "HoTenHocSinh",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 250,
        },
        {
            title: "Ngày tháng năm sinh",
            field: "txtNgaysinh",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            visible: true,
            width: 160,
        },
        {
            title: "Nơi sinh",
            field: "NoiSinh",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 300,
        },
        {
            title: "Giới tính",
            field: "Gioitinh",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 110,
        },
        {
            title: "Dân tộc",
            field: "TenDanToc",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 110,
        },
        {
            title: "Điểm thi",
            field: "DiemThi",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 100,
        },
        {
            title: "Xếp loại",
            field: "TenXepLoai",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            minWidth: 150,
        },
        {
            title: "Số hiệu văn bằng",
            field: "SoHieuVanBang",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            minWidth: 250,
        },
        {
            title: "Số vào sổ gốc cấp văn bằng",
            field: "SoVaoSoGoc",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            minWidth: 250,
        },
        {
            title: "Ghi chú",
            field: "GhiChu",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            minWidth: 250,
        },
    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});
$(document).on("keyup", "#timKiem_Xem", function (e) {
    var data = $(this).val();
    if (e.keyCode == "13") {
        if (data == "" || data == undefined) {
            data = $("#timKiem_Xem").value();
        }
        dulieuloc = data;
        GridXemCT.setFilter([
            [
                {
                    field: "HoTenHocSinh",
                    type: "like",
                    value: data,
                },
                {
                    field: "NoiSinh",
                    type: "like",
                    value: data,
                },
                {
                    field: "TenDanToc",
                    type: "like",
                    value: data,
                },
            ],
        ]);
    }
});
