let hotKey = 0;
let tempAction = "them";
let comboData = {};
var fmThaoTac = function (cell) {
    return formaterbtnThaoTac(cell.getData().id);
};
var fmDangSD = function (cell) {
    return formaterDangSD(cell.getValue(), cell.getData().id);
};
var fmNoiBat = function (cell) {
    const value = cell.getValue();
    return value ? '<span class="badge bg-warning">N<PERSON><PERSON> bật</span>' : '<span class="badge bg-secondary">Thường</span>';
};

var fmKiemDuyet = function (cell) {
    const data = cell.getData();
    const trangThai = data.TrangThaiKiemDuyet;
    const tenTrangThai = data.TenTrangThaiKiemDuyet || 'Đã duyệt';
    const mauTrangThai = data.MauTrangThaiKiemDuyet || 'success';

    return `<span class="badge bg-${mauTrangThai}">${tenTrangThai}</span>`;
};

$(document).ready(function () {
    // Load combo data first
    loadComboData();

    // initialize Tabulator
    loadData();

    // Hide bulk actions initially
    $("#bulkActionsDropdown").hide();

    // set CSRF header for all AJAX
    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });
});

// Initialize status toggle functionality for news
console.log('Initializing custom status toggle for news module');
checkDangSDTinTuc('.checkDangSD');

// keyboard shortcuts
$(document).on("keydown", function (e) {
    switch (e.keyCode) {
        case 113: // F2
            if (hotKey === 0) $("#btnThemMoi").trigger("click");
            e.preventDefault();
            break;
        case 115: // F4
            if (hotKey === 1) $("#mdThemMoi").modal("hide");
            e.preventDefault();
            break;
        case 120: // F9
            if (hotKey === 1) $("#btnLuuVaDong").trigger("click");
            e.preventDefault();
            break;
    }
});

$("#mdThemMoi").on("shown.bs.modal", () => (hotKey = 1));
$("#mdThemMoi").on("hidden.bs.modal", () => (hotKey = 0));

// Custom status toggle function for news module
async function checkDangSDTinTuc(selector) {
    console.log('Setting up status toggle for selector:', selector);
    $(document).on("change.checkDangSDTinTuc", selector, async function () {
        console.log('Status toggle clicked!');
        if (!(await ntspermiss.sua)) {
            $(this).click();
            NTS.canhbao("Bạn không có quyền sửa");
            return;
        }
        const inp = $(this);
        const id = inp.attr("data");
        const originalValue = inp.prop("checked");

        if (!id) {
            console.warn("No data attribute found on:", inp);
            inp.prop("checked", !originalValue);
            NTS.canhbao("Không tìm thấy ID bản ghi.");
            return;
        }

        $.confirm({
            title: '<span class="text-dark">Cảnh báo!</span>',
            type: "blue",
            icon: "fa fa-question-circle",
            typeAnimated: true,
            theme: "material",
            columnClass: "col-md-5 col-md-offset-3 w-max-400px",
            content: "Bạn có chắc muốn thay đổi trạng thái sử dụng của tin tức này?",
            buttons: {
                confirm: {
                    text: '<i class="fa fa-check"></i> Chấp nhận',
                    btnClass: "btn-primary",
                    keys: ["enter"],
                    action: async function () {
                        try {
                            const apiUrl = window.Laravel.tintuc.toggleStatusUrl.replace(':id', id);
                            const response = await NTS.getAjaxAPIAsync(
                                "POST",
                                apiUrl,
                                {
                                    field: 'DangSD'
                                }
                            );
                            if (response && !response.Err) {
                                NTS.thanhcong(
                                    response.Msg || "Cập nhật trạng thái thành công."
                                );
                            } else {
                                inp.prop("checked", !originalValue);
                                const msg =
                                    response && response.Msg
                                        ? response.Msg
                                        : "Lỗi không xác định.";
                                response && response.canhbao
                                    ? NTS.canhbao(msg)
                                    : NTS.loi(msg);
                            }
                        } catch (error) {
                            inp.prop("checked", !originalValue);
                            NTS.loi("Lỗi hệ thống: " + error.message);
                        }
                    },
                },
                cancel: {
                    text: '<i class="fa fa-close"></i> Hủy bỏ',
                    btnClass: "btn-danger",
                    keys: ["esc"],
                    action: function () {
                        inp.prop("checked", !originalValue);
                    },
                },
            },
        });
    });
}

$("#btnThemMoi").on("click", showCreateModal);

$(document).on("click", ".btnSuaGrid1", function () {
    $("#tinTucId").val($(this).attr("data"));
    SuaDuLieu($(this).attr("data"));
});

$(document).on("click", ".btnXoaGrid1", function () {
    $("#tinTucId").val($(this).attr("data"));
    XoaDuLieu($(this).attr("data"));
});

// Handle bulk delete
$(document).on("click", "#XoaChon", function () {
    var selectedRows = table.getSelectedData();
    if (selectedRows.length === 0) {
        NTS.canhbao("Vui lòng chọn ít nhất một tin tức để xóa!");
        return;
    }

    var ids = selectedRows.map(row => row.id);
    var message = `Bạn có chắc chắn muốn xóa ${selectedRows.length} tin tức đã chọn?`;

    $.confirm({
        title: '<span class="text-danger">Cảnh báo!</span>',
        type: "red",
        icon: "fa fa-exclamation-triangle",
        typeAnimated: true,
        theme: "material",
        columnClass: "col-md-5 col-md-offset-3 w-max-400px",
        content: message,
        buttons: {
            confirm: {
                text: '<i class="fa fa-check"></i> Xác nhận',
                btnClass: "btn-danger",
                keys: ["enter"],
                action: async function () {
                    try {
                        var result = await NTS.getAjaxAPIAsync(
                            "DELETE",
                            window.Laravel.tintuc.bulkDelete,
                            { ids: ids }
                        );

                        if (!result.err) {
                            await LoadDataTable();
                            NTS.thanhcong(result.msg);
                            table.deselectRow(); // Clear selection
                        } else {
                            result.canhbao ? NTS.canhbao(result.msg) : NTS.loi(result.msg);
                        }
                    } catch (error) {
                        console.error("Lỗi khi xóa hàng loạt:", error);
                        NTS.loi("Có lỗi xảy ra khi xóa tin tức");
                    }
                }
            },
            cancel: {
                text: '<i class="fa fa-times"></i> Hủy',
                btnClass: "btn-secondary"
            }
        }
    });
});

function loadComboData() {
    $.ajax({
        url: '/bientapcongthongtin/tintuc/getComboData',
        type: 'GET',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function (response) {
            if (response.Err === false) {
                comboData = response.data;
                populateComboBoxes();

                // Khởi tạo bảng sau khi có dữ liệu thiết lập
                initializeTable();
                setupTableEvents();
                LoadDataTable();

                // Ẩn/hiện các thành phần kiểm duyệt
                toggleKiemDuyetElements();
            }
        },
        error: function () {
            NTS.loi('Không thể tải dữ liệu combo!');
        }
    });
}

function populateComboBoxes() {
    // Populate loại tin tức in modal
    let loaiTinTucSelect = $('#loaiTinTucID');
    loaiTinTucSelect.empty().append('<option value="">--Chọn loại bài viết--</option>');
    comboData.loaiTinTucs.forEach(function (item) {
        loaiTinTucSelect.append(`<option value="${item._id}">${item.TenLoaiTinTuc}</option>`);
    });

    // Populate loại tin tức in filter
    let loaiTinTucFilter = $('#LoaiTinTuc_Loc');
    loaiTinTucFilter.empty().append('<option value="">— Chọn loại tin tức —</option>');
    comboData.loaiTinTucs.forEach(function (item) {
        loaiTinTucFilter.append(`<option value="${item._id}">${item.TenLoaiTinTuc}</option>`);
    });
}

async function showCreateModal() {
    tempAction = "them";
    resetForm("#mdThemMoi");
    $("#dangSD").prop("checked", true);
    $("#laNoiBat").prop("checked", true); // Mặc định đánh dấu nổi bật

    // Thiết lập mặc định cho checkbox "Đã kiểm duyệt"
    const yeuCauKiemDuyet = comboData?.thietLapWebsite?.ck_YeuCauKiemDuyet || false;

    if (yeuCauKiemDuyet) {
        // Hiện toggle kiểm duyệt và thiết lập giá trị mặc định
        $(".kiem-duyet-toggle").show();
        const defaultKiemDuyet = comboData?.thietLapWebsite?.ck_TuDongDuyetBai || false;
        $("#daKiemDuyet").prop("checked", defaultKiemDuyet);
    } else {
        // Ẩn toggle kiểm duyệt
        $(".kiem-duyet-toggle").hide();
    }

    $("#tieuDeModal").text("Thêm mới tin tức");
    $("#tinTucId").val("");

    // Set default values for new fields
    const today = new Date().toISOString().split('T')[0];
    $("#ngayTao").val(today);
    $("#nguoiTao").val('Admin'); // Replace with actual user name
    $("#dinhDanh").val('');

    // Clear image preview
    $("#previewHinhAnh").hide();
    $("#imgPreview").attr('src', '').hide();
    $("#fileHinhAnh").val('');
    $("#tenFileHinhAnh").text('');

    // Clear TinyMCE content
    if (tinymce.get('noiDung')) {
        tinymce.get('noiDung').setContent('');
    }

    // Show the modal
    $("#mdThemMoi").modal("show");
}

async function SuaDuLieu(id) {
    try {
        tempAction = "sua";
        $("#tieuDeModal").text("Cập nhật thông tin tin tức");

        const result = await NTS.getAjaxAPIAsync(
            "GET",
            window.location.pathname + "/loaddulieusua",
            { id: id }
        );

        if (!result.Err) {
            const d = result.Result[0];
            $("#tinTucId").val(d.tinTucId);
            $("#tieuDe").val(d.tieuDe);
            $("#loaiTinTucID").val(d.loaiTinTucID);
            $("#hinhAnh").val(d.hinhAnh);
            $("#tuKhoa").val(d.tuKhoa);
            $("#noiDungTomTat").val(d.noiDungTomTat);

            // Set new fields
            if (d.ngayTao) {
                const ngayTao = new Date(d.ngayTao).toISOString().split('T')[0];
                $("#ngayTao").val(ngayTao);
            }
            $("#nguoiTao").val(d.nguoiTao || 'Admin');
            $("#dinhDanh").val(d.dinhDanh || '');

            // Handle image preview for edit mode
            if (d.hinhAnh) {
                $("#imgPreview").attr('src', d.hinhAnh);
                $("#previewHinhAnh").show();
                // Extract filename from URL for display
                const fileName = d.hinhAnh.split('/').pop();
                $("#tenFileHinhAnh").text(fileName);
            } else {
                $("#previewHinhAnh").hide();
                $("#tenFileHinhAnh").text('');
            }

            // Set TinyMCE content
            if (tinymce.get('noiDung')) {
                tinymce.get('noiDung').setContent(d.noiDung || '');
            } else {
                $("#noiDung").val(d.noiDung);
            }

            // Set checkboxes
            $("#dangSD").prop("checked", !!d.dangSD);
            $("#laNoiBat").prop("checked", !!d.laNoiBat);

            // Thiết lập checkbox "Đã kiểm duyệt" dựa trên thiết lập và trạng thái hiện tại
            const yeuCauKiemDuyet = comboData?.thietLapWebsite?.ck_YeuCauKiemDuyet || false;

            if (yeuCauKiemDuyet) {
                // Hiện toggle kiểm duyệt và thiết lập giá trị
                $(".kiem-duyet-toggle").show();
                const isDaDuyet = d.trangThaiKiemDuyet === 1 || d.trangThaiKiemDuyet === undefined;
                $("#daKiemDuyet").prop("checked", isDaDuyet);
            } else {
                // Ẩn toggle kiểm duyệt
                $(".kiem-duyet-toggle").hide();
            }
            $("#donViXem").prop("checked", !!d.donViXem);

            $("#mdThemMoi").modal("show");
        } else {
            result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        }
    } catch (error) {
        console.error('Error:', error);
        NTS.loi('Có lỗi xảy ra khi tải dữ liệu');
    }
}

$(document).on("keyup", "#timKiem", function (e) {
    var data = $(this).val();
    if (e.keyCode == "13") {
        if (data == "" || data == undefined) {
            data = $("#timKiem").value();
        }
        table.setFilter([
            [
                {
                    field: "TieuDe",
                    type: "like",
                    value: data,
                },
                {
                    field: "NoiDungTomTat",
                    type: "like",
                    value: data,
                },
                {
                    field: "TuKhoa",
                    type: "like",
                    value: data,
                },
            ],
        ]);
    }
});

$("#btnLuuVaDong").on("click", async function () {
    // Validate required fields
    const validate = new NTSValidate("#mdThemMoi");
    if (!validate.trim().check()) {
        return false;
    }

    const isCreate = tempAction === "them";
    const tinTucId = $("#tinTucId").val();

    // Get content from TinyMCE
    let noiDungContent = '';
    if (tinymce.get('noiDung')) {
        noiDungContent = tinymce.get('noiDung').getContent();
    } else {
        noiDungContent = $("#noiDung").val();
    }

    // build payload
    const payload = {
        tinTucId: tinTucId,
        tieuDe: $("#tieuDe").val(),
        loaiTinTucID: $("#loaiTinTucID").val(),
        hinhAnh: $("#hinhAnh").val(),
        tuKhoa: $("#tuKhoa").val(),
        noiDungTomTat: $("#noiDungTomTat").val(),
        noiDung: noiDungContent,
        dangSD: $("#dangSD").is(":checked"),
        laNoiBat: $("#laNoiBat").is(":checked"),
        daKiemDuyet: $("#daKiemDuyet").is(":checked"),
        donViXem: $("#donViXem").is(":checked"),
        ngayTao: $("#ngayTao").val(),
        nguoiTao: $("#nguoiTao").val(),
        dinhDanh: $("#dinhDanh").val(),
    };

    try {
        var result = await NTS.getAjaxAPIAsync(
            isCreate ? "POST" : "PUT",
            window.location.pathname + "/luuthongtin",
            payload
        );

        if (!result.err) {
            await LoadDataTable();
            NTS.thanhcong(result.msg);
            $("#mdThemMoi").modal("hide");
        } else {
            result.canhbao ? NTS.canhbao(result.msg) : NTS.loi(result.msg);
            return false;
        }
    } catch (error) {
        console.error("Lỗi khi lưu thông tin:", error);
        NTS.loi("Có lỗi xảy ra khi lưu thông tin");
        return false;
    }
});

// Tạo cấu hình cột động dựa trên thiết lập kiểm duyệt
function createTableColumns() {
    const baseColumns = [
        {
            formatter: "rowSelection",
            titleFormatter: "rowSelection",
            hozAlign: "center",
            cellClick: function (_, cell) {
                cell.getRow().toggleSelect();
            },
            width: 40,
            headerSort: false,
            frozen: true,
            print: false,
        },
        {
            title: '<i class="fa fa-ellipsis-h"></i>',
            headerHozAlign: "center",
            hozAlign: "center",
            formatter: fmThaoTac,
            width: 60,
            headerSort: false,
            frozen: true,
            vertAlign: "middle",
            print: false,
        },
        {
            title: "Tiêu đề",
            field: "TieuDe",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            minWidth: 250,
        },
        {
            title: "Loại tin tức",
            field: "LoaiTinTuc",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 150,
        },
        {
            title: "Người tạo",
            field: "NguoiTao",
            hozAlign: "left",
            headerHozAlign: "center",
            formatter: "textarea",
            vertAlign: "middle",
            width: 120,
        },
        {
            title: "Ngày tạo",
            field: "NgayTao",
            hozAlign: "center",
            headerHozAlign: "center",
            formatter: "textarea",
            vertAlign: "middle",
            width: 120,
        },
        {
            title: "Lượt xem",
            field: "LuotXem",
            hozAlign: "center",
            headerHozAlign: "center",
            formatter: "textarea",
            vertAlign: "middle",
            width: 100,
        },
        {
            title: "Trạng thái sử dụng",
            field: "DangSD",
            headerWordWrap: true,
            hozAlign: "center",
            vertAlign: "middle",
            formatter: fmDangSD,
            headerSort: false,
            width: 135,
            headerHozAlign: "center",
        },
        {
            title: "Nổi bật",
            field: "LaNoiBat",
            headerWordWrap: true,
            hozAlign: "center",
            vertAlign: "middle",
            formatter: fmNoiBat,
            headerSort: false,
            width: 100,
            headerHozAlign: "center",
        },
    ];

    // Thêm cột kiểm duyệt nếu yêu cầu kiểm duyệt được bật
    if (comboData?.thietLapWebsite?.ck_YeuCauKiemDuyet) {
        baseColumns.push({
            title: "Đã kiểm duyệt",
            field: "TrangThaiKiemDuyet",
            headerWordWrap: true,
            hozAlign: "center",
            vertAlign: "middle",
            formatter: fmKiemDuyet,
            headerSort: false,
            width: 120,
            headerHozAlign: "center",
        });
    }

    return baseColumns;
}

// Khởi tạo bảng với cấu hình động
var table;

function initializeTable() {
    table = new Tabulator("#Grid1", {
        layout: "fitColumns",
        pagination: true,
        paginationSize: 50,
        paginationSizeSelector: [50, 100, 150, 200, 500, true],
        height: "550",
        HeaderVertAlign: "center",
        locale: true,
        langs: TabulatorLangsVi,
        placeholder: "Không có dữ liệu",
        paginationCounter: "rows",
        selectableRows: true,
        columns: createTableColumns()
    });

    // Handle row selection changes to show/hide bulk actions
    table.on("rowSelectionChanged", function (data) {
        let count = data.length;
        if (count > 0) {
            $("#XoaChon").show();
            $("#XoaChon").text(`Xóa ${count} mục đã chọn`);
        } else {
            $("#XoaChon").hide();
        }
    });
}

// Add double-click event handler to open update modal
function setupTableEvents() {
    table.on("rowDblClick", function (_, row) {
        var rowData = row.getData();
        SuaDuLieu(rowData.id);
    });
}

// Ẩn/hiện các thành phần kiểm duyệt dựa trên thiết lập
function toggleKiemDuyetElements() {
    const yeuCauKiemDuyet = comboData?.thietLapWebsite?.ck_YeuCauKiemDuyet || false;

    if (yeuCauKiemDuyet) {
        // Hiện các thành phần kiểm duyệt
        $(".kiem-duyet-element").show();
        $("#TrangThaiKiemDuyet_Loc").closest('.col-md-6').show();
    } else {
        // Ẩn các thành phần kiểm duyệt
        $(".kiem-duyet-element").hide();
        $("#TrangThaiKiemDuyet_Loc").closest('.col-md-6').hide();
    }
}

async function loadData() {
    var result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/getall",
        {}
    );
    if (!result.err) {
        table.setData(result.result);
    } else {
        result.canhbao ? NTS.canhbao(result.msg) : NTS.loi(result.msg);
    }
}

function XoaDuLieu(id) {
    CanhBaoXoa(() => {
        var result = NTS.getAjaxAPI(
            "DELETE",
            window.location.pathname + "/xoa",
            { ma: id }
        );
        if (!result.err) {
            loadData();
            NTS.thanhcong(result.msg);
        } else {
            result.canhbao ? NTS.canhbao(result.msg) : NTS.loi(result.msg);
        }
    });
    return false;
}

async function LoadDataTable() {
    var result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/getall",
        {}
    );
    if (!result.err) {
        table.setData(result.result);
    } else {
        result.canhbao ? NTS.canhbao(result.msg) : NTS.loi(result.msg);
    }
}

function resetForm(form) {
    $(form).find('input[type="text"], input[type="url"], input[type="date"]').val('');
    $(form).find('input[type="file"]').val('');
    $(form).find('textarea').val('');
    $(form).find('select').val('');
    $(form).find('input[type="checkbox"]').prop('checked', false);
    // Hide image preview
    $("#previewHinhAnh").hide();
    $("#imgPreview").attr('src', '').hide();
    $("#tenFileHinhAnh").text('');
}

// Advanced search functionality
$(document).on("click", "#TimKiem", function () {
    var filters = [];

    // Tiêu đề filter
    var tieuDe = $("#TieuDe_Loc").val();
    if (tieuDe) {
        filters.push({
            field: "TieuDe",
            type: "like",
            value: tieuDe
        });
    }

    // Loại tin tức filter
    var loaiTinTuc = $("#LoaiTinTuc_Loc").val();
    if (loaiTinTuc) {
        filters.push({
            field: "LoaiTinTucID",
            type: "=",
            value: loaiTinTuc
        });
    }

    // Trạng thái sử dụng filter
    var dangSD = $("#DangSD_Loc").val();
    if (dangSD !== "") {
        filters.push({
            field: "DangSD",
            type: "=",
            value: dangSD === "1"
        });
    }

    // Nổi bật filter
    var laNoiBat = $("#LaNoiBat_Loc").val();
    if (laNoiBat !== "") {
        filters.push({
            field: "LaNoiBat",
            type: "=",
            value: laNoiBat === "1"
        });
    }

    // Trạng thái kiểm duyệt filter
    var trangThaiKiemDuyet = $("#TrangThaiKiemDuyet_Loc").val();
    if (trangThaiKiemDuyet !== "") {
        filters.push({
            field: "TrangThaiKiemDuyet",
            type: "=",
            value: parseInt(trangThaiKiemDuyet)
        });
    }

    if (filters.length > 0) {
        table.setFilter([filters]);
    } else {
        table.clearFilter();
    }

    $("#KhungTimKiem").slideUp(200);
});

// Khởi tạo khi trang được load
$(document).ready(function () {
    loadComboData();
});
