# Ẩn/Hiện Thành Phần Kiểm Duyệt Theo Thiết Lập

## Tóm Tắt Cập Nhật

Đã cập nhật hệ thống để **ẩn/hiện các thành phần kiểm duyệt** dựa trên thiết lập `ck_YeuCauKiemDuyet` trong bảng `thiet_lap_website`.

### Logic Ẩn/Hiện

**Khi `ck_YeuCauKiemDuyet = false`:**
- ✅ **Ẩn cột "Đã kiểm duyệt"** trong bảng danh sách tin tức
- ✅ **Ẩn toggle "Đã kiểm duyệt"** trong form thêm/sửa tin tức  
- ✅ **Ẩn bộ lọc "Trạng thái kiểm duyệt"** trong tìm kiếm nâng cao

**Khi `ck_YeuCauKiemDuyet = true`:**
- ✅ **<PERSON><PERSON><PERSON> cột "Đã kiểm duyệt"** trong bảng danh sách tin tức
- ✅ **Hi<PERSON><PERSON> toggle "Đã kiểm duyệt"** trong form thêm/sửa tin tức
- ✅ **Hiện bộ lọc "Trạng thái kiểm duyệt"** trong tìm kiếm nâng cao

## Files Đã Cập Nhật

### 1. Controller - Trả về thiết lập website
**File:** `app/Http/Controllers/BienTapCongThongTin/TinTucController.php`

```php
public function getComboData()
{
    $thietLapWebsite = \App\Models\CongThongTin\ThietLapWebsite::first();
    
    $data = [
        'loaiTinTucs' => LoaiTinTuc::where('DangSD', true)->get(['_id', 'TenLoaiTinTuc']),
        'thietLapWebsite' => [
            'ck_YeuCauKiemDuyet' => $thietLapWebsite->ck_YeuCauKiemDuyet ?? false,
            'ck_TuDongDuyetBai' => $thietLapWebsite->ck_TuDongDuyetBai ?? false,
        ]
    ];
    
    return response()->json(['Err' => false, 'data' => $data]);
}
```

### 2. JavaScript - Logic ẩn/hiện động
**File:** `public/js/bientapcongthongtin/tintuc.js`

#### A. Tạo cột bảng động:
```javascript
function createTableColumns() {
    const baseColumns = [
        // ... các cột cơ bản
    ];

    // Thêm cột kiểm duyệt nếu yêu cầu kiểm duyệt được bật
    if (comboData?.thietLapWebsite?.ck_YeuCauKiemDuyet) {
        baseColumns.push({
            title: "Đã kiểm duyệt",
            field: "TrangThaiKiemDuyet",
            formatter: fmKiemDuyet,
            // ...
        });
    }

    return baseColumns;
}
```

#### B. Ẩn/hiện thành phần UI:
```javascript
function toggleKiemDuyetElements() {
    const yeuCauKiemDuyet = comboData?.thietLapWebsite?.ck_YeuCauKiemDuyet || false;
    
    if (yeuCauKiemDuyet) {
        // Hiện các thành phần kiểm duyệt
        $(".kiem-duyet-element").show();
        $("#TrangThaiKiemDuyet_Loc").closest('.col-md-6').show();
    } else {
        // Ẩn các thành phần kiểm duyệt
        $(".kiem-duyet-element").hide();
        $("#TrangThaiKiemDuyet_Loc").closest('.col-md-6').hide();
    }
}
```

#### C. Ẩn/hiện toggle trong form:
```javascript
// Trong showCreateModal() và SuaDuLieu()
const yeuCauKiemDuyet = comboData?.thietLapWebsite?.ck_YeuCauKiemDuyet || false;

if (yeuCauKiemDuyet) {
    $(".kiem-duyet-toggle").show();
    // Thiết lập giá trị mặc định
} else {
    $(".kiem-duyet-toggle").hide();
}
```

### 3. HTML - Thêm class để điều khiển
**File:** `resources/views/bientapcongthongtin/tintuc/index.blade.php`

#### A. Bộ lọc kiểm duyệt:
```html
<div class="row mb-3 kiem-duyet-element">
    <div class="col-md-6">
        <label class="form-label" for="TrangThaiKiemDuyet_Loc">Trạng thái kiểm duyệt</label>
        <select class="form-control input-sm" id="TrangThaiKiemDuyet_Loc">
            <!-- options -->
        </select>
    </div>
</div>
```

#### B. Toggle trong form:
```html
<label class="form-check form-switch kiem-duyet-toggle">
    <input class="form-check-input" type="checkbox" id="daKiemDuyet">
    <label class="form-check-label" for="daKiemDuyet">Đã kiểm duyệt</label>
</label>
```

## Luồng Hoạt Động

### 1. Khởi tạo trang:
```javascript
$(document).ready(function() {
    loadComboData(); // Load thiết lập website
});
```

### 2. Sau khi load thiết lập:
```javascript
function loadComboData() {
    // ... load dữ liệu
    success: function(response) {
        comboData = response.data;
        populateComboBoxes();
        
        // Khởi tạo bảng với cột động
        initializeTable();
        setupTableEvents();
        LoadDataTable();
        
        // Ẩn/hiện thành phần kiểm duyệt
        toggleKiemDuyetElements();
    }
}
```

### 3. Khi mở form thêm/sửa:
```javascript
// Kiểm tra thiết lập và ẩn/hiện toggle
const yeuCauKiemDuyet = comboData?.thietLapWebsite?.ck_YeuCauKiemDuyet || false;
if (yeuCauKiemDuyet) {
    $(".kiem-duyet-toggle").show();
} else {
    $(".kiem-duyet-toggle").hide();
}
```

## Các Class CSS Được Sử Dụng

- **`.kiem-duyet-element`**: Cho bộ lọc trạng thái kiểm duyệt
- **`.kiem-duyet-toggle`**: Cho toggle "Đã kiểm duyệt" trong form

## Kết Quả

### Trường hợp 1: `ck_YeuCauKiemDuyet = false`
- ❌ Không hiển thị cột "Đã kiểm duyệt" trong bảng
- ❌ Không hiển thị toggle "Đã kiểm duyệt" trong form
- ❌ Không hiển thị bộ lọc kiểm duyệt
- ✅ Giao diện gọn gàng, không có thành phần kiểm duyệt

### Trường hợp 2: `ck_YeuCauKiemDuyet = true`
- ✅ Hiển thị cột "Đã kiểm duyệt" trong bảng với trạng thái màu sắc
- ✅ Hiển thị toggle "Đã kiểm duyệt" trong form
- ✅ Hiển thị bộ lọc kiểm duyệt trong tìm kiếm nâng cao
- ✅ Đầy đủ tính năng quản lý kiểm duyệt

## Lưu Ý Quan Trọng

1. **Tự động ẩn/hiện:** Không cần reload trang, tự động ẩn/hiện dựa trên thiết lập
2. **Tương thích ngược:** Hoạt động với dữ liệu cũ
3. **Linh hoạt:** Admin có thể bật/tắt kiểm duyệt bất kỳ lúc nào
4. **Hiệu suất:** Chỉ load và hiển thị những gì cần thiết

Hệ thống đã được tối ưu để chỉ hiển thị các thành phần kiểm duyệt khi thực sự cần thiết!
