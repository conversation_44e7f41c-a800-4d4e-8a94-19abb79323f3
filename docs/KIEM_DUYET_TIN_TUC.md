# Hệ Thống <PERSON>ể<PERSON> Du<PERSON>t Tin Tức

## Tổng <PERSON>uan

Hệ thống kiểm duyệt tin tức cho phép quản trị viên kiểm soát việc hiển thị tin tức trên cổng thông tin dựa trên thiết lập của website.

## Các Trường Kiểm Duyệt Mới

### Model TinTuc
- `TrangThaiKiemDuyet`: Trạng thái kiểm duyệt (0: Ch<PERSON> duy<PERSON>, 1: <PERSON><PERSON> du<PERSON>, 2: Từ chối)
- `UserID_NguoiKiemDuyet`: ID người kiểm duyệt
- `NguoiKiemDuyet`: Tên người kiểm duyệt
- `NgayKiemDuyet`: Ng<PERSON>y giờ kiểm duyệt
- `GhiChuKiemDuyet`: <PERSON>hi chú khi kiểm duyệt
- `YeuCauKiemDuyet`: <PERSON><PERSON> yêu cầu kiểm duyệt không
- `TuDongDuyet`: <PERSON><PERSON> tự động duyệt không

### Model ThietLapWebsite
- `ck_YeuCauKiemDuyet`: Bật/tắt yêu cầu kiểm duyệt
- `ck_TuDongDuyetBai`: Bật/tắt tự động duyệt bài

## Logic Kiểm Duyệt

### Khi Tạo Tin Tức Mới

1. **Nếu `ck_YeuCauKiemDuyet = false`**:
   - Tin tức không cần kiểm duyệt
   - Tự động đặt `TrangThaiKiemDuyet = 1` (Đã duyệt)
   - Hiển thị ngay lập tức

2. **Nếu `ck_YeuCauKiemDuyet = true`**:
   - **Và `ck_TuDongDuyetBai = true`**: Tự động duyệt, hiển thị ngay
   - **Và `ck_TuDongDuyetBai = false`**: Chờ duyệt thủ công

### Hiển Thị Tin Tức

Tin tức chỉ hiển thị trên cổng thông tin khi:
- `TrangThaiKiemDuyet = 1` (Đã duyệt), HOẶC
- `YeuCauKiemDuyet = false` (Không yêu cầu kiểm duyệt), HOẶC
- `TuDongDuyet = true` (Tự động duyệt)

## Các Scope Mới

```php
// Lấy tin tức đã duyệt
TinTuc::daDuyet()->get();

// Lấy tin tức chờ duyệt
TinTuc::choDuyet()->get();

// Lấy tin tức bị từ chối
TinTuc::tuChoi()->get();

// Lấy tin tức có thể hiển thị
TinTuc::coTheHienThi()->get();
```

## Các Method Mới

```php
// Duyệt tin tức
$tinTuc->duyetTinTuc('Tên người duyệt', $userID, 'Ghi chú');

// Từ chối tin tức
$tinTuc->tuChoiTinTuc('Tên người duyệt', $userID, 'Lý do từ chối');

// Kiểm tra có thể hiển thị
$tinTuc->coTheHienThi();
```

## Accessor Mới

```php
// Tên trạng thái kiểm duyệt
$tinTuc->ten_trang_thai_kiem_duyet; // "Chờ duyệt", "Đã duyệt", "Từ chối"

// Màu trạng thái (Bootstrap)
$tinTuc->mau_trang_thai_kiem_duyet; // "warning", "success", "danger"

// Ngày kiểm duyệt đã format
$tinTuc->formatted_ngay_kiem_duyet; // "04/07/2024 14:30"
```

## Controller Kiểm Duyệt

### KiemDuyetTinTucController

- `index()`: Danh sách tin tức cần kiểm duyệt
- `duyet($id)`: Duyệt tin tức
- `tuChoi($id)`: Từ chối tin tức
- `show($id)`: Xem chi tiết tin tức
- `capNhatThietLap()`: Cập nhật thiết lập kiểm duyệt
- `thongKe()`: Thống kê kiểm duyệt

## Migration

Chạy migration để thêm các trường mới:

```bash
php artisan migrate
```

Migration sẽ tự động:
- Thêm các trường kiểm duyệt vào collection `tin_tuc`
- Đặt tất cả tin tức hiện có thành "Đã duyệt"

## Routes

Thêm vào `web.php`:

```php
require __DIR__.'/kiem-duyet.php';
```

## Cách Sử Dụng

### 1. Thiết Lập Kiểm Duyệt

Cập nhật trong bảng `thiet_lap_website`:
```php
$thietLap = ThietLapWebsite::current();
$thietLap->update([
    'ck_YeuCauKiemDuyet' => true,  // Bật kiểm duyệt
    'ck_TuDongDuyetBai' => false   // Tắt tự động duyệt
]);
```

### 2. Tạo Tin Tức

Khi tạo tin tức mới, hệ thống sẽ tự động thiết lập trạng thái kiểm duyệt dựa trên thiết lập website.

### 3. Kiểm Duyệt Tin Tức

```php
// Duyệt tin tức
$tinTuc = TinTuc::find($id);
$tinTuc->duyetTinTuc('Admin', auth()->id(), 'Nội dung phù hợp');

// Từ chối tin tức
$tinTuc->tuChoiTinTuc('Admin', auth()->id(), 'Nội dung không phù hợp');
```

### 4. Hiển Thị Tin Tức

Tất cả controller hiển thị tin tức đã được cập nhật để sử dụng scope `coTheHienThi()`.

## Lưu Ý

- Tin tức cũ sẽ được tự động đặt thành "Đã duyệt" khi chạy migration
- Hệ thống tương thích ngược với tin tức hiện có
- Có thể bật/tắt kiểm duyệt bất cứ lúc nào thông qua thiết lập website
