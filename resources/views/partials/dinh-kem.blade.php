@php
// nếu không đ<PERSON><PERSON><PERSON> t<PERSON><PERSON>ền vào, mặc định hiển thị
$ChapNhanFile = $ChapNhanFile ?? [
"jpg", "jpeg", "png",
"pdf", "doc", "docx", "rar", "zip"
];
$accept = '.' . implode(',.', $ChapNhanFile);

$id = $id ?? 'dinhKemFile';
@endphp
<style>
.drop-area.dragover,
.drop-area.dragover * {
    background: #e3f2fd !important;
    border-color: #5a8dee !important;
    transition: background 0.2s, border 0.2s;
}

.file-preview .btn-close {
    color: #e53935 !important;
    opacity: 1 !important;
    z-index: 2;
    padding: 0;
    transition: background 0.15s, color 0.15s, border 0.15s;
}

.file-preview .btn-close:hover,
.file-preview .btn-close:focus {
    color: #c62828 !important;
    outline: none;
}
</style>
<div>
    <label class="form-label mb-2"><PERSON><PERSON><PERSON> kèm</label>
    <div id="drop-area" class="drop-area d-flex align-items-center p-3 rounded shadow-sm"
        style="background: #f5f8fa; gap: 16px; border: 1px dashed #b3c0ce; cursor: pointer; transition: box-shadow 0.2s;">
        <div class="me-3 text-center d-flex flex-column align-items-center" style="min-width:50px;">
            <i class="fa fa-cloud-upload" style="font-size: 36px; color: #5a8dee;"></i>
        </div>
        <div class="flex-grow-1">
            <div style="font-size: 15px; color: #444; font-weight: 500;">Kéo thả
                file vào đây hoặc chọn file từ thiết bị</div>
            <div id="{{$id}}_fileHelp" style="font-size: 13px; color: #888;">
                <span class="fw-bold">Hỗ trợ:</span>
                {{ implode(', ', $ChapNhanFile) }}. Tối đa 10MB
            </div>
        </div>
        <div class="d-flex flex-column gap-2">
            <button id="{{$id}}_btnChonTepVB_us" type="button"
                class="btn btn-primary btn-sm d-flex align-items-center justify-content-center mb-1"
                style="min-width:110px;">
                <i class="fa fa-upload me-2"></i> Chọn file
            </button>
            <button id="{{$id}}_btnXoaHetTepVB_us" type="button"
                class="btn btn-outline-danger btn-sm d-flex align-items-center justify-content-center"
                style="min-width:110px;">
                <i class="fa fa-trash-o me-2"></i> Xóa tất cả
            </button>
        </div>
    </div>
    <input type="hidden" id="{{$id}}" value="">
    <input type="file" id="{{$id}}_fileVB_us" style="display: none;" accept="{{ $accept }}" multiple
        aria-describedby="{{$id}}_fileHelp">
    <div id="{{$id}}_list-file" class="mt-2"></div>
</div>

<script>
var listDinhKem;

let uploadedFileUrls = [];

$("#{{$id}}_btnChonTepVB_us").on("click", function() {
    // …forward the click to the hidden file input
    $("#{{$id}}_fileVB_us").trigger("click");
});

$("#{{$id}}_fileVB_us").on("change", async function() {
    const files = Array.from(this.files);
    $("#{{$id}}_list-file").empty();

    for (const file of files) {
        const form = new FormData();
        form.append("file", file);

        try {
            const res = await NTS.getAjaxAPIAsync(
                "POST",
                "/api/dungchung/files/upload",
                form,
            );
            if (!res.success) {
                NTS.loi(`Tải lên thất bại: ${file.name}`);
                continue;
            }
            // collect the returned URLs array (or wrap single URL)
            const url = Array.isArray(res.url) ? res.url[0] : res.url;
            uploadedFileUrls.push(url);
        } catch (err) {
            console.error("Upload error", err);
            NTS.loi(`Lỗi upload ${file.name}`);
        } finally {
            updateBtnXoaHet();
        }
    }

    // 2) Render previews by iterating uploadedFileUrls
    uploadedFileUrls.forEach((url) => {
        renderAttachment(url);
    });

    const ttList = [].slice.call(
        document.querySelectorAll('[data-bs-toggle="tooltip"]')
    );
    ttList.forEach((el) => new bootstrap.Tooltip(el));

    $("#{{$id}}").val(uploadedFileUrls.join("|"));
});

$("#{{$id}}_btnXoaHetTepVB_us").on("click", async function() {
    CanhBaoXoa(async () => {
        $("#{{$id}}_fileVB_us").val("");
        $("#{{$id}}_list-file").empty();
        $("#{{$id}}").val("");

        try {
            // call delete-multiple with our URL array
            if (uploadedFileUrls.length !== 0) {
                const res = await NTS.getAjaxAPIAsync(
                    "DELETE",
                    "/api/dungchung/files/delete-multiple", {
                        urls: uploadedFileUrls
                    },
                );
                if (!res.success) {
                    return NTS.loi("Xóa tất cả thất bại: " + (res.message || ""));
                }
                if (res.loi) {
                    return NTS.loi("Xóa tất cả thất bại: " + (res.message || ""));
                }

                NTS.thanhcong("Xoá tất cả đính kèm thành công");
            }
        } catch (err) {
            console.error("Delete all error", err);
            NTS.loi("Có lỗi khi xóa tất cả đính kèm");
        } finally {
            uploadedFileUrls = []; // reset URL store
            updateBtnXoaHet();
        }
    });
});

function renderAttachment(url) {
    const filename = url.split("/").pop();

    // 2) get the extension (e.g. "jpg")
    const ext = filename.split(".").pop().toLowerCase();

    // 3) decide if it’s an image
    const imageExts = ["jpg", "jpeg", "png", "gif", "bmp", "webp"];
    const isImage = imageExts.includes(ext);

    const $item = $(`
            <div class="file-preview position-relative d-inline-block text-center me-2 mb-2">
            <!-- delete button -->
                <button type="button" class="{{$id}}_btn-close btn-close position-absolute top-0 end-0 m-1" aria-label="Delete"></button>
            </div>
              `);

    // image vs icon
    let $thumb;
    if (isImage) {
        $thumb = $(`
            <img class="img-thumbnail" 
                 style="width:100px;height:100px;object-fit:cover;"
                 src="${url}">
          `);
    } else {
        $thumb = $(`
            <div class="file-icon bg-secondary text-white rounded
                        d-flex align-items-center justify-content-center mb-1"
                 style="width:100px;height:100px;font-size:2rem;">
              <i class="fa fa-file"></i>
            </div>
          `);
    }

    // 1) native tooltip
    $thumb.attr("title", filename);

    // 2) (optional) Bootstrap tooltip
    $thumb.attr("data-bs-toggle", "tooltip").attr("data-bs-placement", "top");

    // assemble
    $item.append($thumb);
    $item.append(
        $(
            '<a target="_blank" class="d-block small text-truncate" style="max-width:100px;"></a>'
        )
        .attr("href", url)
        .text(filename)
    );
    $("#{{$id}}_list-file").append($item);

    $item.find(".{{$id}}_btn-close").on("click", async () => {
        CanhBaoXoa(async () => {
            try {
                // call delete-multiple with a JSON array of this single URL
                NTS.getAjaxAPIAsync(
                    "DELETE",
                    "/api/dungchung/files/delete-multiple", {
                        urls: [url]
                    }
                ).then(res => {
                    if (!res.success) {
                        return NTS.loi("Xóa file thất bại: " + filename);
                    }

                    uploadedFileUrls = uploadedFileUrls.filter(u => u !== url);
                    $("#{{$id}}").val(uploadedFileUrls.join("|"));
                    $item.remove();
                }).catch(err => {
                    console.error("Error deleting file:", err);
                    NTS.loi("Lỗi khi xóa file.");
                });
            } catch (err) {
                console.error("Delete error", err);
                NTS.loi("Lỗi khi xóa " + filename);
            } finally {
                updateBtnXoaHet();
            }
        });

    });
}

function resetFileInput() {
    // Clear the file input
    $("#{{$id}}_fileVB_us").val("");
    // Clear the preview container
    $("#{{$id}}_list-file").empty();
    // Clear the hidden input value
    $("#{{$id}}").val("");
    // Clear the uploaded URLs array
    uploadedFileUrls = [];
}
(function() {
    // Set your IDs as variables for easy reference
    var $dropArea = $("#drop-area");
    var $fileInput = $("#{{$id}}_fileVB_us");

    // Prevent default drag behaviors on the whole window (to avoid browser opening files)
    $(document).on('dragover drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
    });

    // Highlight drop area when file is dragged over
    $dropArea.on('dragenter dragover', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $dropArea.addClass('dragover');
    });

    // Remove highlight when drag leaves or file is dropped
    $dropArea.on('dragleave dragend drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $dropArea.removeClass('dragover');
    });

    // Handle file drop
    $dropArea.on('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $dropArea.removeClass('dragover');

        var dt = e.originalEvent.dataTransfer;
        if (dt && dt.files && dt.files.length) {
            $fileInput[0].files = dt.files;

            // Trigger change event for the input so your upload logic runs
            $fileInput.trigger('change');
        }
    });

    // Also trigger click to file input on drop area click (for extra usability)
    $dropArea.on('click', function(e) {
        if (e.target === this) { // Only if background clicked, not on buttons
            $fileInput.trigger('click');
        }
    });
})();

// JS:
const $btnXoaHet = $("#dinhKemQD_btnXoaHetTepVB_us");

// Hàm cập nhật trạng thái nút
function updateBtnXoaHet() {
    const $hidden = $("#{{$id}}");
    // Nếu có file thì show, không thì ẩn
    const urls = $hidden.val()
        .split('|')
        .map(u => u.trim())
        .filter(u => u !== '');

    // Nếu không có URL nào thì ẩn, ngược lại show
    if (urls.length === 0) {
        console.log("ẩn")
        // nếu không có file thì ẩn
        $btnXoaHet.addClass("d-none");
    } else {
        console.log("hiện")

        // ngược lại hiện
        $btnXoaHet.removeClass("d-none");
    }
}

$(document).on('change', "#{{$id}}", function() {
    console.log("hiện")
    updateBtnXoaHet();
})
// 1) Khi khởi tạo trang, chạy 1 lần
updateBtnXoaHet();


// 4) Khi bấm Xóa tất cả
</script>