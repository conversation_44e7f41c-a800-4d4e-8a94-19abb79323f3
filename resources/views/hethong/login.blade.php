<!DOCTYPE html>
<html lang="vi">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta charset="utf-8" />
    <title>Quản lý văn bản - QLVB</title>
    <meta name="description" content="Quản lý văn bản - QLVB" />
    <link rel="icon" href="{{ asset('img/logo-1.png') }}" type="image/x-icon" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <link href="{{ asset('css/tabler/tabler.min.css') }}" rel="stylesheet" />

    <script src="{{ asset('js/libs/bootstrap/dist/js/bootstrap.bundle.min.js') }}?v={{ time() }}"></script>

    <link href="{{ asset('js/libs/font-awesome/4.7.0/css/font-awesome.css') }}" rel="stylesheet" />
    <link href="{{ asset('js/libs/select2/select2.min.css') }}" rel="stylesheet" />

    <script src="{{ asset('js/libs/jquery/jquery-3.6.3.min.js') }}?v={{ time() }}"></script>

    <style>
    .input-icon>.ace-icon {
        font-size: 14px;
    }

    .ntsloadding {
        position: fixed;
        left: 0px;
        top: 0px;
        width: 100%;
        height: 100%;
        z-index: 9999;
        opacity: 0.5;
        text-align: center;
        background: #CCC;
        filter: alpha(opacity=80);
    }

    .message-loading-overlay>.ace-icon {
        position: absolute;
        top: 40%;
        left: 49%;
        right: 0;
        text-align: center;
    }

    .login-container {
        width: 420px;
        margin: 0 auto;
    }

    .input-icon>input {
        padding-left: 5px !important;
        padding-right: 6px !important;
    }

    .main-container {
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        min-height: 100vh;
    }


    @import url("https://fonts.googleapis.com/css?family=Montserrat:400,800");

    * {
        box-sizing: border-box;
    }

    body {
        font-family: Verdana;
        font-weight: 300;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #3f3f3f;
        background: url('{{ asset('img/login_background.png') }}');
        background-size: cover;
        background-position: center;
    }

    h1 {
        font-weight: 300;
    }




    form {
        display: flex;
        /* align-items: center; */
        justify-content: center;
        flex-direction: column;
        padding: 0 35px;
        height: 100%;
        text-align: center;
        width: 100%;
    }

    form img {
        align-items: center;
    }

    button {
        border-radius: 6px;
        padding: 12px 24px;
        background: #ffffff14;
        border: 1px solid #fff;
        color: #fff;
        cursor: pointer;
        outline: none;
        font-size: 13px;
        text-transform: uppercase;
        font-weight: 300;
        transition: transform 80ms ease-in;
    }

    button:hover {
        background: #fff;
        color: #5bb430;
    }

    button:active {
        transform: scale(0.95);
    }





    input::placeholder {
        color: #a0a0a0;
    }

    input[type="password"]::placeholder {
        color: #a0a0a0;
    }

    input[type="text"]::placeholder {
        color: #a0a0a0;
    }

    .input-group .input-group-text,
    .input-group .form-control {
        background-color: white;
    }

    .container {
        margin-top: 5%;
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
        position: relative;
        overflow: hidden;
        width: 920px;
        max-width: 100%;
        min-height: 600px;
    }

    .form-container {
        position: absolute;
        top: 0;
        height: 100%;
        transition: all 0.6s ease-in-out;
    }

    .form-container.sign-in-container {
        left: 0;
        width: 55%;
        z-index: 2;
    }

    .form-container.sign-up-container {
        left: 0;
        width: 50%;
        opacity: 0;
        z-index: 1;
    }

    .form-container button,
    .form-container a {
        background: #5bb430;
        border: 1px solid #5bb430;
        color: #fff;
    }

    .form-container a {
        margin-top: 8px;
        text-decoration: none;
        cursor: pointer;
        border-radius: 6px;
        padding: 12px 24px;
        cursor: pointer;
        outline: none;
        font-size: 13px;
        text-transform: uppercase;
        font-weight: 300;
        transition: transform 80ms ease-in;
        font-family: inherit;
        font-size: inherit;
        line-height: 15px;
        height: 40px;
        width: 100%;
    }

    .form-container button:hover {
        background: transparent;
        color: #5bb430;
    }

    .form-container a:hover {
        background: transparent;
        color: #5bb430;
    }

    .container.right-panel-active .sign-in-container {
        transform: translateX(66.5%);
    }

    .container.right-panel-active .sign-up-container {
        transform: translateX(66.5%);
        opacity: 1;
        z-index: 5;
        animation: show 0.6s;
    }

    .overlay-container {
        position: absolute;
        top: 0;
        left: 55%;
        width: 45%;
        height: 100%;
        overflow: hidden;
        transition: transform 0.6s ease-in-out;
        z-index: 100;
    }

    .overlay-container .overlay {
        background: linear-gradient(-45deg, #58ae27, #97de81, #5b8b4d);
        background-repeat: no-repeat;
        background-size: cover;
        background-position: 0 0;
        color: #ffffff;
        position: relative;
        left: -100%;
        left: -150%;
        height: 100%;
        width: 250%;
        transform: translateX(0);
        transition: transform 0.6s ease-in-out;
    }

    .overlay-container .overlay .overlay-panel {
        position: absolute;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        padding: 0 30px;
        text-align: center;
        top: 0;
        height: 100%;
        width: 40%;
        transform: translateX(0);
        transition: transform 0.6s ease-in-out;
    }

    .overlay-container .overlay .overlay-panel.overlay-left {
        transform: translateX(-20%);
    }

    .overlay-container .overlay .overlay-panel.overlay-right {
        right: 0;
        transform: translateX(0);
    }

    .container.right-panel-active .overlay-container {
        transform: translateX(-150%);
    }

    .container.right-panel-active .overlay-container .overlay {
        transform: translateX(60%);
    }

    .container.right-panel-active .overlay-container .overlay .overlay-left {
        transform: translateX(0);
    }

    .container.right-panel-active .overlay-container .overlay .overlay-right {
        transform: translateX(20%);
    }


    input[type="password"],
    input[type="text"] {
        background: white;
        padding: 5px 10px;
        font-size: 12px;
        width: 100%;
    }

    @keyframes show {

        0%,
        39.99% {
            opacity: 0;
            z-index: 1;
        }

        40%,
        100% {
            opacity: 1;
            z-index: 5;
        }
    }

    @keyframes square {
        0% {
            -webkit-transform: translateY(0);
            transform: translateY(0);
        }

        100% {
            -webkit-transform: translateY(-700px) rotate(600deg);
            transform: translateY(-700px) rotate(600deg);
        }
    }



    input:focus {
        border: unset !important;
    }

    @media (min-width: 768px) {
        .d-md-flex {
            display: -ms-flexbox !important;
            display: flex !important;
        }
    }

    @media (min-width: 768px) {
        .order-md-last {
            -webkit-box-ordinal-group: 14;
            -ms-flex-order: 13;
            order: 13;
        }
    }

    @media (max-width: 768px) {

        .text-wrap,
        .login-wrap {
            width: 100% !important;
        }

        .text-wrap-h {
            height: 100px !important;
        }

        .overlay-container {
            left: 0%;
            width: 250%;
        }

        .overlay-container .overlay {
            left: 0%;
            width: 100%;
        }

        .order-md-last>p {
            display: none !important;
        }

        .text-wrap>button {
            display: none !important;
        }

        .social-login {
            display: none !important;
        }

        #signInVer2 {
            display: block !important;
        }

        form {
            padding: 0 15px !important;
            margin-top: 40px !important;
        }

        h3 {
            font-size: 18px !important;
            margin-top: 60px !important;
        }

        .container.right-panel-active .overlay-container {
            transform: translateX(-60%);
        }

        .text-wrap-re {
            width: 40% !important;
        }

        .overlay-container .overlay .overlay-panel.overlay-left {
            transform: translateX(0%);
        }

        .container.right-panel-active .sign-up-container {
            transform: translateX(0%) !important;
        }

        .formVer2 {
            margin-top: 0px !important;
        }

        .container {
            width: 360px;
        }
    }

    .text-wrap,
    .login-wrap {
        width: 60%;
    }



    input[type=text]:focus-visible,
    input[type=password]:focus-visible {
        border: 1px solid #f77c13 !important;
    }

    .slide img {
        opacity: 1;
        transition: opacity 0.5s ease-in-out;
    }

    .slide img.loaded {
        opacity: 1;
    }


    .slider {
        position: relative;
        width: 100%;
        height: 100%;
        max-width: 100%;
        overflow: hidden;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .slides {
        display: flex;
        transition: transform 0.5s ease-in-out;
        height: 100%;
    }

    .slide {
        min-width: 100%;
        height: 100%;
    }

    .slide img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .navigation {
        position: absolute;
        top: 50%;
        width: 100%;
        display: flex;
        justify-content: space-between;
        transform: translateY(-50%);
    }

    .navigation button {
        background-color: rgba(0, 0, 0, 0.5);
        border: none;
        color: white;
        padding: 10px;
        cursor: pointer;
        border-radius: 50%;
        font-size: 18px;
        transition: background-color 0.3s;
    }

    .navigation button:hover {
        background-color: rgba(0, 0, 0, 0.8);
    }

    .dots {
        position: absolute;
        bottom: 15px;
        width: 100%;
        display: flex;
        justify-content: center;
        gap: 10px;
    }

    .dot {
        width: 10px;
        height: 10px;
        background-color: rgba(255, 255, 255, 0.5);
        border-radius: 50%;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    .dot.active {
        background-color: white;
    }

    .checkbox-small {
        transform: scale(0.85);
        transform-origin: top left;
    }

    .select2-container .select2-selection--single {
        height: 2.5rem !important;
        /* same as .form-control */
        padding: 6px 16px;

        /* match Bootstrap’s vertical padding */
        border-radius: 0.375rem;
        /* match Bootstrap’s border radius */
        border: 1px solid #ced4da;
        box-sizing: border-box;
    }

    /* Vertically center the selected text */
    .select2-container .select2-selection__rendered {
        height: 2.5rem !important;

        line-height: 2.5rem;
    }

    /* Align the dropdown arrow */
    .select2-container .select2-selection__arrow {
        height: 2.5rem !important;
        line-height: 2.5rem;
        right: 0.75rem;
        padding: 13px 16px;
        /* match padding on the right */
    }

    html {
        height: 100vh;
    }

    body {
        height: 100vh;
    }


    .login-title {
        color: #f77c13;
        font-size: 1.75rem;
        font-weight: 700;
        text-align: center;
        letter-spacing: 1px;
    }

    .form-control:focus {
        box-shadow: 0 0 0 2px #ffb88533;
        border-color: #ffb885;
    }

    .btn-login {
        background: #f77c13;
        color: #fff;
        font-weight: 600;
        font-size: 1rem;
        border-radius: 8px;
        border: none;
        margin-top: 8px;
        transition: all 0.3s ease;
    }

    .btn-login:hover {
        background: rgb(255, 185, 9);
        /* richer orange */
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        transform: translateY(-1px);
        color: #ffe8d6;
        /* softer off-white text */
    }


    .form-check-input:checked {
        background-color: #f77c13;
        border-color: #f77c13;
    }

    .forgot-link {
        color: #f77c13 !important;
        font-size: 0.97rem;
        text-decoration: none;
    }

    .forgot-link:hover {
        text-decoration: underline;
    }

    .input-group-text {
        width: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0;
        background: transparent;

    }

    .select2-container .select2-selection--single,
    .select2-container--default .select2-selection--single {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;

        height: calc(1.5em + .75rem + 2px) !important;
        /* same as .form-control */
        padding: .375rem .75rem;
        font-size: 1rem;
        line-height: 1.5;
        border: 1px solid #ced4da;
        border-radius: .25rem;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 1.5;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: calc(1.5em + .75rem + 2px);
    }

    .login-box {
        padding-bottom: 30px;
        border-radius: 16px;
        /* padding: 0px 28px; */
    }

    .login-box .form-control,
    .login-box .form-select,
    .login-box .input-group-text {
        background-color: #fff;
        color: #000;
    }
    </style>
</head>

<body>
    <!-- <div class="container-fluid width-100 min-vh-100 d-flex align-items-center justify-content-center"> -->
    <div class="row w-100">
        <div class="col-10 offset-2 row px-0">
            <div class="offset-1 col-lg-4 d-lg-flex align-items-center justify-content-center position-relative p-0 rounded-start-4"
                style="background: #0047AB; color: #fff;">
                <div class="text-center px-4">
                    <img src="your-illustration.png" alt="Illustration" class="img-fluid mb-4"
                        style="max-width: 300px;">
                    <h3>NTSOFT VBCC</h3>
                    <h2>PHẦN MỀM QUẢN LÝ VĂN BẰNG, CHỨNG CHỈ</h2>
                </div>
            </div>
            <!-- Left: Login form -->
            <div class="col-lg-4 col-12 d-flex flex-column justify-content-top pt-4 rounded-end-4"
                style="background-color: #fff; height:auto">
                <div class="text-center">
                    <img src="{{ asset('img/logo-no_text.png') }}" style="width: 125px;">
                </div>
                <div class="login-box">
                    <form id="loginForm" method="POST" action="{{ url('login') }}" class=" pb-2">
                        <div class="login-title mb-2">Đăng nhập</div>
                        @csrf
                        <div class="mb-3">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fa fa-envelope text-warning"></i></span>
                                <input type="text" class="form-control @error('username') is-invalid @enderror"
                                    id="username" name="username" value="{{ old('username') }}" required
                                    placeholder="Tên đăng nhập">
                                @error('username')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fa fa-lock text-warning"></i></span>
                                <input type="password" class="form-control @error('password') is-invalid @enderror"
                                    id="password" name="password" required placeholder="Mật khẩu">
                                <span class="input-group-text" style="cursor:pointer" onclick="togglePassword()">
                                    <i class="fa fa-eye-slash text-muted" id="togglePassIcon"></i>
                                </span>
                                @error('password')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <!-- NienDo (put where fits best, below password for now) -->

                        <div class="mb-3">
                            <div class="input-group">
                                <span class="input-group-text text-warning">
                                    <i class="fa fa-calendar"></i>
                                </span>
                                <input type="number" class="form-control" id="NienDo" name="NienDo" min="2010"
                                    max="2030" value="{{ old('NienDo', date('Y')) }}" placeholder="Năm">
                            </div>
                            <!-- <div class="input-group">
                                <span class="input-group-text text-warning">
                                    <i class="fa fa-calendar"></i>
                                </span>
                                <select id="NienDo" name="NienDo" class="form-select">
                                    <option></option>
                                    <option value="2010">2010</option>
                                    …
                                    <option value="2030">2030</option>
                                </select>
                            </div> -->
                        </div>

                        @if (true)
                        <div class="mb-2 d-flex justify-content-center">
                            {!! NoCaptcha::display() !!}
                            @error('g-recaptcha-response')
                            <div class="invalid-feedback d-block text-center">{{ $message }}</div>
                            @enderror
                        </div>
                        @endif

                        <div class="d-flex mb-2">
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" id="remember" name="remember"
                                    {{ old('remember') ? 'checked' : '' }}>
                                <label class="form-check-label text-muted" for="remember">
                                    Ghi nhớ đăng nhập
                                </label>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-login mt- w-100 py-2">Đăng nhập</button>
                    </form>

                    <div class="text-center" style="">
                        <small class="text-muted">
                            PHẦN MỀM QUẢN LÝ VĂN BẰNG, CHỨNG CHỈ
                        </small>
                        <span class="fw-bold">
                            NTSOFT VBCC
                        </span>
                    </div>
                </div>


            </div>

        </div>

    </div>
    <!-- </div> -->

    <script>
    window.Laravel = window.Laravel || {};
    window.Laravel = {
        ...window.Laravel,
        comboNam: "{{ route('dungchung.comboNam') }}",
    };

    function togglePassword() {
        const pwd = document.getElementById('password');
        const icon = document.getElementById('togglePassIcon');
        if (pwd.type === 'password') {
            pwd.type = 'text';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        } else {
            pwd.type = 'password';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        }
    }
    </script>
    <script src="{{ asset('js/dungchung/customs.js') }}?v={{ time() }}"></script>

    <!-- Libs -->
    <script src="{{ asset('js/libs/select2/select2.full.min.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/libs/jquery/jquery-ui.min.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/libs/bootstrap/dist/js/bootstrap.bundle.min.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/dungchung/NTSPlugin.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/dungchung/NTSLibrary.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/dungchung/NTSValidate.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/libs/confirm/js/jquery-confirm.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/libs/confirm/dist/jquery-confirm.min.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/libs/moment/moment.min.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/libs/datepicker/bootstrap-datetimepicker.min.js') }}?v={{ time() }}"></script>
    {!! NoCaptcha::renderJs() !!}

    <!-- Script -->
    <script src="{{ asset('js/login.js') }}"></script>

</body>

</html>