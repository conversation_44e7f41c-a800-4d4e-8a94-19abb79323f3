@extends('layouts.layouts')
@section('title', '<PERSON><PERSON><PERSON><PERSON> lập hệ thống')
@section('content')
    <div class="row flex-row-reverse mb-2">
        <div class="col mb-1" style="margin-bottom: 0 !important">
            <div class="text-end">
                <!-- <button type="button" class="btn btn-primary btn-nts-them" id="btnLuuVaDong"
                        onclick="btnLuuDuLieu();return false;"><i class="fa fa-save"></i>&ensp;Lưu (F9)</button>
                </div> -->
            </div>
            <div class="col-sm-4 col-xs-12">
            </div>
        </div>
        <div class="row">
            <div class="card" style="box-shadow: unset;">
                <div class="card-header" id="user-profile-2">
                    <ul class="nav nav-tabs card-header-tabs" data-bs-toggle="tabs" role="tablist"
                        style="padding-left: 10px; padding-bottom: 5px;">
                        <li class="nav-item" role="presentation" style="float: left;">
                            <a href="#Tab1_View" id="btnTab1_View" class="nav-link active" data-bs-toggle="tab"
                                aria-selected="false" role="tab" tabindex="-1">
                                <svg class="icon icon-tabler icon-tabler-vocabulary" width="24" height="24"
                                    viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path
                                        d="M10 19h-6a1 1 0 0 1 -1 -1v-14a1 1 0 0 1 1 -1h6a2 2 0 0 1 2 2a2 2 0 0 1 2 -2h6a1 1 0 0 1 1 1v14a1 1 0 0 1 -1 1h-6a2 2 0 0 0 -2 2a2 2 0 0 0 -2 -2z">
                                    </path>
                                    <path d="M12 5v16"></path>
                                    <path d="M7 7h1"></path>
                                    <path d="M7 11h1"></path>
                                    <path d="M16 7h1"></path>
                                    <path d="M16 11h1"></path>
                                    <path d="M16 15h1"></path>
                                </svg>
                                &nbsp; Thông tin chung
                            </a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a href="#Tab2_View" id="btnTab2_View" class="nav-link" data-bs-toggle="tab"
                                aria-selected="false" role="tab" tabindex="-1">
                                <svg class="icon icon-tabler icon-tabler-vocabulary" width="24" height="24"
                                    viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path
                                        d="M10 19h-6a1 1 0 0 1 -1 -1v-14a1 1 0 0 1 1 -1h6a2 2 0 0 1 2 2a2 2 0 0 1 2 -2h6a1 1 0 0 1 1 1v14a1 1 0 0 1 -1 1h-6a2 2 0 0 0 -2 2a2 2 0 0 0 -2 -2z">
                                    </path>
                                    <path d="M12 5v16"></path>
                                    <path d="M7 7h1"></path>
                                    <path d="M7 11h1"></path>
                                    <path d="M16 7h1"></path>
                                    <path d="M16 11h1"></path>
                                    <path d="M16 15h1"></path>
                                </svg>
                                &nbsp; Cấu hình hệ thống
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content">
                        <div class="tab-pane fade show active" id="Tab1_View" role="tabpanel"
                            aria-labelledby="btnTab1_View">
                            <div class="row">
                                <div class="col-md-12">
                                    <fieldset class="KhungVien mt-2">
                                        <legend class="bg-nts-header">1. Thiết lập đơn vị báo cáo</legend>
                                        <div class="widget-body rounded-10">
                                            <div class="widget-main rounded-10">
                                                <div class="row mb-1">
                                                    <div class="col-md-12">
                                                        <label class="form-label" for="DonViCapTren">Đơn vị cấp trên</label>
                                                        <input class="form-control input-sm" type="text" id="DonViCapTren"
                                                            placeholder="Nhập tên đơn vị" />
                                                    </div>
                                                </div>
                                                <div class="row mb-1">
                                                    <div class="col-md-12">
                                                        <label class="form-label" for="DonViBaoCao">Đơn vị báo cáo</label>
                                                        <input class="form-control input-sm" type="text" id="DonViBaoCao"
                                                            placeholder="Nhập tên đơn vị" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </fieldset>
                                    <fieldset class="KhungVien  mt-2">
                                        <legend class="bg-nts-hea">2. Thiết lập ký tên báo cáo</legend>
                                        <div class="widget-body rounded-10">
                                            <div class="row">
                                                <div class="col-lg-6">
                                                    <div class="mb-1">
                                                        <label class="form-label" for="KyThay">Ký thay</label>
                                                        <input type="text" class="form-control" id="KyThay"
                                                            placeholder="Ký thay">
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-lg-6">
                                                    <div class="mb-1">
                                                        <label class="form-label" for="NguoiKT">Họ tên người ký thay</label>
                                                        <input type="text" class="form-control" id="NguoiKT"
                                                            placeholder="Nhập họ tên người ký thay">
                                                    </div>
                                                </div>
                                                <div class="col-lg-6">
                                                    <div class="mb-1">
                                                        <label class="form-label" for="ChucDanhNguoiKT">Chức danh người ký
                                                            thay</label>
                                                        <input type="text" class="form-control" id="ChucDanhNguoiKT"
                                                            placeholder="Nhập chức danh người ký thay">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-lg-6">
                                                    <div class="mb-1">
                                                        <label class="form-label" for="NguoiKy">Họ tên người ký</label>
                                                        <input type="text" class="form-control" id="NguoiKy"
                                                            placeholder="Nhập họ tên người ký">
                                                    </div>
                                                </div>
                                                <div class="col-lg-6">
                                                    <div class="mb-1">
                                                        <label class="form-label" for="ChucDanhNguoiKy">Chức danh người
                                                            ký</label>
                                                        <input type="text" class="form-control" id="ChucDanhNguoiKy"
                                                            placeholder="Nhập chức danh người ký">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-lg-6">
                                                    <div class="mb-1">
                                                        <label class="form-label" for="NguoiLap">Họ tên người lập</label>
                                                        <input type="text" class="form-control" id="NguoiLap"
                                                            placeholder="Nhập họ tên người lập">
                                                    </div>
                                                </div>
                                                <div class="col-lg-6">
                                                    <div class="mb-1">
                                                        <label class="form-label" for="ChucDanhNguoiLap">Chức danh người
                                                            lập</label>
                                                        <input type="text" class="form-control" id="ChucDanhNguoiLap"
                                                            placeholder="Nhập chức danh người lập">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </fieldset>
                                    <fieldset class="KhungVien  mt-2">
                                        <legend class="bg-nts-header">3. Thiết lập ngày báo cáo</legend>
                                        <div class="widget-body rounded-10">
                                            <div class="row">
                                                <div class="col-lg-6">
                                                    <div class="mb-1">
                                                        <label class="form-label" for="DiaDanh">Địa danh</label>
                                                        <input type="text" class="form-control" id="DiaDanh"
                                                            placeholder="Nhập địa danh">
                                                    </div>
                                                </div>
                                                <div class="col-lg-6">
                                                    <div class="mb-1">
                                                        <label class="form-label" for="DangNgayBaoCao">Loại ngày lập báo
                                                            cáo</label>
                                                        <select class="form-control" id="DangNgayBaoCao" tabindex="0">
                                                            <option value="1" selected> Lấy ngày hiện tại</option>
                                                            <option value="2">Lấy ngày từ thiết lập</option>
                                                            <option value="3">Ngày... tháng... năm...</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-lg-6">
                                                    <div class="mb-1">
                                                        <label class="form-label" for="NgayLapBaoCao1">Ngày lập báo
                                                            cáo</label>
                                                        <input type="text" class="form-control date-picker"
                                                            id="NgayLapBaoCao1" autocomplete="off"
                                                            data-date-format="dd/mm/yyyy">
                                                    </div>
                                                </div>
                                                <div class="col-lg-6">
                                                    <div class="mb-1">
                                                        <label class="form-label" for="NgayLapBaoCao2">Mẫu hiểu thị</label>
                                                        <input type="text" class="form-control input-sm cauhinh"
                                                            id="NgayLapBaoCao2" placeholder="Ngày... tháng... năm..."
                                                            autocomplete="off">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </fieldset>
                                    <fieldset class="KhungVien mt-2">
                                        <legend class="bg-nts-header">4. Thiết lập địa bàn hành chính</legend>
                                        <div class="widget-body rounded-10">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="mb-1">
                                                        <label class="form-label" for="">Thuộc tỉnh</label>
                                                        <select class="form-control" id="TinhID" tabindex="0">
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-lg-4">
                                                    <div class="mb-1">
                                                        <label class="form-label" for="">Thuộc xã</label>
                                                        <select class="form-control" id="XaID" tabindex="0">
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-lg-4">
                                                    <div class="mb-1">
                                                        <label class="form-label" for="">Thuộc thôn</label>
                                                        <select class="form-control" id="ThonID" tabindex="0">
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                    </fieldset>
                                </div>
                            </div>
                            <button type="button" class="btn btn-primary btn-nts-them" id="btnLuuVaDong" style="float: right"
                                onclick="btnLuuDuLieu();return false;"><i class="fa fa-save"></i>&ensp;Lưu (F9)</button>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="Tab2_View" role="tabpanel" aria-labelledby="btnTab2_View">
                        <form enctype="multipart/form-data">
                            <fieldset class="KhungVien mt-2">
                                <legend class="bg-nts-header">1. Tiêu đề website & chân trang</legend>
                                <div class="row mb-4">
                                    <div class="col-md-6 mb-3 mb-md-0">
                                        <label for="TieuDeWebsite" class="form-label fw-bold">Tiêu đề website</label>
                                        <input type="text" class="form-control" id="TieuDeWebsite" name="TieuDeWebsite"
                                            placeholder="Nhập tiêu đề website...">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="NoiDungChanTrang" class="form-label fw-bold">Nội dung chân trang
                                            (footer)</label>
                                        <input class="form-control" id="NoiDungChanTrang" name="NoiDungChanTrang" rows="2"
                                            placeholder="Nhập nội dung chân trang...">
                                    </div>
                                </div>
                            </fieldset>
                            <fieldset class="KhungVien mt-2">
                                <legend class="bg-nts-header">2. Favicon & Logo website</legend>
                                <div class="row mb-4">
                                    <div class="col-md-6 mb-3 mb-md-0">
                                        <label for="FaviconUrl" class="form-label fw-bold">Favicon</label>
                                        <div>
                                            <input type="hidden" id="FaviconUrl">
                                            <input type="file" id="fileFaviconUrl" style="display: none;"
                                                accept=".jpg,.jpeg,.png,.gif,.webp,.ico">
                                            <button type="button" class="btn btn" id="btnChonFaviconUrl">
                                                <i class="fa fa-image"></i> Chọn ảnh (*.jpg, *.png, *.ico)
                                            </button>
                                        </div>
                                        <div id="previewFaviconUrl" class="mt-2" style="display: none;">
                                            <div class="d-flex align-items-center">
                                                <button type="button" class="btn btn-sm btn-success me-2"
                                                    id="btnXemFaviconUrl">
                                                    <i class="fa fa-eye"></i> Xem hình ảnh
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger" id="btnXoaFaviconUrl">
                                                    <i class="fa fa-times"></i> Xóa hình ảnh
                                                </button>
                                                <span id="tenFileFaviconUrl" class="ms-2 text-muted"></span>
                                            </div>
                                            <img id="imgPreviewFaviconUrl" src="" alt="Preview" class="mt-2"
                                                style="max-width: 200px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px; display: none;">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="LogoUrl" class="form-label fw-bold">Logo website</label>
                                        <div>
                                            <input type="hidden" id="LogoUrl">
                                            <input type="file" id="fileLogoUrl" style="display: none;"
                                                accept=".jpg,.jpeg,.png,.gif,.webp,.ico">
                                            <button type="button" class="btn btn" id="btnChonLogoUrl">
                                                <i class="fa fa-image"></i> Chọn ảnh (*.jpg, *.png, *.ico)
                                            </button>
                                        </div>
                                        <div id="previewLogoUrl" class="mt-2" style="display: none;">
                                            <div class="d-flex align-items-center">
                                                <button type="button" class="btn btn-sm btn-success me-2"
                                                    id="btnXemLogoUrl">
                                                    <i class="fa fa-eye"></i> Xem hình ảnh
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger" id="btnXoaLogoUrl">
                                                    <i class="fa fa-times"></i> Xóa hình ảnh
                                                </button>
                                                <span id="tenFileLogoUrl" class="ms-2 text-muted"></span>
                                            </div>
                                            <img id="imgPreviewLogoUrl" src="" alt="Preview" class="mt-2"
                                                style="max-width: 200px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px; display: none;">
                                        </div>
                                    </div>
                                </div>
                            </fieldset>
                            <fieldset class="KhungVien mt-2">
                                <legend class="bg-nts-header">3. Thông tin liên hệ</legend>
                                <div class="row mb-4">
                                    <div class="col-md-3 mb-3 mb-md-0">
                                        <label for="ThongTinLienHe-DiaChi" class="form-label fw-bold">Địa chỉ</label>
                                        <input type="text" id="ThongTinLienHe-DiaChi" class="form-control mb-2"
                                            name="ThongTinLienHe-DiaChi" placeholder="Địa chỉ">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="ThongTinLienHe-DienThoai" class="form-label fw-bold">Điện thoại</label>
                                        <input type="text" id="ThongTinLienHe-DienThoai" class="form-control mb-2"
                                            name="ThongTinLienHe-DienThoai" placeholder="Điện thoại">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="ThongTinLienHe-Email" class="form-label fw-bold">Email</label>
                                        <input type="email" id="ThongTinLienHe-Email" class="form-control mb-2"
                                            name="ThongTinLienHe-Email" placeholder="Email">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="ThongTinLienHe-Website" class="form-label fw-bold">Website</label>
                                        <input type="text" id="ThongTinLienHe-Website" class="form-control"
                                            name="ThongTinLienHe-Website" placeholder="Website">
                                    </div>
                                </div>
                            </fieldset>
                            <fieldset class="KhungVien mt-2">
                                <legend class="bg-nts-header">4. Ảnh đại diện & Banner</legend>
                                <div class="row mb-4">
                                    <div class="col-md-4 mb-3 mb-md-0">
                                        <!-- 
                                            <input type="file" class="form-control" id="AnhDaiDienBaiViet" name="AnhDaiDienBaiViet" accept="image/*" onchange="previewImage(this, 'thumbnailPreview')">
                                            <img id="thumbnailPreview" class="img-preview mt-2" src="/assets/default-post-thumbnail.png" alt="Thumbnail" style="max-width: 140px; max-height: 70px; border-radius: 6px; border: 1px solid #e0e0e0;"> -->
                                        <label for="AnhDaiDienBaiViet" class="form-label fw-bold">Ảnh đại diện bài viết mặc
                                            định</label>
                                        <div>
                                            <input type="hidden" id="AnhDaiDienBaiViet">
                                            <input type="file" id="fileAnhDaiDienBaiViet" style="display: none;"
                                                accept=".jpg,.jpeg,.png,.gif,.webp">
                                            <button type="button" class="btn btn" id="btnChonAnhDaiDienBaiViet">
                                                <i class="fa fa-image"></i> Chọn ảnh đính kèm (*.jpg, *.png)
                                            </button>
                                        </div>
                                        <div id="previewAnhDaiDienBaiViet" class="mt-2" style="display: none;">
                                            <div class="d-flex align-items-center">
                                                <button type="button" class="btn btn-sm btn-success me-2"
                                                    id="btnXemAnhDaiDienBaiViet">
                                                    <i class="fa fa-eye"></i> Xem hình ảnh
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger"
                                                    id="btnXoaAnhDaiDienBaiViet">
                                                    <i class="fa fa-times"></i> Xóa hình ảnh
                                                </button>
                                                <span id="tenFileAnhDaiDienBaiViet" class="ms-2 text-muted"></span>
                                            </div>
                                            <img id="imgPreviewAnhDaiDienBaiViet" src="" alt="Preview" class="mt-2"
                                                style="max-width: 200px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px; display: none;">
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3 mb-md-0">
                                        <label for="BannerLoginNho" class="form-label fw-bold">Banner login nhỏ</label>
                                        <div>
                                            <input type="hidden" id="BannerLoginNho">
                                            <input type="file" id="fileBannerLoginNho" style="display: none;"
                                                accept=".jpg,.jpeg,.png,.gif,.webp,.ico">
                                            <button type="button" class="btn btn" id="btnChonBannerLoginNho">
                                                <i class="fa fa-image"></i> Chọn ảnh (*.jpg, *.png, *.ico)
                                            </button>
                                        </div>
                                        <div id="previewBannerLoginNho" class="mt-2" style="display: none;">
                                            <div class="d-flex align-items-center">
                                                <button type="button" class="btn btn-sm btn-success me-2"
                                                    id="btnXemBannerLoginNho">
                                                    <i class="fa fa-eye"></i> Xem hình ảnh
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger"
                                                    id="btnXoaBannerLoginNho">
                                                    <i class="fa fa-times"></i> Xóa hình ảnh
                                                </button>
                                                <span id="tenFileBannerLoginNho" class="ms-2 text-muted"></span>
                                            </div>
                                            <img id="imgPreviewBannerLoginNho" src="" alt="Preview" class="mt-2"
                                                style="max-width: 200px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px; display: none;">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="BannerLoginMoi" class="form-label fw-bold">Banner login lớn</label>
                                        <div>
                                            <input type="hidden" id="BannerLoginMoi">
                                            <input type="file" id="fileBannerLoginMoi" style="display: none;"
                                                accept=".jpg,.jpeg,.png,.gif,.webp,.ico">
                                            <button type="button" class="btn btn" id="btnChonBannerLoginMoi">
                                                <i class="fa fa-image"></i> Chọn ảnh (*.jpg, *.png, *.ico)
                                            </button>
                                        </div>
                                        <div id="previewBannerLoginMoi" class="mt-2" style="display: none;">
                                            <div class="d-flex align-items-center">
                                                <button type="button" class="btn btn-sm btn-success me-2"
                                                    id="btnXemBannerLoginMoi">
                                                    <i class="fa fa-eye"></i> Xem hình ảnh
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger"
                                                    id="btnXoaBannerLoginMoi">
                                                    <i class="fa fa-times"></i> Xóa hình ảnh
                                                </button>
                                                <span id="tenFileBannerLoginMoi" class="ms-2 text-muted"></span>
                                            </div>
                                            <img id="imgPreviewBannerLoginMoi" src="" alt="Preview" class="mt-2"
                                                style="max-width: 200px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px; display: none;">
                                        </div>
                                    </div>
                                </div>
                            </fieldset>
                            <fieldset class="KhungVien mt-2">
                                <legend class="bg-nts-header">5. Cấu hình nâng cao</legend>

                                <div class="row g-3 mb-2 mt-2">
                                    <!-- Mỗi col giờ là col-md-3 (4 cột / dòng) -->
                                    <div class="col-md-3">
                                        <div class="d-flex align-items-center">
                                            <label class="me-2 mb-0 form-label" for="ck_YeuCauKiemDuyet">Yêu cầu kiểm
                                                duyệt</label>
                                            <div class="form-check form-switch m-0">
                                                <input class="form-check-input" type="checkbox" id="ck_YeuCauKiemDuyet">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-3">
                                        <div class="d-flex align-items-center">
                                            <label class="me-2 mb-0 form-label" for="ck_QuanTriNgayTrenWebsite">Quản trị
                                                ngay trên website</label>
                                            <div class="form-check form-switch m-0">
                                                <input class="form-check-input" type="checkbox"
                                                    id="ck_QuanTriNgayTrenWebsite">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-3">
                                        <div class="d-flex align-items-center">
                                            <label class="me-2 mb-0 form-label" for="ck_KhongHoiKhiRoiTrangChinh">Không hỏi
                                                khi rời trang chính</label>
                                            <div class="form-check form-switch m-0">
                                                <input class="form-check-input" type="checkbox"
                                                    id="ck_KhongHoiKhiRoiTrangChinh">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-3">
                                        <div class="d-flex align-items-center">
                                            <label class="me-2 mb-0 form-label" for="ck_KhoaKhongChoCopyNoiDung">Khóa không
                                                cho copy nội dung</label>
                                            <div class="form-check form-switch m-0">
                                                <input class="form-check-input" type="checkbox"
                                                    id="ck_KhoaKhongChoCopyNoiDung">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-3">
                                        <div class="d-flex align-items-center">
                                            <label class="me-2 mb-0 form-label" for="ck_XacThucTaiKhoan">Xác thực tài
                                                khoản</label>
                                            <div class="form-check form-switch m-0">
                                                <input class="form-check-input" type="checkbox" id="ck_XacThucTaiKhoan">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-3">
                                        <div class="d-flex align-items-center">
                                            <label class="me-2 mb-0 form-label" for="ck_TamNgungTaiKhoan">Tạm ngưng tài
                                                khoản</label>
                                            <div class="form-check form-switch m-0">
                                                <input class="form-check-input" type="checkbox" id="ck_TamNgungTaiKhoan">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-3">
                                        <div class="d-flex align-items-center">
                                            <label class="me-2 mb-0 form-label" for="ck_TuDongDuyetBai">Tự động duyệt
                                                bài</label>
                                            <div class="form-check form-switch m-0">
                                                <input class="form-check-input" type="checkbox" id="ck_TuDongDuyetBai">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-3">
                                        <div class="d-flex align-items-center">
                                            <label class="me-2 mb-0 form-label" for="ck_KiemDuyetBinhLuan">Kiểm duyệt bình
                                                luận</label>
                                            <div class="form-check form-switch m-0">
                                                <input class="form-check-input" type="checkbox" id="ck_KiemDuyetBinhLuan">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-3">
                                        <div class="d-flex align-items-center">
                                            <label class="me-2 mb-0 form-label" for="ck_CoDinhKhungWebsite">Cố định khung
                                                website</label>
                                            <div class="form-check form-switch m-0">
                                                <input class="form-check-input" type="checkbox" id="ck_CoDinhKhungWebsite">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-3">
                                        <div class="d-flex align-items-center">
                                            <label class="me-2 mb-0 form-label" for="ck_KhoaBaiDang">Khóa bài đăng</label>
                                            <div class="form-check form-switch m-0">
                                                <input class="form-check-input" type="checkbox" id="ck_KhoaBaiDang">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-3">
                                        <div class="d-flex align-items-center">
                                            <label class="me-2 mb-0 form-label" for="ck_CoNutLenDauTrang">Có nút lên đầu
                                                trang</label>
                                            <div class="form-check form-switch m-0">
                                                <input class="form-check-input" type="checkbox" id="ck_CoNutLenDauTrang">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row g-3 mb-2 mt-0">
                                    <div class="col-md-6">
                                        <label for="AdminTimeOut" class="form-label fw-bold">Thời gian timeout(milisecond)</label>
                                        <input type="text" class="form-control" id="AdminTimeOut" name="AdminTimeOut"
                                            placeholder="Thời gian timeout">
                                    </div>
                                </div>
                            </fieldset>
                            <button class="btn btn-primary px-4 py-2" style="float: right" type="submit">Lưu cấu hình</button>
                        </form>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>
    <input type="hidden" id="ThietLapWebsiteID" />
    {{--
    <script src="~/Areas/HeThong/Scripts/ThietLapHeThong.js?v=@DateTime.Now.Ticks.ToString()" defer="defer"></script>
    <script src="~/Content/Coloris-main/dist/coloris.min.js"></script> --}}
@endsection
@push('scripts')
    <script>
        window.Laravel = window.Laravel || {};
        window.Laravel.thietlaphethong = {
            GetDSDiaBanHC_Tinh: "{{ route('diabanhanhchinh.db.getdiabanhc_tinh') }}",
            GetDSDiaBanHC_ByIDCha: "{{ route('diabanhanhchinh.db.getdiabanhc_byidcha') }}",
        };
    </script>
    <script src="{{ asset('js/hethong/ThietLapHeThong.js') }}?v={{ time() }}"></script>
    <link rel="stylesheet" href="{{ asset('/css/hethong/ThietLapHeThong.css') }}">
@endpush