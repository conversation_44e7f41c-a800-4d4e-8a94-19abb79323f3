@extends('layouts.layouts')

{{-- tiêu đề trang --}}
@section('title', '<PERSON>h sách tin tức')

@push('styles')
    <link rel="stylesheet" href="{{ asset('css/bientapcongthongtin/tintuc-form.css') }}?v={{ time() }}">
@endpush

@section('content')
    @php
        $filters = [
            // 1. Tiêu đề / Loại tin tức
            '<div class="row mb-3">
    <div class="col-md-6">
        <label class="form-label" for="TieuDe_Loc">Tiêu đề</label>
        <input type="text" class="form-control input-sm" id="TieuDe_Loc" placeholder="Nhập tiêu đề…">
    </div>
    <div class="col-md-6">
        <label class="form-label" for="LoaiTinTuc_Loc">Loại tin tức</label>
        <select class="form-control input-sm" id="LoaiTinTuc_Loc">
            <option value="">— Chọn loại tin tức —</option>
        </select>
    </div>
</div>',

            // 2. Trạng thái sử dụng / Nổi bật
            '<div class="row mb-3">
    <div class="col-md-6">
        <label class="form-label" for="DangSD_Loc">Trạng thái sử dụng</label>
        <select class="form-control input-sm" id="DangSD_Loc">
            <option value="">— Chọn trạng thái —</option>
            <option value="1">Đang sử dụng</option>
            <option value="0">Ngừng sử dụng</option>
        </select>
    </div>
    <div class="col-md-6">
        <label class="form-label" for="LaNoiBat_Loc">Nổi bật</label>
        <select class="form-control input-sm" id="LaNoiBat_Loc">
            <option value="">— Chọn tất cả —</option>
            <option value="1">Nổi bật</option>
            <option value="0">Thường</option>
        </select>
    </div>
</div>',

            // 3. Trạng thái kiểm duyệt (ẩn/hiện dựa trên thiết lập)
            '<div class="row mb-3 kiem-duyet-element">
    <div class="col-md-6">
        <label class="form-label" for="TrangThaiKiemDuyet_Loc">Trạng thái kiểm duyệt</label>
        <select class="form-control input-sm" id="TrangThaiKiemDuyet_Loc">
            <option value="">— Chọn tất cả —</option>
            <option value="1">Đã duyệt</option>
            <option value="0">Chờ duyệt</option>
            <option value="2">Từ chối</option>
        </select>
    </div>
    <div class="col-md-6">
    </div>
</div>',
        ];

    @endphp
    @include('partials.filter-panel', [
        'filters' => $filters,
        'actions' => null,
        'showTimKiemNangCao' => true,
        'showBulkActions' => true,
    ])

    <div class="row" style="margin-top: 4px; padding-left: 0;">
        <div class="col-md-12 px-0">
            <div id="Grid1"></div>
        </div>
    </div>

    {{-- Modal thêm/sửa --}}
    <div class="modal modal-blur fade" id="mdThemMoi" tabindex="-1" aria-modal="true" role="dialog" data-bs-backdrop="static">
        <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tieuDeModal"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="tinTucId" />
                    <div class="row">
                        <!-- Hàng 1: Ngày tạo và Người tạo -->
                        <div class="col-lg-6 mb-3">
                            <label class="form-label" for="ngayTao">Ngày tạo <span class="text-danger">(*)</span></label>
                            <input type="date" class="form-control" id="ngayTao" readonly>
                        </div>
                        <div class="col-lg-6 mb-3">
                            <label class="form-label" for="nguoiTao">Người tạo <span class="text-danger">(*)</span></label>
                            <input type="text" class="form-control" id="nguoiTao" readonly>
                        </div>

                        <!-- Hàng 2: Loại bài viết và Từ khóa -->
                        <div class="col-lg-6 mb-3">
                            <label class="form-label" for="loaiTinTucID">Loại bài viết </label>
                            <select class="form-select" id="loaiTinTucID" required>
                                <option value="">--Chọn loại bài viết--</option>
                            </select>
                        </div>
                        <div class="col-lg-6 mb-3">
                            <label class="form-label" for="tuKhoa">Từ khóa <span class="text-danger">(*)</span></label>
                            <input type="text" class="form-control" id="tuKhoa" placeholder="Nhập từ khóa...">
                        </div>

                        <!-- Hàng 3: Tiêu đề -->
                        <div class="col-lg-12 mb-3">
                            <label class="form-label" for="tieuDe">Tiêu đề </label>
                            <input type="text" class="form-control" id="tieuDe" required
                                placeholder="Nhập tiêu đề...">
                        </div>

                        <!-- Hàng 4: Định danh -->
                        <div class="col-lg-12 mb-3">
                            <label class="form-label" for="dinhDanh">Định danh </label>
                            <input type="text" class="form-control" id="dinhDanh" readonly
                                placeholder="Tự động tạo từ tiêu đề...">
                        </div>

                        <!-- Hàng 5: Mô tả ngắn -->
                        <div class="col-lg-12 mb-3">
                            <label class="form-label" for="noiDungTomTat">Mô tả ngắn </label>
                            <textarea class="form-control" id="noiDungTomTat" rows="5" required placeholder="Nhập mô tả ngắn..."></textarea>
                        </div>

                        <!-- Hàng 6: Chọn ảnh -->
                        <div class="col-lg-12 mb-3">
                            <label class="form-label" for="hinhAnh">Chọn ảnh <span class="text-danger">(*)</span></label>
                            <div>
                                <input type="hidden" id="hinhAnh">
                                <input type="file" id="fileHinhAnh" style="display: none;"
                                    accept=".jpg,.jpeg,.png,.gif,.webp">
                                <button type="button" class="btn btn-primary" id="btnChonAnh">
                                    <i class="fa fa-image"></i> Chọn ảnh đính kèm (*.jpg, *.png)
                                </button>
                            </div>
                            <div id="previewHinhAnh" class="mt-2" style="display: none;">
                                <div class="d-flex align-items-center">
                                    <button type="button" class="btn btn-sm btn-success me-2" id="btnXemHinhAnh">
                                        <i class="fa fa-eye"></i> Xem hình ảnh
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger" id="btnXoaHinhAnh">
                                        <i class="fa fa-times"></i> Xóa hình ảnh
                                    </button>
                                    <span id="tenFileHinhAnh" class="ms-2 text-muted"></span>
                                </div>
                                <img id="imgPreview" src="" alt="Preview" class="mt-2"
                                    style="max-width: 200px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px; display: none;">
                            </div>
                        </div>

                        <!-- Hàng 7: Nội dung -->
                        <div class="col-lg-12 mb-3">
                            <label class="form-label" for="noiDung">Nội dung</label>
                            <textarea class="form-control" id="noiDung" rows="8"></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <div style="display: contents; align-items: center; justify-content: space-between">
                        <div class="col-md-6" style="display: flex; align-items: center">
                            <label style="margin-bottom: unset; margin-right: 15px;" class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="dangSD">
                                <label class="form-check-label" for="dangSD">Đang sử dụng</label>
                            </label>
                            <label style="margin-bottom: unset; margin-right: 15px;" class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="laNoiBat">
                                <label class="form-check-label" for="laNoiBat">Nổi bật</label>
                            </label>
                            <label style="margin-bottom: unset; margin-right: 15px;"
                                class="form-check form-switch kiem-duyet-toggle">
                                <input class="form-check-input" type="checkbox" id="daKiemDuyet">
                                <label class="form-check-label" for="daKiemDuyet">Đã kiểm duyệt</label>
                            </label>
                        </div>
                        <div class="col-md-6">
                            <div style="float:right;text-align: right">
                                <a href="#" class="btn btn-outline-danger" data-bs-dismiss="modal">
                                    <i class="fa fa-close"></i>&nbsp;Đóng (F4)
                                </a>
                                <a href="#" id="btnLuuVaDong" class="btn btn-success ms-auto nts-color-luu">
                                    <i class="fa fa-save"></i>&ensp;Lưu và đóng (F9)
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('partials.nhapExcelModal', [
        'id' => 'mdNhanExcel',
        'title' => 'Nhận excel tin tức',
    ])

@endsection

@push('scripts')
    <script>
        window.Laravel = window.Laravel || {};
        window.Laravel.tintuc = {
            // other URLs you may already have...
            exportExcelUrl: "{{ url('export/excel_v2') }}",
            exportPdfUrl: "{{ url('export/pdf_v2') }}",
            uploadDocUrl: "{{ route('api.files.upload') }}",
            uploadImageUrl: "{{ route('api.congthongtin.files.uploadImage') }}",
            loadTenSheetUrl: "{{ route('nhanvien.loadSheetNames') }}",
            checkExcel: "{{ route('nhanvien.checkExcel') }}",
            loadExcel: "{{ route('nhanvien.loadExcel') }}",
            importExcel: "{{ route('nhanvien.importExcel') }}",
            downloadTemplate: "{{ route('nhanvien.template') }}",
            bulkDelete: "{{ route('bientapcongthongtin.tintuc.bulkDelete') }}",
            toggleStatusUrl: "{{ route('bientapcongthongtin.tintuc.toggleStatus', ':id') }}",
            deleteOldImageUrl: "{{ route('bientapcongthongtin.tintuc.deleteOldImage') }}"
        };
        $(document).on("click", ".btnTaiFileMauNhanExcel", function() {
            // simply open the template URL in a new tab to trigger download
            window.open(window.Laravel.tintuc.downloadTemplate, '_blank');
        });

        // Initialize TinyMCE for content field
        $(document).ready(function() {
            initTinyMCE();
            initImageUpload();
            initSlugGeneration();
            initFormDefaults();
        });

        // Image upload functionality
        function initImageUpload() {
            // Trigger file input when button is clicked
            $('#btnChonAnh').on('click', function() {
                $('#fileHinhAnh').click();
            });

            // Handle file selection
            $('#fileHinhAnh').on('change', function() {
                const file = this.files[0];
                if (file) {
                    // Validate file type
                    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
                    if (!allowedTypes.includes(file.type)) {
                        NTS.canhbao('Vui lòng chọn file ảnh hợp lệ (JPG, PNG, GIF, WEBP)!');
                        this.value = '';
                        return;
                    }

                    // Validate file size (max 5MB)
                    if (file.size > 5 * 1024 * 1024) {
                        NTS.canhbao('Kích thước file không được vượt quá 5MB!');
                        this.value = '';
                        return;
                    }

                    // Show loading
                    NTS.loadding();

                    // Xóa ảnh cũ trước khi upload ảnh mới (chỉ khi đang sửa tin tức)
                    const tinTucId = $('#tinTucId').val();
                    const currentImageUrl = $('#hinhAnh').val();

                    const uploadNewImage = function() {
                        // Upload file
                        const formData = new FormData();
                        formData.append('file', file);
                        formData.append('thuMucLuu', 'tin-tuc');

                        $.ajax({
                            url: '/congthongtin/upload-image',
                            type: 'POST',
                            data: formData,
                            processData: false,
                            contentType: false,
                            headers: {
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                            },
                            success: function(response) {
                                NTS.unloadding();
                                if (response.success) {
                                    // Set the URL to the input
                                    $('#hinhAnh').val(response.url);

                                    // Show preview with file name
                                    $('#tenFileHinhAnh').text(file.name);
                                    $('#imgPreview').attr('src', response.url);
                                    $('#previewHinhAnh').show();

                                    NTS.thanhcong('Upload ảnh thành công!');
                                } else {
                                    NTS.loi('Upload ảnh thất bại!');
                                }
                            },
                            error: function(xhr) {
                                NTS.unloadding();
                                let errorMsg = 'Upload ảnh thất bại!';
                                if (xhr.responseJSON && xhr.responseJSON.message) {
                                    errorMsg = xhr.responseJSON.message;
                                }
                                NTS.loi(errorMsg);
                            }
                        });
                    };

                    // Nếu đang sửa tin tức và có ảnh cũ, xóa ảnh cũ trước
                    if (tinTucId && currentImageUrl) {
                        $.ajax({
                            url: window.Laravel.tintuc.deleteOldImageUrl,
                            type: 'POST',
                            data: {
                                tinTucId: tinTucId,
                                _token: $('meta[name="csrf-token"]').attr('content')
                            },
                            success: function(response) {
                                console.log('Đã xóa ảnh cũ:', response);
                                // Tiếp tục upload ảnh mới
                                uploadNewImage();
                            },
                            error: function(xhr) {
                                console.warn('Không thể xóa ảnh cũ:', xhr);
                                // Vẫn tiếp tục upload ảnh mới dù xóa ảnh cũ thất bại
                                uploadNewImage();
                            }
                        });
                    } else {
                        // Không có ảnh cũ hoặc đang thêm mới, upload trực tiếp
                        uploadNewImage();
                    }
                }
            });

            // Handle view image
            $('#btnXemHinhAnh').on('click', function() {
                const imgSrc = $('#imgPreview').attr('src');
                if (imgSrc) {
                    $('#imgPreview').toggle();
                }
            });

            // Handle remove image
            $('#btnXoaHinhAnh').on('click', function() {
                $('#hinhAnh').val('');
                $('#fileHinhAnh').val('');
                $('#previewHinhAnh').hide();
                $('#imgPreview').attr('src', '').hide();
                $('#tenFileHinhAnh').text('');
            });
        }

        // Auto generate slug from title
        function initSlugGeneration() {
            $('#tieuDe').on('input', function() {
                const title = $(this).val();
                if (title) {
                    const slug = generateSlug(title);
                    $('#dinhDanh').val(slug);
                }
            });
        }

        // Generate slug function
        function generateSlug(text) {
            return text
                .toLowerCase()
                .normalize('NFD')
                .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
                .replace(/[đĐ]/g, 'd')
                .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
                .replace(/\s+/g, '-') // Replace spaces with hyphens
                .replace(/-+/g, '-') // Replace multiple hyphens with single
                .trim('-'); // Remove leading/trailing hyphens
        }

        // Initialize form defaults
        function initFormDefaults() {
            // Set current date
            const today = new Date().toISOString().split('T')[0];
            $('#ngayTao').val(today);

            // Set current user (you can modify this based on your authentication system)
            $('#nguoiTao').val('Admin'); // Replace with actual user name
        }

        function initTinyMCE() {
            tinymce.init({
                selector: '#noiDung',
                height: 500,
                menubar: true,
                plugins: [
                    'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                    'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                    'insertdatetime', 'media', 'table', 'help', 'wordcount', 'emoticons',
                    'template', 'codesample', 'pagebreak', 'nonbreaking', 'quickbars',
                    'save', 'directionality', 'visualchars', 'autoresize'
                ],
                toolbar: 'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | ' +
                    'alignleft aligncenter alignright alignjustify | outdent indent | numlist bullist | ' +
                    'forecolor backcolor removeformat | pagebreak | charmap emoticons | ' +
                    'fullscreen preview save print | insertfile image media template link anchor codesample | ' +
                    'ltr rtl | code visualblocks | table tabledelete | tableprops tablerowprops tablecellprops | ' +
                    'tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol',
                content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; font-size: 14px; -webkit-font-smoothing: antialiased; }',
                skin: 'oxide',
                content_css: 'default',
                directionality: 'ltr',
                branding: false,
                promotion: false,
                resize: 'both',
                statusbar: true,
                elementpath: true,
                convert_urls: false,
                relative_urls: false,
                remove_script_host: false,
                document_base_url: window.location.origin,

                // Link plugin configuration
                link_assume_external_targets: true,
                link_context_toolbar: true,
                link_default_target: '_blank',
                link_default_protocol: 'https',
                link_title: true,
                target_list: [{
                        title: 'None',
                        value: ''
                    },
                    {
                        title: 'Same window',
                        value: '_self'
                    },
                    {
                        title: 'New window',
                        value: '_blank'
                    },
                    {
                        title: 'Parent window',
                        value: '_parent'
                    },
                    {
                        title: 'Top window',
                        value: '_top'
                    }
                ],

                // Advanced features
                paste_data_images: true,
                paste_as_text: false,
                paste_auto_cleanup_on_paste: true,
                paste_remove_styles: false,
                paste_remove_styles_if_webkit: false,
                paste_strip_class_attributes: 'none',

                // Image handling
                automatic_uploads: true,
                file_picker_types: 'image',
                images_upload_url: '{{ route('api.files.upload') }}',
                images_upload_handler: function(blobInfo, success, failure) {
                    var xhr, formData;
                    xhr = new XMLHttpRequest();
                    xhr.withCredentials = false;
                    xhr.open('POST', '{{ route('api.files.upload') }}');
                    xhr.setRequestHeader('X-CSRF-TOKEN', $('meta[name="csrf-token"]').attr('content'));

                    xhr.onload = function() {
                        var json;
                        if (xhr.status != 200) {
                            failure('HTTP Error: ' + xhr.status);
                            return;
                        }
                        json = JSON.parse(xhr.responseText);
                        if (!json || typeof json.location != 'string') {
                            failure('Invalid JSON: ' + xhr.responseText);
                            return;
                        }
                        success(json.location);
                    };

                    formData = new FormData();
                    formData.append('file', blobInfo.blob(), blobInfo.filename());
                    xhr.send(formData);
                },

                // Table settings
                table_default_attributes: {
                    'class': 'table table-bordered'
                },
                table_default_styles: {
                    'border-collapse': 'collapse',
                    'width': '100%'
                },

                // Code sample settings
                codesample_languages: [{
                        text: 'HTML/XML',
                        value: 'markup'
                    },
                    {
                        text: 'JavaScript',
                        value: 'javascript'
                    },
                    {
                        text: 'CSS',
                        value: 'css'
                    },
                    {
                        text: 'PHP',
                        value: 'php'
                    },
                    {
                        text: 'Python',
                        value: 'python'
                    },
                    {
                        text: 'Java',
                        value: 'java'
                    },
                    {
                        text: 'C',
                        value: 'c'
                    },
                    {
                        text: 'C#',
                        value: 'csharp'
                    },
                    {
                        text: 'C++',
                        value: 'cpp'
                    }
                ],

                // Template settings
                templates: [{
                        title: 'Bài viết cơ bản',
                        description: 'Template cơ bản cho bài viết tin tức',
                        content: '<h2>Tiêu đề chính</h2><p>Nội dung giới thiệu...</p><h3>Nội dung chi tiết</h3><p>Chi tiết bài viết...</p><h3>Kết luận</h3><p>Tóm tắt và kết luận...</p>'
                    },
                    {
                        title: 'Thông báo',
                        description: 'Template cho thông báo chính thức',
                        content: '<div style="border: 1px solid #ddd; padding: 20px; margin: 10px 0;"><h3 style="color: #d9534f;">THÔNG BÁO</h3><p><strong>Về việc:</strong> [Nội dung thông báo]</p><p><strong>Thời gian:</strong> [Ngày/tháng/năm]</p><p><strong>Địa điểm:</strong> [Địa điểm]</p><p><strong>Nội dung:</strong></p><p>[Chi tiết nội dung thông báo]</p></div>'
                    }
                ],

                setup: function(editor) {
                    editor.on('change', function() {
                        editor.save();
                    });
                }
            });
        }
    </script>
    <script src="{{ asset('js/bientapcongthongtin/tintuc.js') }}?v={{ time() }}"></script>
@endpush
