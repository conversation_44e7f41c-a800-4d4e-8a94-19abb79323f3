<link rel="stylesheet" href="{{ asset('css/congthongtin/news-events.css') }}" type="text/css" media="all">

<div class="news-section" style="padding: 40px 0;">
    <div class="news-section-header">
        <h2 style="text-align: center; margin-bottom: 30px;"><span style="color: #0066ff;">📰 TIN TỨC LIÊN QUAN</span>
        </h2>
    </div>

    <div class="news-cards-container">
        @if (isset($tinTucNoiBat) && $tinTucNoiBat->count() > 0)
            @php
                // Luôn tạo đủ tin tức để có hiệu ứng slide
                $originalCount = $tinTucNoiBat->count();

                // Tạo ít nhất 8 tin tức để có hiệu ứng slide mượt mà
                $minNewsForSlide = 8;
                $allDisplayNews = collect();

                if ($originalCount > 0) {
                    // Lặp lại tin tức để có đủ số lượng
                    $cycles = ceil($minNewsForSlide / $originalCount);
                    for ($i = 0; $i < $cycles; $i++) {
                        $shuffledNews = $tinTucNoiBat->shuffle();
                        $allDisplayNews = $allDisplayNews->concat($shuffledNews);
                    }

                    // Lấy đúng số lượng cần thiết
                    $allDisplayNews = $allDisplayNews->take($minNewsForSlide);
                }

                // Luôn bật hiệu ứng slide nếu có tin tức
                $hasMultipleNews = $originalCount > 0;

                // Debug log
                \Log::info(
                    'Related News Debug: Original: ' .
                        $originalCount .
                        ', Display: ' .
                        $allDisplayNews->count() .
                        ', hasMultipleNews: ' .
                        ($hasMultipleNews ? 'true' : 'false'),
                );
            @endphp

            <div class="news-cards-wrapper {{ $hasMultipleNews ? 'has-multiple-news' : '' }}">

                @foreach ($allDisplayNews as $index => $tinTuc)
                    <div class="news-card card-1"
                        onclick="window.location.href='{{ route('news.detail', $tinTuc->DinhDanh) }}'"
                        style="cursor: pointer;">
                        <div class="card-image">
                            @if ($tinTuc->HinhAnh)
                                @if (str_starts_with($tinTuc->HinhAnh, 'http'))
                                    <img src="{{ $tinTuc->HinhAnh }}" alt="{{ $tinTuc->TieuDe }}"
                                        style="width: 100%; height: 100%; object-fit: cover;" loading="lazy"
                                        onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                    <div class="illustration-1" style="display: none;"></div>
                                @else
                                    <img src="{{ asset($tinTuc->HinhAnh) }}" alt="{{ $tinTuc->TieuDe }}"
                                        style="width: 100%; height: 100%; object-fit: cover;" loading="lazy"
                                        onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                    <div class="illustration-1" style="display: none;"></div>
                                @endif
                            @else
                                <div class="illustration-1"></div>
                            @endif
                        </div>
                        <div class="card-content">
                            <h3>{{ $tinTuc->TieuDe }}</h3>
                            <div class="card-meta">
                                <span class="author"><i class="fas fa-user"></i> {{ $tinTuc->NguoiTao }}</span>
                                <span class="date"><i class="fas fa-calendar"></i>
                                    {{ $tinTuc->formatted_ngay_tao }}</span>
                                <span class="views"><i class="fas fa-eye"></i> {{ number_format($tinTuc->LuotXem) }}
                                    lượt xem</span>
                            </div>
                            <div class="category-tag">
                                <span class="icon"><i class="fas fa-tags"></i></span>
                                <span class="title">{{ $tinTuc->loaiTinTuc->TenLoaiTinTuc ?? 'Tin tức' }}</span>
                            </div>

                            <p class="card-description">
                                {{ Str::limit($tinTuc->NoiDungTomTat, 100) }}
                            </p>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="news-cards-wrapper">
                {{-- Fallback content if no related news --}}
                <div class="news-card card-1">
                    <div class="card-image">
                        <div class="illustration-1"></div>
                    </div>
                    <div class="card-content">
                        <h3>Chưa có tin tức liên quan</h3>
                        <div class="card-meta">
                            <span class="author"><i class="fas fa-user"></i> Hệ thống</span>
                            <span class="date"><i class="fas fa-calendar"></i> {{ date('d/m/Y') }}</span>
                        </div>
                        <div class="category-tag">
                            <span class="icon"><i class="fas fa-tags"></i></span>
                            <span class="title">Thông báo</span>
                        </div>
                        <p class="card-description">
                            Hiện tại chưa có tin tức liên quan nào. Vui lòng xem các tin tức khác.
                        </p>
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>

<script src="{{ asset('js/congthongtin/news-events.js') }}" type="text/javascript"></script>
