<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $tinTuc->TieuDe }} - Tin tức</title>
    <meta name="description" content="{{ $tinTuc->NoiDungTomTat }}">
    <meta name="keywords" content="{{ $tinTuc->TuKhoa }}">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
        }

        .news-header {
            background: url('{{ asset('assets/congthongtin/bg-page.png') }}') center/cover no-repeat;
            min-height: 500px;
            color: white;
            padding: 100px 0;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            position: relative;
        }

        .news-content {
            padding: 40px 0;
        }

        .news-meta {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }

        .news-meta .meta-item {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 10px;
        }

        .news-meta .meta-item i {
            color: #2563eb;
            margin-right: 5px;
        }

        .news-body {
            font-size: 16px;
            line-height: 1.8;
        }

        .news-body h3 {
            color: #2563eb;
            margin-top: 30px;
            margin-bottom: 15px;
        }

        .news-body ul {
            padding-left: 20px;
        }

        .news-body li {
            margin-bottom: 8px;
        }



        .breadcrumb {
            background: none;
            padding: 0;
            margin-bottom: 20px;
            justify-content: center;
        }

        .breadcrumb-item a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
        }

        .breadcrumb-item a:hover {
            color: white;
        }

        .breadcrumb-item.active {
            color: rgba(255, 255, 255, 0.6);
        }

        .back-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            border-color: rgba(255, 255, 255, 0.5);
        }

        .category-badge {
            background: rgba(255, 255, 255, 0.2);
            text-align: center;
            width: 100px;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            display: block;
            margin: 0 auto 15px auto;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .news-subtitle {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.9);
            margin-top: 20px;
            line-height: 1.6;
            font-weight: 400;
            text-align: center;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Content Protection Styles */
        .protected-content {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-touch-callout: none;
            -webkit-tap-highlight-color: transparent;
            pointer-events: auto;
        }

        .protected-content::selection {
            background: transparent;
        }

        .protected-content::-moz-selection {
            background: transparent;
        }

        /* Disable drag for images */
        .protected-content img {
            -webkit-user-drag: none;
            -khtml-user-drag: none;
            -moz-user-drag: none;
            -o-user-drag: none;
            user-drag: none;
            pointer-events: none;
        }

        /* Overlay protection for critical content */
        .content-overlay {
            position: relative;
        }

        .content-overlay::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
            pointer-events: none;
        }

        /* Disable print styles */
        @media print {
            .protected-content {
                display: none !important;
            }

            .print-warning {
                display: block !important;
                text-align: center;
                font-size: 18px;
                color: #333;
                padding: 50px;
            }
        }

        .print-warning {
            display: none;
        }

        /* Comments Section Styles */
        .comment-form-section {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }

        .comment-form .form-control {
            border-radius: 8px;
            border: 1px solid #ddd;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .comment-form .form-control:focus {
            border-color: #2563eb;
            box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
        }

        .comment-item {
            background: #fff;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .comment-item:hover {
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .comment-author {
            font-weight: 600;
            color: #2563eb;
            margin-bottom: 5px;
        }

        .comment-date {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 10px;
        }

        .comment-content {
            color: #333;
            line-height: 1.6;
        }

        .comment-status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .comment-status.approved {
            background: #d4edda;
            color: #155724;
        }

        .comment-status.pending {
            background: #fff3cd;
            color: #856404;
        }

        .comment-status.rejected {
            background: #f8d7da;
            color: #721c24;
        }

        .comments-loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .comments-empty {
            text-align: center;
            padding: 40px;
            color: #6c757d;
            background: #f8f9fa;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }

        .btn-loading {
            position: relative;
        }

        .btn-loading:disabled {
            opacity: 0.7;
        }

        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }

        /* Floating Share Buttons */
        .floating-share-buttons {
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .floating-share-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: white;
            font-size: 18px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .floating-share-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
            color: white;
            text-decoration: none;
        }

        .floating-share-btn.facebook {
            background: #1877f2;
        }

        .floating-share-btn.facebook:hover {
            background: #166fe5;
        }

        .floating-share-btn.twitter {
            background: #1da1f2;
        }

        .floating-share-btn.twitter:hover {
            background: #0d8bd9;
        }

        .floating-share-btn.linkedin {
            background: #0077b5;
        }

        .floating-share-btn.linkedin:hover {
            background: #005885;
        }

        .floating-share-btn.copy {
            background: #6c757d;
        }

        .floating-share-btn.copy:hover {
            background: #545b62;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .floating-share-buttons {
                right: 10px;
                gap: 8px;
            }

            .floating-share-btn {
                width: 45px;
                height: 45px;
                font-size: 16px;
            }
        }

        @media (max-width: 576px) {
            .floating-share-buttons {
                position: relative;
                right: auto;
                top: auto;
                transform: none;
                flex-direction: row;
                justify-content: center;
                margin: 20px 0;
            }
        }
    </style>
</head>

<body class="has-absolute-header">
    @include('congthongtin.components.header')

    <div class="news-header">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('home') }}">Trang chủ</a></li>
                <li class="breadcrumb-item"><a href="{{ route('news.index') }}">Tin tức & Sự kiện</a></li>
                <li class="breadcrumb-item active" aria-current="page">Chi tiết tin tức</li>
            </ol>
        </nav>

        <h2 style="text-transform: capitalize; font-weight: 700; color: #fff;">{{ $tinTuc->TieuDe }}</h2>

        @if ($tinTuc->NoiDungTomTat)
            <p class="news-subtitle">{{ $tinTuc->NoiDungTomTat }}</p>
        @endif

        <div class="category-badge">
            {{ $tinTuc->loaiTinTuc->TenLoaiTinTuc ?? 'Tin tức' }}
        </div>
    </div>

    <!-- Content -->
    <div class="news-content">
        <div class="container">
            <div class="row">
                <!-- News Meta -->
                <div class="news-meta">
                    <div class="meta-item">
                        <i class="fas fa-user"></i>
                        <strong>Tác giả:</strong> {{ $tinTuc->NguoiTao }}
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-calendar"></i>
                        <strong>Ngày đăng:</strong> {{ $tinTuc->formatted_ngay_tao }}
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-eye"></i>
                        <strong>Lượt xem:</strong> {{ number_format($tinTuc->LuotXem) }}
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-tags"></i>
                        <strong>Từ khóa:</strong> {{ $tinTuc->TuKhoa }}
                    </div>
                </div>



                <!-- News Image -->
                @if ($tinTuc->HinhAnh)
                    <div class="text-center mb-4 protected-content">
                        @if (str_starts_with($tinTuc->HinhAnh, 'http'))
                            <img src="{{ $tinTuc->HinhAnh }}" alt="{{ $tinTuc->TieuDe }}" class="img-fluid rounded"
                                loading="lazy" draggable="false" oncontextmenu="return false;">
                        @else
                            <img src="{{ asset($tinTuc->HinhAnh) }}" alt="{{ $tinTuc->TieuDe }}"
                                class="img-fluid rounded" loading="lazy" draggable="false"
                                oncontextmenu="return false;">
                        @endif
                    </div>
                @endif

                <!-- News Body -->
                <div class="news-body protected-content content-overlay">
                    {!! $tinTuc->NoiDung !!}
                </div>



                <!-- Comments Section -->
                <div class="mt-5 pt-4 border-top">
                    <h4 class="mb-4"><i class="fas fa-comments me-2"></i>Bình luận</h4>

                    <!-- Comment Form -->
                    <div class="comment-form-section mb-5">
                        <h5 class="mb-3">Để lại bình luận của bạn</h5>
                        <form id="commentForm" class="comment-form">
                            @csrf
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="HoVaTen" class="form-label">Họ và tên <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="HoVaTen" name="HoVaTen" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="Email" class="form-label">Email <span
                                            class="text-danger">*</span></label>
                                    <input type="email" class="form-control" id="Email" name="Email" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="BinhLuan" class="form-label">Nội dung bình luận <span
                                        class="text-danger">*</span></label>
                                <textarea class="form-control" id="BinhLuan" name="BinhLuan" rows="4"
                                    placeholder="Nhập nội dung bình luận của bạn..." maxlength="1000" required></textarea>
                                <div class="form-text">Tối đa 1000 ký tự</div>
                                <div class="invalid-feedback"></div>
                            </div>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-paper-plane me-2"></i>Gửi bình luận
                            </button>
                        </form>
                    </div>

                    <!-- Comments List -->
                    <div class="comments-section">
                        <h5 class="mb-3">Các bình luận</h5>
                        <div id="commentsList" class="comments-list">
                            <!-- Comments will be loaded here -->
                        </div>
                        <div class="text-center mt-4">
                            <button id="loadMoreBtn" class="btn btn-outline-primary" style="display: none;">
                                <i class="fas fa-chevron-down me-2"></i>Xem thêm bình luận
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Related News -->
    <div class="container px-3" style="overflow-x: hidden; background-color: #fff">
        @if ($tinTucLienQuan->count() > 0)
            @include('congthongtin.components.related-news', ['tinTucNoiBat' => $tinTucLienQuan])
        @endif
    </div>



    <!-- Print Warning -->
    <div class="print-warning">
        <h2>⚠️ Nội dung được bảo vệ</h2>
        <p>Nội dung này không thể in được do chính sách bảo vệ bản quyền.</p>
    </div>

    @include('congthongtin.components.footer')

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Content Protection Script -->
    <script>
        (function() {
            'use strict';

            // Disable right-click context menu
            document.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                return false;
            });

            // Disable text selection with mouse
            document.addEventListener('selectstart', function(e) {
                if (e.target.closest('.protected-content')) {
                    e.preventDefault();
                    return false;
                }
            });

            // Disable drag start
            document.addEventListener('dragstart', function(e) {
                if (e.target.closest('.protected-content')) {
                    e.preventDefault();
                    return false;
                }
            });

            // Disable common keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Disable Ctrl+A (Select All)
                if (e.ctrlKey && e.keyCode === 65) {
                    e.preventDefault();
                    return false;
                }

                // Disable Ctrl+C (Copy)
                if (e.ctrlKey && e.keyCode === 67) {
                    e.preventDefault();
                    return false;
                }

                // Disable Ctrl+V (Paste)
                if (e.ctrlKey && e.keyCode === 86) {
                    e.preventDefault();
                    return false;
                }

                // Disable Ctrl+X (Cut)
                if (e.ctrlKey && e.keyCode === 88) {
                    e.preventDefault();
                    return false;
                }

                // Disable Ctrl+S (Save)
                if (e.ctrlKey && e.keyCode === 83) {
                    e.preventDefault();
                    return false;
                }

                // Disable Ctrl+P (Print)
                if (e.ctrlKey && e.keyCode === 80) {
                    e.preventDefault();
                    alert('Chức năng in đã bị vô hiệu hóa để bảo vệ nội dung.');
                    return false;
                }

                // Disable F12 (Developer Tools)
                if (e.keyCode === 123) {
                    e.preventDefault();
                    return false;
                }

                // Disable Ctrl+Shift+I (Developer Tools)
                if (e.ctrlKey && e.shiftKey && e.keyCode === 73) {
                    e.preventDefault();
                    return false;
                }

                // Disable Ctrl+Shift+J (Console)
                if (e.ctrlKey && e.shiftKey && e.keyCode === 74) {
                    e.preventDefault();
                    return false;
                }

                // Disable Ctrl+U (View Source)
                if (e.ctrlKey && e.keyCode === 85) {
                    e.preventDefault();
                    return false;
                }

                // Disable Ctrl+Shift+C (Inspect Element)
                if (e.ctrlKey && e.shiftKey && e.keyCode === 67) {
                    e.preventDefault();
                    return false;
                }
            });





            // Disable image saving
            document.addEventListener('DOMContentLoaded', function() {
                const images = document.querySelectorAll('img');
                images.forEach(function(img) {
                    img.addEventListener('dragstart', function(e) {
                        e.preventDefault();
                    });

                    img.addEventListener('contextmenu', function(e) {
                        e.preventDefault();
                    });
                });
            });

            // Disable print
            window.addEventListener('beforeprint', function(e) {
                e.preventDefault();
                alert('Chức năng in đã bị vô hiệu hóa để bảo vệ nội dung.');
                return false;
            });

            // Override print function
            window.print = function() {
                alert('Chức năng in đã bị vô hiệu hóa để bảo vệ nội dung.');
                return false;
            };

            // Disable copy via clipboard API
            document.addEventListener('copy', function(e) {
                if (e.target.closest('.protected-content')) {
                    e.clipboardData.setData('text/plain', '');
                    e.preventDefault();
                    return false;
                }
            });

            // Show warning message when trying to select text
            document.addEventListener('mousedown', function(e) {
                if (e.target.closest('.protected-content')) {
                    if (e.detail > 1) { // Double click or more
                        e.preventDefault();
                        return false;
                    }
                }
            });

            // Disable text selection on mobile
            document.addEventListener('touchstart', function(e) {
                if (e.target.closest('.protected-content')) {
                    if (e.touches.length > 1) {
                        e.preventDefault();
                    }
                }
            });

            // Additional protection: Clear selection
            setInterval(function() {
                if (window.getSelection) {
                    const selection = window.getSelection();
                    if (selection.rangeCount > 0) {
                        const range = selection.getRangeAt(0);
                        if (range.commonAncestorContainer.closest &&
                            range.commonAncestorContainer.closest('.protected-content')) {
                            selection.removeAllRanges();
                        }
                    }
                }
            }, 100);

        })();
    </script>

    <!-- Comments JavaScript -->
    <script>
        (function() {
            'use strict';

            let currentPage = 1;
            let isLoading = false;
            let hasMore = true;

            // DOM elements
            const commentForm = document.getElementById('commentForm');
            const commentsList = document.getElementById('commentsList');
            const loadMoreBtn = document.getElementById('loadMoreBtn');
            const submitBtn = document.getElementById('submitBtn');

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                loadComments();
                setupEventListeners();
            });

            function setupEventListeners() {
                // Comment form submission
                commentForm.addEventListener('submit', handleCommentSubmit);

                // Load more button
                loadMoreBtn.addEventListener('click', function() {
                    currentPage++;
                    loadComments();
                });
            }

            function handleCommentSubmit(e) {
                e.preventDefault();

                if (isLoading) return;

                const formData = new FormData(commentForm);
                const submitButton = e.target.querySelector('button[type="submit"]');

                // Clear previous errors
                clearFormErrors();

                // Show loading state
                setSubmitLoading(true);

                fetch(`{{ route('news.binh-luan.store', $tinTuc->DinhDanh) }}`, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute(
                                    'content') ||
                                document.querySelector('input[name="_token"]').value
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Show success message
                            showMessage('success', data.message);

                            // Reset form
                            commentForm.reset();

                            // Reload comments to show the new one (if approved)
                            currentPage = 1;
                            commentsList.innerHTML = '';
                            loadComments();
                        } else {
                            // Show validation errors
                            if (data.errors) {
                                showFormErrors(data.errors);
                            } else {
                                showMessage('error', data.message || 'Có lỗi xảy ra khi gửi bình luận.');
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showMessage('error', 'Có lỗi xảy ra khi gửi bình luận. Vui lòng thử lại.');
                    })
                    .finally(() => {
                        setSubmitLoading(false);
                    });
            }

            function loadComments() {
                if (isLoading) return;

                isLoading = true;

                // Show loading state
                if (currentPage === 1) {
                    commentsList.innerHTML =
                        '<div class="comments-loading"><i class="fas fa-spinner fa-spin me-2"></i>Đang tải bình luận...</div>';
                } else {
                    setLoadMoreLoading(true);
                }

                fetch(`{{ route('news.binh-luan.get', $tinTuc->DinhDanh) }}?page=${currentPage}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            if (currentPage === 1) {
                                commentsList.innerHTML = '';
                            }

                            if (data.data.length === 0 && currentPage === 1) {
                                commentsList.innerHTML =
                                    '<div class="comments-empty"><i class="fas fa-comments fa-2x mb-3"></i><p>Chưa có bình luận nào. Hãy là người đầu tiên bình luận!</p></div>';
                            } else {
                                data.data.forEach(comment => {
                                    appendComment(comment);
                                });
                            }

                            // Update pagination
                            hasMore = data.pagination.has_more;
                            loadMoreBtn.style.display = hasMore ? 'block' : 'none';
                        } else {
                            if (currentPage === 1) {
                                commentsList.innerHTML =
                                    '<div class="comments-empty"><i class="fas fa-exclamation-triangle fa-2x mb-3"></i><p>Không thể tải bình luận. Vui lòng thử lại.</p></div>';
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        if (currentPage === 1) {
                            commentsList.innerHTML =
                                '<div class="comments-empty"><i class="fas fa-exclamation-triangle fa-2x mb-3"></i><p>Có lỗi xảy ra khi tải bình luận.</p></div>';
                        }
                    })
                    .finally(() => {
                        isLoading = false;
                        setLoadMoreLoading(false);
                    });
            }

            function appendComment(comment) {
                const commentElement = document.createElement('div');
                commentElement.className = 'comment-item';
                commentElement.innerHTML = `
                    <div class="comment-author">${escapeHtml(comment.HoVaTen)}</div>
                    <div class="comment-date">${comment.NgayTao}</div>
                    <div class="comment-content">${escapeHtml(comment.BinhLuan)}</div>
                `;
                commentsList.appendChild(commentElement);
            }

            function setSubmitLoading(loading) {
                isLoading = loading;
                submitBtn.disabled = loading;

                if (loading) {
                    submitBtn.innerHTML =
                        '<span class="spinner-border spinner-border-sm me-2" role="status"></span>Đang gửi...';
                } else {
                    submitBtn.innerHTML = '<i class="fas fa-paper-plane me-2"></i>Gửi bình luận';
                }
            }

            function setLoadMoreLoading(loading) {
                loadMoreBtn.disabled = loading;

                if (loading) {
                    loadMoreBtn.innerHTML =
                        '<span class="spinner-border spinner-border-sm me-2" role="status"></span>Đang tải...';
                } else {
                    loadMoreBtn.innerHTML = '<i class="fas fa-chevron-down me-2"></i>Xem thêm bình luận';
                }
            }

            function showMessage(type, message) {
                // Create alert element
                const alertElement = document.createElement('div');
                alertElement.className =
                    `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
                alertElement.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                // Insert before comment form
                const commentFormSection = document.querySelector('.comment-form-section');
                commentFormSection.parentNode.insertBefore(alertElement, commentFormSection);

                // Auto dismiss after 5 seconds
                setTimeout(() => {
                    if (alertElement.parentNode) {
                        alertElement.remove();
                    }
                }, 5000);
            }

            function showFormErrors(errors) {
                Object.keys(errors).forEach(field => {
                    const input = document.getElementById(field);
                    const feedback = input.nextElementSibling;

                    input.classList.add('is-invalid');
                    if (feedback && feedback.classList.contains('invalid-feedback')) {
                        feedback.textContent = errors[field][0];
                    }
                });
            }

            function clearFormErrors() {
                const inputs = commentForm.querySelectorAll('.form-control');
                inputs.forEach(input => {
                    input.classList.remove('is-invalid');
                    const feedback = input.nextElementSibling;
                    if (feedback && feedback.classList.contains('invalid-feedback')) {
                        feedback.textContent = '';
                    }
                });
            }

            function escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

        })();
    </script>

    <!-- Floating Share Buttons -->
    <div class="floating-share-buttons">
        <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(request()->fullUrl()) }}" target="_blank"
            class="floating-share-btn facebook" title="Chia sẻ lên Facebook">
            <i class="fab fa-facebook-f"></i>
        </a>
        <a href="https://twitter.com/intent/tweet?url={{ urlencode(request()->fullUrl()) }}&text={{ urlencode($tinTuc->TieuDe) }}"
            target="_blank" class="floating-share-btn twitter" title="Chia sẻ lên Twitter">
            <i class="fab fa-twitter"></i>
        </a>
        <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ urlencode(request()->fullUrl()) }}"
            target="_blank" class="floating-share-btn linkedin" title="Chia sẻ lên LinkedIn">
            <i class="fab fa-linkedin-in"></i>
        </a>
        <button type="button" class="floating-share-btn copy" title="Sao chép liên kết"
            onclick="copyToClipboard()">
            <i class="fas fa-link"></i>
        </button>
    </div>

    <!-- Copy to clipboard script -->
    <script>
        function copyToClipboard() {
            const url = window.location.href;

            if (navigator.clipboard && window.isSecureContext) {
                // Use modern clipboard API
                navigator.clipboard.writeText(url).then(() => {
                    showCopyMessage('Đã sao chép liên kết!');
                }).catch(() => {
                    fallbackCopyTextToClipboard(url);
                });
            } else {
                // Fallback for older browsers
                fallbackCopyTextToClipboard(url);
            }
        }

        function fallbackCopyTextToClipboard(text) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";
            textArea.style.opacity = "0";

            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    showCopyMessage('Đã sao chép liên kết!');
                } else {
                    showCopyMessage('Không thể sao chép liên kết');
                }
            } catch (err) {
                showCopyMessage('Không thể sao chép liên kết');
            }

            document.body.removeChild(textArea);
        }

        function showCopyMessage(message) {
            // Create temporary message
            const messageDiv = document.createElement('div');
            messageDiv.textContent = message;
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #28a745;
                color: white;
                padding: 10px 15px;
                border-radius: 5px;
                z-index: 10000;
                font-size: 14px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            `;

            document.body.appendChild(messageDiv);

            // Remove after 3 seconds
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 3000);
        }
    </script>

</body>

</html>
