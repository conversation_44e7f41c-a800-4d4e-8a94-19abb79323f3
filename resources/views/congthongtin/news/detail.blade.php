<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $tinTuc->TieuDe }} - Tin tức</title>
    <meta name="description" content="{{ $tinTuc->NoiDungTomTat }}">
    <meta name="keywords" content="{{ $tinTuc->TuKhoa }}">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
        }

        .news-header {
            background: url('{{ asset('assets/congthongtin/bg-page.png') }}') center/cover no-repeat;
            min-height: 500px;
            color: white;
            padding: 100px 0;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            position: relative;
        }

        .news-content {
            padding: 40px 0;
        }

        .news-meta {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }

        .news-meta .meta-item {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 10px;
        }

        .news-meta .meta-item i {
            color: #2563eb;
            margin-right: 5px;
        }

        .news-body {
            font-size: 16px;
            line-height: 1.8;
        }

        .news-body h3 {
            color: #2563eb;
            margin-top: 30px;
            margin-bottom: 15px;
        }

        .news-body ul {
            padding-left: 20px;
        }

        .news-body li {
            margin-bottom: 8px;
        }



        .breadcrumb {
            background: none;
            padding: 0;
            margin-bottom: 20px;
            justify-content: center;
        }

        .breadcrumb-item a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
        }

        .breadcrumb-item a:hover {
            color: white;
        }

        .breadcrumb-item.active {
            color: rgba(255, 255, 255, 0.6);
        }

        .back-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            border-color: rgba(255, 255, 255, 0.5);
        }

        .category-badge {
            background: rgba(255, 255, 255, 0.2);
            text-align: center;
            width: 100px;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            display: block;
            margin: 0 auto 15px auto;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .news-subtitle {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.9);
            margin-top: 20px;
            line-height: 1.6;
            font-weight: 400;
            text-align: center;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Content Protection Styles */
        .protected-content {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-touch-callout: none;
            -webkit-tap-highlight-color: transparent;
            pointer-events: auto;
        }

        .protected-content::selection {
            background: transparent;
        }

        .protected-content::-moz-selection {
            background: transparent;
        }

        /* Disable drag for images */
        .protected-content img {
            -webkit-user-drag: none;
            -khtml-user-drag: none;
            -moz-user-drag: none;
            -o-user-drag: none;
            user-drag: none;
            pointer-events: none;
        }

        /* Overlay protection for critical content */
        .content-overlay {
            position: relative;
        }

        .content-overlay::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
            pointer-events: none;
        }

        /* Disable print styles */
        @media print {
            .protected-content {
                display: none !important;
            }

            .print-warning {
                display: block !important;
                text-align: center;
                font-size: 18px;
                color: #333;
                padding: 50px;
            }
        }

        .print-warning {
            display: none;
        }

        /* Disable F12 warning */
        .dev-tools-warning {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            font-size: 24px;
            text-align: center;
        }
    </style>
</head>

<body class="has-absolute-header">
    @include('congthongtin.components.header')

    <div class="news-header">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('home') }}">Trang chủ</a></li>
                <li class="breadcrumb-item"><a href="{{ route('news.index') }}">Tin tức & Sự kiện</a></li>
                <li class="breadcrumb-item active" aria-current="page">Chi tiết tin tức</li>
            </ol>
        </nav>

        <h2 style="text-transform: capitalize; font-weight: 700; color: #fff;">{{ $tinTuc->TieuDe }}</h2>

        @if ($tinTuc->NoiDungTomTat)
            <p class="news-subtitle">{{ $tinTuc->NoiDungTomTat }}</p>
        @endif

        <div class="category-badge">
            {{ $tinTuc->loaiTinTuc->TenLoaiTinTuc ?? 'Tin tức' }}
        </div>
    </div>

    <!-- Content -->
    <div class="news-content">
        <div class="container">
            <div class="row">
                <!-- News Meta -->
                <div class="news-meta">
                    <div class="meta-item">
                        <i class="fas fa-user"></i>
                        <strong>Tác giả:</strong> {{ $tinTuc->NguoiTao }}
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-calendar"></i>
                        <strong>Ngày đăng:</strong> {{ $tinTuc->formatted_ngay_tao }}
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-eye"></i>
                        <strong>Lượt xem:</strong> {{ number_format($tinTuc->LuotXem) }}
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-tags"></i>
                        <strong>Từ khóa:</strong> {{ $tinTuc->TuKhoa }}
                    </div>
                </div>



                <!-- News Image -->
                @if ($tinTuc->HinhAnh)
                    <div class="text-center mb-4 protected-content">
                        @if (str_starts_with($tinTuc->HinhAnh, 'http'))
                            <img src="{{ $tinTuc->HinhAnh }}" alt="{{ $tinTuc->TieuDe }}" class="img-fluid rounded"
                                loading="lazy" draggable="false" oncontextmenu="return false;">
                        @else
                            <img src="{{ asset($tinTuc->HinhAnh) }}" alt="{{ $tinTuc->TieuDe }}"
                                class="img-fluid rounded" loading="lazy" draggable="false"
                                oncontextmenu="return false;">
                        @endif
                    </div>
                @endif

                <!-- News Body -->
                <div class="news-body protected-content content-overlay">
                    {!! $tinTuc->NoiDung !!}
                </div>

                <!-- Share buttons -->
                <div class="mt-4 pt-4 border-top">
                    <h5>Chia sẻ bài viết:</h5>
                    <div class="d-flex gap-2">
                        <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(request()->fullUrl()) }}"
                            target="_blank" class="btn btn-primary btn-sm">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://twitter.com/intent/tweet?url={{ urlencode(request()->fullUrl()) }}&text={{ urlencode($tinTuc->TieuDe) }}"
                            target="_blank" class="btn btn-info btn-sm">
                            <i class="fab fa-twitter"></i> Twitter
                        </a>
                        <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ urlencode(request()->fullUrl()) }}"
                            target="_blank" class="btn btn-secondary btn-sm">
                            <i class="fab fa-linkedin-in"></i> LinkedIn
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Related News -->
    <div class="container px-3" style="overflow-x: hidden; background-color: #fff">
        @if ($tinTucLienQuan->count() > 0)
            @include('congthongtin.components.related-news', ['tinTucNoiBat' => $tinTucLienQuan])
        @endif
    </div>

    <!-- Developer Tools Warning -->
    <div class="dev-tools-warning" id="devToolsWarning">
        <div>
            <h3>⚠️ Cảnh báo bảo mật</h3>
            <p>Việc sử dụng Developer Tools để copy nội dung là không được phép!</p>
            <p>Vui lòng đóng Developer Tools để tiếp tục.</p>
        </div>
    </div>

    <!-- Print Warning -->
    <div class="print-warning">
        <h2>⚠️ Nội dung được bảo vệ</h2>
        <p>Nội dung này không thể in được do chính sách bảo vệ bản quyền.</p>
    </div>

    @include('congthongtin.components.footer')

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Content Protection Script -->
    <script>
        (function() {
            'use strict';

            // Disable right-click context menu
            document.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                return false;
            });

            // Disable text selection with mouse
            document.addEventListener('selectstart', function(e) {
                if (e.target.closest('.protected-content')) {
                    e.preventDefault();
                    return false;
                }
            });

            // Disable drag start
            document.addEventListener('dragstart', function(e) {
                if (e.target.closest('.protected-content')) {
                    e.preventDefault();
                    return false;
                }
            });

            // Disable common keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Disable Ctrl+A (Select All)
                if (e.ctrlKey && e.keyCode === 65) {
                    e.preventDefault();
                    return false;
                }

                // Disable Ctrl+C (Copy)
                if (e.ctrlKey && e.keyCode === 67) {
                    e.preventDefault();
                    return false;
                }

                // Disable Ctrl+V (Paste)
                if (e.ctrlKey && e.keyCode === 86) {
                    e.preventDefault();
                    return false;
                }

                // Disable Ctrl+X (Cut)
                if (e.ctrlKey && e.keyCode === 88) {
                    e.preventDefault();
                    return false;
                }

                // Disable Ctrl+S (Save)
                if (e.ctrlKey && e.keyCode === 83) {
                    e.preventDefault();
                    return false;
                }

                // Disable Ctrl+P (Print)
                if (e.ctrlKey && e.keyCode === 80) {
                    e.preventDefault();
                    alert('Chức năng in đã bị vô hiệu hóa để bảo vệ nội dung.');
                    return false;
                }

                // Disable F12 (Developer Tools)
                if (e.keyCode === 123) {
                    e.preventDefault();
                    return false;
                }

                // Disable Ctrl+Shift+I (Developer Tools)
                if (e.ctrlKey && e.shiftKey && e.keyCode === 73) {
                    e.preventDefault();
                    return false;
                }

                // Disable Ctrl+Shift+J (Console)
                if (e.ctrlKey && e.shiftKey && e.keyCode === 74) {
                    e.preventDefault();
                    return false;
                }

                // Disable Ctrl+U (View Source)
                if (e.ctrlKey && e.keyCode === 85) {
                    e.preventDefault();
                    return false;
                }

                // Disable Ctrl+Shift+C (Inspect Element)
                if (e.ctrlKey && e.shiftKey && e.keyCode === 67) {
                    e.preventDefault();
                    return false;
                }
            });

            // Detect Developer Tools
            let devtools = {
                open: false,
                orientation: null
            };

            const threshold = 160;

            setInterval(function() {
                if (window.outerHeight - window.innerHeight > threshold ||
                    window.outerWidth - window.innerWidth > threshold) {
                    if (!devtools.open) {
                        devtools.open = true;
                        document.getElementById('devToolsWarning').style.display = 'flex';
                        console.clear();
                        console.log('%cCảnh báo!', 'color: red; font-size: 50px; font-weight: bold;');
                        console.log('%cViệc sử dụng Developer Tools để copy nội dung là không được phép!',
                            'color: red; font-size: 16px;');
                    }
                } else {
                    if (devtools.open) {
                        devtools.open = false;
                        document.getElementById('devToolsWarning').style.display = 'none';
                    }
                }
            }, 500);

            // Clear console periodically
            setInterval(function() {
                console.clear();
            }, 1000);

            // Disable image saving
            document.addEventListener('DOMContentLoaded', function() {
                const images = document.querySelectorAll('img');
                images.forEach(function(img) {
                    img.addEventListener('dragstart', function(e) {
                        e.preventDefault();
                    });

                    img.addEventListener('contextmenu', function(e) {
                        e.preventDefault();
                    });
                });
            });

            // Disable print
            window.addEventListener('beforeprint', function(e) {
                e.preventDefault();
                alert('Chức năng in đã bị vô hiệu hóa để bảo vệ nội dung.');
                return false;
            });

            // Override print function
            window.print = function() {
                alert('Chức năng in đã bị vô hiệu hóa để bảo vệ nội dung.');
                return false;
            };

            // Disable copy via clipboard API
            document.addEventListener('copy', function(e) {
                if (e.target.closest('.protected-content')) {
                    e.clipboardData.setData('text/plain', '');
                    e.preventDefault();
                    return false;
                }
            });

            // Show warning message when trying to select text
            document.addEventListener('mousedown', function(e) {
                if (e.target.closest('.protected-content')) {
                    if (e.detail > 1) { // Double click or more
                        e.preventDefault();
                        return false;
                    }
                }
            });

            // Disable text selection on mobile
            document.addEventListener('touchstart', function(e) {
                if (e.target.closest('.protected-content')) {
                    if (e.touches.length > 1) {
                        e.preventDefault();
                    }
                }
            });

            // Additional protection: Clear selection
            setInterval(function() {
                if (window.getSelection) {
                    const selection = window.getSelection();
                    if (selection.rangeCount > 0) {
                        const range = selection.getRangeAt(0);
                        if (range.commonAncestorContainer.closest &&
                            range.commonAncestorContainer.closest('.protected-content')) {
                            selection.removeAllRanges();
                        }
                    }
                }
            }, 100);

        })();
    </script>

</body>

</html>
