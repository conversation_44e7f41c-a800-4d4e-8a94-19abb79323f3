<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tin tức - <PERSON>h sách tin tức</title>
    <meta name="description" content="<PERSON>h sách tin tức mới nhất">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #fff;
        }

        .news-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
        }

        .news-card {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin-bottom: 30px;
            cursor: pointer;
            width: 370px;
            height: 500px;
        }

        .news-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .news-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .news-card .card-body {
            padding: 20px;
        }

        .news-card .card-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
            line-height: 1.4;
        }

        .news-card .card-text {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .news-card .card-meta {
            font-size: 12px;
            color: #999;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .category-badge {
            background: #667eea;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            display: inline-block;
            margin-bottom: 10px;
        }

        .breadcrumb {
            background: none;
            padding: 0;
            margin-bottom: 20px;
        }

        .breadcrumb-item a {
            color: #667eea;
            text-decoration: none;
        }

        .breadcrumb-item.active {
            color: #6c757d;
        }

        .back-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 20px;
            transition: background 0.3s ease;
        }

        .back-btn:hover {
            background: #5a6fd8;
            color: white;
        }

        .search-form {
            margin-bottom: 30px;
            width: 60%;
            margin-left: auto;
            margin-right: auto;
        }

        .filter-section {
            background: white;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .pagination {
            justify-content: center;
        }

        .views-count {
            color: #28a745;
            font-weight: 600;
        }

        .featured-badge {
            background: #ffc107;
            color: #000;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            margin-left: 10px;
        }

        .news-header {
            background: url('{{ asset('assets/congthongtin/bg-page.png') }}') center bottom/cover no-repeat;

            min-height: 400px;
            color: white;
            padding: 100px 0;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            position: relative;
        }

        .breadcrumb {
            background: none;
            padding: 0;
            margin-bottom: 20px;
            justify-content: center;
        }

        .breadcrumb-item a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
        }

        .breadcrumb-item a:hover {
            color: white;
        }

        .breadcrumb-item.active {
            color: rgba(255, 255, 255, 0.6);
        }

        /* Filter Dropdown Styles */
        .filter-dropdown {
            margin-top: 20px;
        }

        .filter-dropdown .dropdown-toggle {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px 16px;
            font-weight: 500;
            text-align: left;
            position: relative;
        }

        .filter-dropdown .dropdown-toggle:hover {
            border-color: #667eea;
            background-color: #f8f9fa;
            color: #667eea;
            /* Thêm màu chữ xanh khi hover */
        }

        .filter-dropdown .dropdown-menu {
            border: none;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            padding: 8px 0;
            max-height: 300px;
            overflow-y: auto;
        }

        .filter-dropdown .dropdown-item {
            padding: 10px 16px;
            border-radius: 0;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .filter-dropdown .dropdown-item:hover {
            background-color: #f8f9fa;
            color: #667eea;
        }

        .filter-dropdown .dropdown-item.active {
            background-color: #667eea;
            color: white;
        }

        .filter-dropdown .dropdown-item.active .badge {
            background-color: rgba(255, 255, 255, 0.2) !important;
        }

        .filter-dropdown .badge {
            font-size: 0.75rem;
            padding: 4px 8px;
        }

        /* Ẩn thông tin phân trang mặc định */
        .pagination-container nav>div:first-child {
            display: none !important;
        }

        /* Ẩn tất cả các phần tử có chứa text "Showing" trong pagination */
        .pagination-container nav div[role="status"] {
            display: none !important;
        }

        .pagination-container nav .hidden {
            display: none !important;
        }

        .pagination-container nav p {
            display: none !important;
        }

        /* Custom Pagination Styles */
        .pagination-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 2rem;
            width: 100%;
            text-align: center;
        }

        .pagination-wrapper {
            margin-bottom: 15px;
        }

        .pagination-info {
            color: #6c757d;
            font-size: 14px;
            margin-top: 10px;
        }

        .pagination {
            justify-content: center;
            display: flex;
            align-items: center;
        }

        .pagination .page-item .page-link {
            color: #667eea;
            border: 1px solid #dee2e6;
            padding: 0.5rem 0.75rem;
            margin: 0 3px;
            border-radius: 5px;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .pagination .page-item.active .page-link {
            background-color: #667eea;
            border-color: #667eea;
            color: white;
        }

        .pagination .page-item .page-link:hover {
            background-color: #f8f9fa;
            color: #5a6fd8;
            border-color: #dee2e6;
        }

        .pagination .page-item.disabled .page-link {
            color: #6c757d;
            pointer-events: none;
            background-color: #fff;
            border-color: #dee2e6;
        }

        /* Fix for arrow icons */
        .pagination .page-link[rel="prev"],
        .pagination .page-link[rel="next"] {
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>

<body>
    @include('congthongtin.components.header')

    <div class="news-header">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('home') }}">Trang chủ</a></li>
                <li class="breadcrumb-item active"><a href="{{ route('news.index') }}">Tin tức & Sự kiện</a></li>
            </ol>
        </nav>

        <h2 style="text-transform: capitalize; font-weight: 700; color: #fff;">Tin tức & Sự kiện</h2>

    </div>

    <!-- Content -->
    <div class="container my-5">
        <div class="row">
            <!-- Main Content -->
            <div class="col-12">
                <!-- Search Form -->
                <div class="search-form">
                    <form method="GET" action="{{ route('news.index') }}">
                        <div class="row g-2">
                            <div class="col-md-9">
                                <input type="text" name="search" class="form-control"
                                    placeholder="Tìm kiếm tin tức..." value="{{ request('search') }}">
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search"></i> Tìm kiếm
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Category Filter Dropdown -->
                <div class="filter-dropdown mb-4">
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle min-w-30" type="button"
                            id="categoryDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-filter me-2"></i>
                            @if (request('loai'))
                                @php
                                    $currentCategory = $loaiTinTucs->firstWhere('DinhDanh', request('loai'));
                                @endphp
                                {{ $currentCategory ? $currentCategory->TenLoaiTinTuc : 'Lọc theo danh mục' }}
                            @else
                                Lọc theo danh mục
                            @endif
                            <span class="badge bg-primary ms-2">{{ $tinTucs->total() }}</span>
                        </button>
                        <ul class="dropdown-menu w-25 mt-2" aria-labelledby="categoryDropdown">
                            <li>
                                <a class="dropdown-item {{ !request('loai') ? 'active' : '' }}"
                                    href="{{ route('news.index', array_merge(request()->query(), ['loai' => null])) }}">
                                    <i class="fas fa-list me-2"></i>
                                    Tất cả tin tức
                                    <span
                                        class="badge bg-secondary float-end">{{ $loaiTinTucs->sum(function ($loai) {return $loai->tinTucs->count();}) }}</span>
                                </a>
                            </li>
                            <li>
                                <hr class="dropdown-divider">
                            </li>
                            @foreach ($loaiTinTucs as $loai)
                                <li>
                                    <a class="dropdown-item {{ request('loai') == $loai->DinhDanh ? 'active' : '' }}"
                                        href="{{ route('news.index', array_merge(request()->query(), ['loai' => $loai->DinhDanh])) }}">
                                        <i class="fas fa-tag me-2"></i>
                                        {{ $loai->TenLoaiTinTuc }}
                                        <span class="badge bg-secondary float-end">{{ $loai->tinTucs->count() }}</span>
                                    </a>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                </div>

                <!-- News List -->
                <div class="row">
                    @forelse($tinTucs as $tinTuc)
                        <div class="col-lg-4 col-md-6">
                            <div class="news-card"
                                onclick="window.location.href='{{ route('news.detail', $tinTuc->DinhDanh) }}'">
                                @if ($tinTuc->HinhAnh)
                                    @if (str_starts_with($tinTuc->HinhAnh, 'http'))
                                        <img src="{{ $tinTuc->HinhAnh }}" alt="{{ $tinTuc->TieuDe }}" loading="lazy"
                                            onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                        <div
                                            style="height: 200px; background: #f8f9fa; display: none; align-items: center; justify-content: center;">
                                            <i class="fas fa-image fa-3x text-muted"></i>
                                        </div>
                                    @else
                                        <img src="{{ asset($tinTuc->HinhAnh) }}" alt="{{ $tinTuc->TieuDe }}"
                                            loading="lazy"
                                            onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                        <div
                                            style="height: 200px; background: #f8f9fa; display: none; align-items: center; justify-content: center;">
                                            <i class="fas fa-image fa-3x text-muted"></i>
                                        </div>
                                    @endif
                                @else
                                    <div
                                        style="height: 200px; background: #f8f9fa; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-image fa-3x text-muted"></i>
                                    </div>
                                @endif

                                <div class="card-body">
                                    <div class="category-badge">
                                        {{ $tinTuc->loaiTinTuc->TenLoaiTinTuc ?? 'Tin tức' }}
                                    </div>
                                    @if ($tinTuc->LaNoiBat)
                                        <span class="featured-badge">Nổi bật</span>
                                    @endif

                                    <h5 class="card-title">{{ $tinTuc->TieuDe }}</h5>
                                    <p class="card-text">{{ Str::limit($tinTuc->NoiDungTomTat, 120) }}</p>

                                    <div class="card-meta">
                                        <div>
                                            <i class="fas fa-user"></i> {{ $tinTuc->NguoiTao }}
                                            <span class="ms-2">
                                                <i class="fas fa-calendar"></i> {{ $tinTuc->formatted_ngay_tao }}
                                            </span>
                                        </div>
                                        <div class="views-count">
                                            <i class="fas fa-eye"></i> {{ number_format($tinTuc->LuotXem) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="col-12">
                            <div class="text-center py-5">
                                <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                                <h4>Không có tin tức nào</h4>
                                <p class="text-muted">Hiện tại chưa có tin tức nào được đăng tải.</p>
                            </div>
                        </div>
                    @endforelse
                </div>

                <!-- Pagination -->
                @if ($tinTucs->hasPages())
                    <div class="pagination-container">
                        <div class="pagination-wrapper">
                            {{ $tinTucs->appends(request()->query())->links('pagination::bootstrap-5') }}
                        </div>
                        <div class="pagination-info">
                            Showing {{ $tinTucs->firstItem() }} to {{ $tinTucs->lastItem() }} of
                            {{ $tinTucs->total() }} results
                        </div>
                    </div>
                @endif
            </div>


        </div>
    </div>

    @include('congthongtin.components.footer')
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>
