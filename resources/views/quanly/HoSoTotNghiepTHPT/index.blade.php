@extends('layouts.layouts')

{{-- tiêu đề trang --}}
@section('title', '<PERSON><PERSON> sơ tốt nghiệp THPT')
@push('scripts')
<link rel="stylesheet" href="{{ asset('css/quanly/hosototnghiep.css') }}">
@endpush
@section('content')
@php
$filters = [
// Từ ngày / Đến ngày
'<div class="row mb-3">
    <div class="col-md-6">
        <label class="form-label" for="TuNgay_Loc">Từ ngày</label>
        <div class="input-group input-group-sm">

            <input type="text" class="form-control input-sm date-picker" id="TuNgay_Loc" placeholder="dd/MM/yyyy"
                autocomplete="off">
        </div>
    </div>
    <div class="col-md-6">
        <label class="form-label" for="DenNgay_Loc"><PERSON><PERSON><PERSON> ngà<PERSON></label>
        <div class="input-group input-group-sm">
            <input type="text" class="form-control input-sm date-picker" id="DenNgay_Loc" placeholder="dd/MM/yyyy"
                autocomplete="off">
        </div>
    </div>
</div>',

// Cấp học (dropdown)
'<div class="row mb-3">
    <div class="col-md-12">
        <label class="form-label" for="CapHoc_Loc">Cấp học</label>
        <select class="form-select input-sm" id="CapHoc_Loc">
            <option value="">-- Tất cả --</option>
            <!-- JS sẽ populate options động -->
        </select>
    </div>
</div>',

// Trạng thái (dropdown)
'<div class="row mb-3">
    <div class="col-md-12">
        <label class="form-label" for="TrangThai_Loc">Trạng thái</label>
        <select class="form-select input-sm" id="TrangThai_Loc">
            <option value="">-- Tất cả --</option>
            <!-- JS sẽ populate options động -->
        </select>
    </div>
</div>'
];

@endphp
@include('partials.filter-panel', [
'filters' => $filters,
'actions' => null,
'showTimKiemNangCao' => true,'showBulkActions' => false,
])


<div class="row" style="margin-top: 4px; padding-left: 0;">
    <div class="col-md-12 px-0">
        <div id="Grid1"></div>
    </div>
</div>

<!-- MODAL THÊM MỚI -->
<div id="mdThemMoi" class="full-screen-panel" style="display:none;">
    <div class="panel-content">
        <div class="modal-content d-flex flex-colum" style="border-radius:0; height:100vh;">
            <div class="modal-header modal-title font-weight-bold"
                style="border-radius: 0 !important; background-color : white !important; color:gray !important; text-transform:uppercase">
                <h5 class="modal-title text-nts-success font-weight-bold" id="lblTieuDeMultiStep">Thêm mới quyết định
                    công nhận tốt
                    nghiệp
                </h5>
                <button type="button" class="btn-close my-0 mx-3 d-flex align-items-center justify-content-center"
                    data-bs-dismiss="modal" aria-label="Close"
                    style="width:32px; height:32px; padding:0; font-size:1.2rem; color:gray !important; font-weight:bold;">
                    <i class="fa fa-times" aria-hidden="true"></i>
                </button>
            </div>

            <div class="modal-body  flex-fill" style="background:white;">
                <div class="row p-1" style="height:100%">
                    <div class="col-md-10" style="height:100%" id="step-content">
                        <input type="hidden" id="HoSoTotNghiepID" value="" />
                        <div id="step-1-content" class="box-shadow-primary border border-primary"
                            style="padding: 10px;padding-top:0; border-radius: 4px; height: 100%;">
                            <div class="row mt-2 box-hdsd"
                                style="background-color: white; border-radius: 8px; padding: 10px; padding-top:0; height:100%;">
                                <div class="col-md-4"
                                    style="display: flex; align-content: stretch; justify-content: flex-start; flex-wrap: wrap; align-items: center;">
                                    <img alt="" src="{{ asset('img/ThemMoiHoSo.jpg') }}?v={{ time() }}"
                                        style="width:100%" />
                                </div>
                                <div class="col-md-8 d-flex flex-column" style="height:100%">
                                    <fieldset class="KhungVien flex-grow-1 d-flex flex-column" id="fieldThongTinChung">
                                        <legend>Thông tin chung</legend>
                                        <!-- SDQ, NguoiKy, ChucVu -->
                                        <div class="row mb-2">
                                            <div class="col-md-3">
                                                <label for="SoQuyetDinh">Số quyết định</label>
                                                <input type="text" class="form-control" id="SoQuyetDinh"
                                                    autocomplete="off" required>
                                            </div>
                                            <div class="col-md-3">
                                                <label for="NguoiKy">Người ký</label>
                                                <input type="text" class="form-control" id="NguoiKy" autocomplete="off">
                                            </div>
                                            <div class="col-md-3">
                                                <label for="ChucVu">Chức vụ</label>
                                                <select class="form-control" id="ChucVu">
                                                    <option>Trực tiếp</option>
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                <label for="NgayKy">Ngày ký</label>
                                                <input type="text" class="form-control date-picker" id="NgayKy"
                                                    name="NgayKy" autocomplete="off" data-date-format="dd/mm/yyyy"
                                                    placeholder="dd/MM/yyyy">
                                            </div>
                                        </div>
                                        <div class="row mb-2">
                                            <div class="col-md-12">
                                                <label for="CoQuanBanHanh">Cơ quan ban hành</label>
                                                <input type="text" class="form-control" id="CoQuanBanHanh"
                                                    autocomplete="off">
                                            </div>
                                        </div>
                                        <div class="row mb-2">
                                            <div class="col-md-4">
                                                <label for="KyThi">Kỳ thi</label>
                                                <select class="form-control" id="KyThi">
                                                </select>
                                            </div>
                                            <div class="col-md-4">
                                                <label for="HoiDong">Hội đồng</label>
                                                <select class="form-control" id="HoiDong">
                                                </select>
                                            </div>
                                            <div class="col-md-4">
                                                <label for="HinhThucDaoTao">Hình thức đào tạo</label>
                                                <select class="form-control" id="HinhThucDaoTao">
                                                </select>
                                            </div>

                                        </div>
                                        <!-- <div class="row mb-2">
                                            <div class="col-md-12">
                                                <label for="HoiDong">Hội đồng</label>
                                                <select class="form-control" id="HoiDong">
                                                </select>
                                            </div>
                                        </div> -->
                                        <div class="row mb-2">
                                            <div class="col-md-12">
                                                <label for="DonVi">Đơn vị</label>
                                                <div id="gridDonVi"></div>
                                            </div>
                                        </div>
                                        <div class="row mb-2">
                                            <div class="col-md-12">
                                                <label for="TrichYeu">Trích yếu</label>
                                                <textarea class="form-control" id="TrichYeu" rows="2"></textarea>
                                            </div>
                                        </div>
                                        <div class="row mb-2">
                                            <div class="col-md-12">
                                                @include('partials.dinh-kem', [
                                                'id' => 'dinhKemQD',
                                                ])
                                            </div>
                                        </div>

                                    </fieldset>
                                    <div class="row justify-content-end">
                                        <div class="col-auto">
                                            <a href="#" id="btnTiepTuc" class="btn nts-color-luu btn-luuvadong">
                                                <img src="{{ asset('img/next-step.svg') }}?v={{ time() }}" />&ensp;Tiếp
                                                tục
                                                (F9)
                                            </a>
                                        </div>
                                    </div>

                                </div> <!-- /col-md-9 content area -->
                            </div>
                        </div>

                        <!-- STEP 2 CONTENT -->
                        <div id="step-2-content" class="border border-primary p-2" style="display:none">

                            <div class="row my-2">
                                <div class="d-flex flex-column">
                                    <!-- Alert -->
                                    <div class="alert alert-warning py-2 mb-2 d-flex align-items-center" style="
        background: #FFF6EB; 
        border: 1px solid #FF9E0D; 
        border-left: 8px solid #FF9E0D;;
        border-radius: 4px;
        font-size: 13.5px; 
        color: #332A0B;
        box-shadow: 0 2px 4px rgba(255, 158, 13, 0.2);
        line-height: 1.3;
        ">
                                        <i class="fa fa-info-circle me-2" style="font-size: 17px; color: #FF9E0D;"></i>
                                        <span id="msgCapNhatDSHS"
                                            style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
                                            Bạn đang thực hiện cập nhật danh sách học sinh/sinh viên được công nhận tốt
                                            nghiệp tại quyết định số:
                                            <strong style="color:#5F3300;">QD-1004</strong> được ký ngày
                                            <strong style="color:#5F3300;">3/5/2024</strong> của
                                            <strong style="color:#5F3300;">Sở Giáo Dục</strong> về việc công nhận học
                                            sinh sinh viên tốt nghiệp THPT năm học 2024
                                        </span>
                                    </div>

                                    <!-- Search and Add -->
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div class="input-icon" style="max-width:340px;">
                                            <input type="text" class="form-control" placeholder="Nội dung tìm kiếm …"
                                                id="searchContent" autocomplete="off">
                                            <span class="input-icon-addon"><i class="fa fa-search"></i></span>
                                        </div>
                                        <!-- <input type="text" id="searchContent" class="form-control w-50"
                                            placeholder="Nội dung tìm kiếm"> -->
                                        <div class="btn-group ms-2">
                                            <button type="button" class="btn btn-warning dropdown-toggle"
                                                id="dropdownThemMoiHS" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="fa fa-plus"></i>&nbsp;Thêm mới&nbsp;&nbsp;<i
                                                    class="fa fa-caret-down"></i>
                                            </button>
                                            <ul class="dropdown-menu" aria-labelledby="dropdownThemMoiHS">
                                                <li>
                                                    <a class="dropdown-item" href="#" id="btnThemMoiHS">
                                                        <i class="fa fa-plus text-nts-primary me-2 "></i>Nhập trực tiếp
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="#" id="btnNhapNhieuHS">
                                                        <i class="fa fa-list-ul me-2 text-nts-primary"></i>Nhập nhiều
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="#" id="btnNhanExcelHS">
                                                        <i class="fa fa-file-excel-o me-2 text-nts-primary"></i>Nhập
                                                        excel
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>

                                    <!-- Tabulator Grid -->
                                    <div id="tabGridHocSinh" class="mb-3"></div>

                                    <!-- Footer Buttons -->
                                    <div class="d-flex justify-content-end mt-3 gap-2">
                                        <button id="btnQuayLaiBuoc1"
                                            class="btn nts-color-canhbao d-flex align-items-center">
                                            <img src="/img/icon_QuayLai.svg" alt="Quay lại (F8)"
                                                style="height:20px;" />&nbsp;Quay lại (F8)
                                        </button>
                                        <button id="btnKetThuc" class="btn nts-color-luu d-flex align-items-center">
                                            <img src="/img/icon_KetThuc.svg" alt="Kết thúc (F9)"
                                                style="height:20px;" />&nbsp;Kết thúc (F9)
                                        </button>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- SIDEBAR (always visible) -->
                    <div class="col-md-2 d-flex flex-column" style="height:100%">
                        <div class="row flex-fill">
                            <div class="box-shadow-primary border border-primary"
                                style="padding: 10px; border-radius: 4px; height: 100%;">
                                <div class="row mt-2 box-hdsd"
                                    style="background-color: white; border-radius: 8px; padding: 10px;">
                                    <ul class="steps steps-counter steps-vertical" style="height:auto">
                                        <h3 class="text-nts-primary"
                                            style="margin-bottom:0px;text-align:center; font-weight:bold; font-size:1.25rem">
                                            CÁC
                                            BƯỚC
                                            THỰC HIỆN</h3>
                                        <li class="step-item" id="sidebar-step-1">
                                            <div class="step-title font-weight-bold h4 m-0">Thông tin chung</div>
                                            <div class="step-desc">Cập nhật các thông tin như: Số quyết định, ngày
                                                ký, người ký, hội đồng công nhận tốt nghiệp, trường,...</div>
                                            <img src="{{ asset('img/Nen.png') }}?v={{ time() }}"
                                                style="max-width: 43%;">
                                        </li>
                                        <li class="step-item" id="sidebar-step-2">
                                            <div class="step-title h4 m-0 font-weight-bold">Nhập danh sách học sinh/sinh
                                                viên</div>
                                            <div class="step-desc">Nhập danh sách học sinh/sinh viên được công nhận tốt
                                                nghiệp và kết quả tốt nghiệp</div>
                                            <img src="{{ asset('img/Nen2.png') }}?v={{ time() }}"
                                                style="max-width: 43%;">
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal them moi hoc sinh -->
<div class="modal fade" id="mdThemMoiHocSinh" tabindex="-1" aria-modal="true" role="dialog" data-bs-backdrop="static">
    <div class="modal-dialog modal-md modal-dialog-centered" role="document">
        <div class="modal-content p-0 rounded-3 overflow-hidden">
            <!-- Header -->
            <div class="modal-header py-2 px-3" style="background:#fd7e14;color:#fff;">
                <h5 class="modal-title fw-bold text-uppercase" style="font-size:1.15rem;">Thêm mới học sinh/sinh viên
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <!-- Body -->
            <div class="modal-body pb-1 pt-3 px-3">
                <!-- Học sinh info -->
                <div class="row mb-3">
                    <div class="col-md-12">
                        <label id="labelNguoiTiepNhan" for="hocSinhID" class="form-label">
                            Học sinh/sinh viên<span class="text-danger">(*)</span>
                        </label>
                        <div class="position-relative">
                            <!-- Custom Select -->
                            <div class="d-flex align-items-center border rounded p-1"
                                style="background-color: #eee; cursor: pointer;" id="customSelect">
                                <div class="flex-grow-1" id="selectedOption">
                                    Chọn học sinh
                                </div>
                                <a href="#" class="text-nts-primary text-decoration-none me-2">Xem chi tiết</a>
                                <button class="btn btn-sm text-white"
                                    style="border-radius: 5px; background-color: var(--main-color)" id="btnDropdownHS">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>

                            <!-- Dropdown Options -->
                            <ul class="list-group position-absolute w-100 d-none" id="customDropdown"
                                style="z-index: 2000; top: 100%; left: 0; background-color: white; border: 1px solid #ced4da; border-radius: 5px; box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1); max-height: 150px; overflow-y: auto;">
                            </ul>

                        </div>
                        <!-- Hidden native select -->
                        <select id="hocSinhID" class="d-none">

                        </select>
                    </div>
                </div>


                <!-- Trường -->
                <div class="mb-2">
                    <label class="form-label mb-1 text-dark" style="font-size:14px;">Trường</label>
                    <input id="tenTruong" type="text" class="form-control" required disabled
                        value="Trung học phổ thông NTSOFT" />
                </div>

                <!-- 3 columns: Rèn luyện, Học tập, Tốt nghiệp, Ưu tiên -->
                <div class="row g-2">
                    <div class="col-md-6">
                        <label class="form-label mb-1" for="ketQuaRenLuyen" style="font-size:14px;">Kết quả rèn
                            luyện</label>
                        <select class="form-select" id="ketQuaRenLuyen">

                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label mb-1" for="ketQuaHocTap" style="font-size:14px;">Kết quả học
                            tập</label>
                        <select class="form-select" id="ketQuaHocTap">

                        </select>
                    </div>

                </div>
                <div class="row g-2 mt-2">
                    <div class="col-md-6">
                        <label class="form-label mb-1" for="chkUuTien" style="font-size:14px;">Thuộc diện ưu
                            tiên</label>
                        <select class="form-select" id="chkUuTien">

                        </select>
                    </div>
                    <!-- <div class="col-md-6 d-flex align-items-center">
                        <input class="form-check-input me-2" type="checkbox" value="" id="chkUuTien" checked>
                        <label class="form-check-label fw-bold" for="chkUuTien" style="font-size:14px;">
                            Thuộc diện ưu tiên
                        </label>
                    </div> -->
                    <div class="col-md-6">
                        <label class="form-label mb-1" for="ketQuaTotNghiep" style="font-size:14px;">Kết quả tốt
                            nghiệp <span class="text-danger">*</span></label>
                        <select class="form-select" id="ketQuaTotNghiep">

                        </select>
                    </div>
                </div>
                <!-- Ghi chú -->
                <div class="mb-2 mt-2">
                    <label class="form-label fw-bold" for="ghiChu" style="font-size:14px;">Ghi chú</label>
                    <textarea class="form-control" id="ghiChu" rows="4"></textarea>
                </div>
            </div>
            <!-- Footer -->
            <div class="modal-footer justify-content-end py-2 px-3" style="background:#f4f4f4;">
                <div>
                    <button type="button" class="btn btn-outline-danger fw-bold px-3 me-2" data-bs-dismiss="modal">
                        <i class="fa fa-times"></i>&nbsp;Đóng (F4)
                    </button>
                    <button type="button" class="btn btn-warning text-white fw-bold px-3" id="btnLuuVaDong">
                        <i class="fa fa-save"></i>&nbsp;Lưu và đóng (F9)
                    </button>
                </div>

            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="mdChonHocSinh" tabindex="-1" aria-modal="true" role="dialog" data-bs-backdrop="static">
    <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
        <div class="modal-content p-0 rounded-3 overflow-hidden">
            <!-- Header -->
            <div class="modal-header py-2 px-3" style="background:#fd7e14;color:#fff;">
                <h5 class="modal-title fw-bold text-uppercase" style="font-size:1.05rem;">Chọn học sinh/sinh viên</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <!-- Body -->
            <div class="modal-body py-2 px-3">
                <div class="row g-2">
                    <!-- Left: Danh sách trường học -->
                    <div class="col-12 col-md-3">
                        <fieldset class="KhungVien border rounded-3 h-100 p-0">
                            <legend>Danh sách trường học</legend>
                            <!-- Search -->
                            <div class="input-group input-group-sm my-2 px-2">
                                <div class="input-icon">
                                    <input type="text" class="form-control" placeholder="Nội dung tìm kiếm …"
                                        id="timKiemTruongHoc" autocomplete="off">
                                    <span class="input-icon-addon"><i class="fa fa-search"></i></span>
                                </div>
                            </div>
                            <!-- School list (replace with Tabulator or custom HTML as needed) -->
                            <div class="px-2 pb-2" style="min-height:360px;max-height:420px;overflow:auto;">
                                <div id="gridTruongHoc"></div>
                            </div>
                        </fieldset>
                    </div>
                    <!-- Right: Danh sách học sinh -->
                    <div class="col-12 col-md-9">
                        <fieldset class="KhungVien border rounded-3 h-100 p-0">
                            <legend>Danh sách học sinh</legend>

                            <!-- Search -->
                            <div class="input-group input-group-sm my-2 px-2" style="max-width:350px;">
                                <div class="input-icon">
                                    <input type="text" class="form-control" placeholder="Nội dung tìm kiếm …"
                                        id="timKiemHocSinh" autocomplete="off">
                                    <span class="input-icon-addon"><i class="fa fa-search"></i></span>
                                </div>
                            </div>
                            <!-- Student grid placeholder -->
                            <div id="tabGridHocSinhChon" class="px-2"
                                style="min-height:360px;max-height:420px;overflow:auto;border-radius:6px;">
                                <div id="gridChonHocSinh">

                                </div>
                            </div>
                        </fieldset>
                    </div>
                </div>
            </div>
            <!-- Footer -->
            <div class="modal-footer justify-content-end py-2 px-3" style="background:#f4f4f4;">
                <button type="button" class="btn btn-outline-danger fw-bold px-3 me-2" data-bs-dismiss="modal">
                    <i class="fa fa-times"></i> Đóng (F4)
                </button>
                <button type="button" class="btn btn-warning text-white fw-bold px-3" id="btnChonVaDong">
                    <i class="fa fa-check"></i> Chọn và đóng (F9)
                </button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="mdQuyetDinhTotNghiep" tabindex="-1" aria-modal="true" role="dialog"
    data-bs-backdrop="static">
    <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
        <div class="modal-content p-0 rounded-3 overflow-hidden">
            <!-- Modal Header -->
            <div class="modal-header py-2 px-3" style="background:#fd7e14;color:#fff;">
                <h5 class="modal-title fw-bold text-uppercase" id="tieuDe_mdQuyetDinhTotNghiep">Ban hành quyết định công
                    nhận tốt nghiệp
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <!-- Modal Body -->
            <div class="modal-body px-4 py-3">
                <div class="row">
                    <!-- Illustration -->
                    <div class="col-md-5 d-flex align-items-center justify-content-center mb-2 mb-md-0">
                        <img src="/img/Logo-note2.jpeg" alt="Quyết định" class="img-fluid" style="max-width:350px;">
                    </div>
                    <!-- Right Side: Content -->
                    <div class="col-md-7">
                        <!-- Alert/Instruction -->
                        <div class="border border-warning rounded mb-3 px-3 py-2"
                            style="background:#fff7e6;font-size:15px;">
                            <span id="alertMessage">
                                Bạn đang thực hiện ban hành quyết định công nhận tốt nghiệp tại quyết định số:
                                <b>01/2024/QĐ-SGD&ĐT</b> ngày ký <b>25/12/2024</b> của <b>Sở giáo dục và đào tạo Tỉnh
                                    NTSOFT</b> về việc công nhận học sinh sinh viên tốt nghiệp THPT năm học 2024. Vui
                                lòng điền đầy đủ các thông tin bên dưới và nhấn vào nút <b>“Ban hành”</b> để thực hiện
                                thao tác ban hành cho quyết định.
                            </span>
                        </div>
                        <!-- Fieldset -->
                        <fieldset class="border rounded-3 p-3 mb-1">
                            <legend class="float-none w-auto px-2 mb-0"
                                style="background:#fd7e14;color:#fff;font-size:14px;font-weight:600;border-radius:7px 7px 0 0;"
                                id="fieldsetLegend">
                                Thông tin ban hành
                            </legend>
                            <form>
                                <div class="row g-2">
                                    <div class="col-12 col-sm-6">
                                        <label class="form-label fw-bold mb-1" for="ngayBanHanh"
                                            style="font-size:14px;">Ngày ban hành <span
                                                class="text-danger">(*)</span></label>
                                        <div class="input-group input-group-sm">
                                            <input type="text" class="form-control date-picker" id="ngayBanHanh"
                                                name="NgayKy" autocomplete="off" data-date-format="dd/mm/yyyy"
                                                placeholder="dd/MM/yyyy" required>

                                        </div>
                                    </div>
                                    <div class="col-12 col-sm-6"></div>
                                    <div class="col-12 col-sm-6">
                                        <label class="form-label fw-bold mb-1" for="nguoiBanHanh"
                                            style="font-size:14px;">Người ban hành</label>
                                        <select class="form-select" id="nguoiBanHanh">
                                            <option>-Chọn-</option>
                                        </select>
                                    </div>
                                    <div class="col-12 col-sm-6">
                                        <label class="form-label fw-bold mb-1" for="chucVuBanHanh"
                                            style="font-size:14px;">Chức vụ</label>
                                        <select class="form-select" id="chucVuBanHanh">
                                            <option>-Chọn-</option>
                                        </select>
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label fw-bold mb-1" for="noiDungBanHanh"
                                            style="font-size:14px;">Nội dung ban hành</label>
                                        <div id="noiDungBanHanh" class="form-control" contenteditable="true"
                                            style="font-size:14px; min-height: 120px;"></div>
                                    </div>
                                </div>
                            </form>
                        </fieldset>
                    </div>
                </div>
            </div>
            <!-- Modal Footer -->
            <div class="modal-footer justify-content-end py-2 px-3" style="background:#f4f4f4;">
                <button type="button" class="btn btn-outline-danger fw-bold px-3 me-2" data-bs-dismiss="modal">
                    <i class="fa fa-times"></i> Đóng (F4)
                </button>
                <button type="button" class="btn btn-warning text-white fw-bold px-3" id="btnBanHanh">
                    <i class="fa fa-check"></i> Ban hành (F9)
                </button>
            </div>
        </div>
    </div>
</div>

@include('partials.viewHocSinh')
@include('partials.mdXemDinhKem')

@include('partials.nhapExcelModal', [
'id' => 'mdNhanExcel',
'title' => 'Nhận excel quyết định công nhận tối nghiệp',
])

<!-- Modal -->
<div class="modal fade" id="mdChiTietQD" tabindex="-1" aria-labelledby="modalChiTietQDLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-scrollable">
        <div class="modal-content">

            <!-- Modal header -->
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="modalChiTietQDLabel">Xem thông tin quyết định</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Đóng"></button>
            </div>

            <!-- Modal body -->
            <div class="modal-body">
                <!-- Thông tin chung -->
                <fieldset class="KhungVien">
                    <legend>Thông tin chung</legend>
                    <div class="row g-2 mt-2">
                        <div class="col-md-12" id="soQuyetDinh_ct">Số quyết định:</div>

                    </div>
                    <div class="row g-2 mt-2">
                        <div class="col-md-6" id="nguoiKy_ct">Người ký:</div>
                        <div class="col-md-6" id="chucVu_ct">Chức vụ:</div>
                    </div>
                    <div class="row g-2 mt-2">
                        <div class="col-md-6" id="ngayKy_ct"></div>
                        <div class="col-md-6" id="coQuanBanHanh">Cơ quan ban hành:</div>
                    </div>
                    <div class="row g-2 mt-2">
                        <div class="col-md-6" id="kythi_ct"></div>
                        <div class="col-md-6" id="htdt_ct"></div>

                    </div>
                    <div class="row g-2 mt-2">
                        <div class="col-md-6" id="hoidong_Ct"></div>
                        <div class="col-md-6" id="donvi_ct"></div>
                    </div>
                    <div class="mt-2">
                        Trích yếu:
                        <strong>
                            <span id="trichYeuContent">...</span>
                        </strong>
                    </div>
                    <div class="mt-2">
                        <div id="dinhKemLink_ct">

                        </div>
                    </div>
                </fieldset>


                <fieldset class="KhungVien border rounded-3 h-100 p-0 pt-2" style="height:360px">
                    <legend>Danh sách học sinh</legend>
                    <div class="px-2 pb-2" style="height:360px;overflow:auto;">
                        <div id="gridHocSinh_ct"></div>
                    </div>
                </fieldset>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-danger fw-bold px-3 me-2" data-bs-dismiss="modal">
                    <i class="fa fa-times"></i> Đóng (F4)
                </button>
            </div>

        </div>
    </div>
</div>


@include('partials.nhapExcelModal', [
'id' => 'mdNhanExcel',
'title' => 'Nhận excel học sinh tốt nghiệp',
])



@endsection

@push('scripts')
<script>
window.Laravel = window.Laravel || {};
window.Laravel = {
    ...window.Laravel,
    // other URLs you may already have...
    maTuTangUrl: `{{ route('dungchung.lay-ma-tutang', ['kyhieuLoaiPhieu' => 'SoQD_HoSoTotNghiep', 'bangDuLieu' => 'quyet_dinhs', 'cotDuLieu' => 'SoQuyetDinh']) }}`,
    getListToChuc: `{{{ route('dungchung.danhmuc.comboCapToChuc') }}}`,
    getListKyThi: `{{{ route('dungchung.danhmuc.comboKyThi') }}}`,
    getListDonVi: `{{{ route('dungchung.danhmuc.comboDonVi') }}}`,
    getListHTDT: `{{{ route('dungchung.danhmuc.comboHTDT') }}}`,
    getListHoiDong: `{{{ route('dungchung.danhmuc.comboHoiDong') }}}`,
    getListChucVu: "{{ route('dungchung.danhmuc.comboChucVu') }}",
    getListHocSinh: "{{ route('dungchung.danhmuc.comboHocSinh') }}",
    getListNhanvien: "{{ route('dungchung.danhmuc.comboNhanVien') }}",
    getListXepLoai: "{{ route('dungchung.danhmuc.comboXepLoai') }}",
    getListDienUuTien: "{{ route('dungchung.danhmuc.comboDienUuTien') }}",
    linkAnhHoSo: "{{asset('img/anhhoso.png')}}",
    //CRUD
    luuThongTin: "{{ route('quyetdinhtotnghiep.luuthongtin') }}",
    luuThongTinHocSinh: "{{ route('quyetdinhtotnghiep.luuthongtinHocSinhTN')}}",
    luuThongTinBanHanh: "{{ route('quyetdinhtotnghiep.luuthongtinBanHanh', ['quyetdinhid' => 'QUYETDINHID']) }}",
    luuThongTinThuHoi: "{{ route('quyetdinhtotnghiep.luuthongtinThuHoi', ['quyetdinhid' => 'QUYETDINHID']) }}",

    getListQD: "{{ route('quyetdinhtotnghiep.getall') }}",
    getListHSByQD: function(quyetDinhId) {
        return "{{ route('quyetdinhtotnghiep.listHocSinhByQuyetDinh', ['quyetdinhid' => 'QUYETDINHID']) }}"
            .replace('QUYETDINHID', quyetDinhId);
    },
    loadDuLieuSua: "{{ route('quyetdinhtotnghiep.loaddulieusua') }}",
    xoa: "{{ route('quyetdinhtotnghiep.xoa') }}",

    getListTruongHoc: "{{ route('quanly.dungchung.getDanhSachTruongHoc') }}",
    getListHS: function(donviId_TruongHoc) {
        return "{{ route('quanly.dungchung.getHSByTruongHoc', ['donviId_TruongHoc' => 'DONVI_ID_PLACEHOLDER']) }}"
            .replace('DONVI_ID_PLACEHOLDER', donviId_TruongHoc);
    },
    getHSByID: "{{ route('quyetdinhtotnghiep.getHocSinhTNById', ['id' => 'ID']) }}",
    xoaHSTN: "{{ route('quyetdinhtotnghiep.xoaHocSinhTN')}}",
    getMauExcel: "{{ route('quyetdinhtotnghiep.getMauExcel')}}",
    uploadDocUrl: "{{ route('api.files.upload') }}",
    loadTenSheetUrl: "{{ route('quyetdinhtotnghiep.loadSheetNames') }}",
    checkExcel: "{{ route('quyetdinhtotnghiep.checkExcel') }}",
    loadExcel: "{{ route('quyetdinhtotnghiep.loadExcel') }}",
    importExcel: "{{ route('quyetdinhtotnghiep.importExcel') }}",
};
$(document).on("click", ".btnTaiFileMauNhanExcel", function() {
    // simply open the template URL in a new tab to trigger download
    window.open(Laravel.getMauExcel, '_blank');
});
</script>
<script src="{{ asset('js/quanly/quyetdinhtotnghiep/quyetdinhtotnghiep.js') }}?v={{ time() }}"></script>
<script src="{{ asset('js/quanly/quyetdinhtotnghiep/banhanhquyetdinh.js') }}?v={{ time() }}"></script>
<script src="{{ asset('js/quanly/quyetdinhtotnghiep/nhanexcel.js') }}?v={{ time() }}"></script>
@endpush