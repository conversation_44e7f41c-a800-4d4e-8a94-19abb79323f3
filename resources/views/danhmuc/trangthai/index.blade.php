@extends('layouts.layouts')

{{-- shorthand section, no @endsection needed --}}
@section('title', 'Danh sách trạng thái')
<style type="text/css">
#MauSac {
    position: relative !important;
}

#MauSacMau {
    position: absolute;
    top: 0;
    right: 0px !important;
    z-index: -1;
    background-color: transparent;
    color: transparent;
}

.input-icon-addon svg {
    width: 24px;
    height: 24px;
}
</style>

@section('content')
<div class="row">
    <div class="row flex-row-reverse">
        <div class="col-md-8">
            <div class="text-end">
                <button type="button" class="nts-color-them btn btn-primary" id="btnThemMoi"><i
                        class="fa fa-plus"></i>&ensp;Thêm mới
                    (F2)</button>
                <div class="btn-group">
                    <div class="dropdown d-inline">
                        <button class="btn btn-primary dropdown-toggle-hide-arrow height-button-icon" type="button"
                            id="growthReportId" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="blue fa fa-ellipsis-h"></i>
                        </button>
                        <div class="dropdown-menu dropdown-menu-lg-end w-auto">
                            <a class="nts-color-in dropdown-item textsize-item" href="javascript:void(0);" id="btnPrint"
                                onclick="previewExportv1('pdf',table);return false;"><i
                                    class="text-warning fa fa-print iconsize-item"></i>&ensp; In</a>
                            <a class="nts-color-excel dropdown-item textsize-item" href="javascript:void(0);"
                                id="btnExport" onclick="previewExportv1('excel',table);return false;"><i
                                    class="text-success fa fa-file-excel-o iconsize-item"></i>&ensp; Xuất
                                Excel</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="input-icon mb-2">
                <input type="text" value="" class="form-control" placeholder="Nội dung tìm kiếm ..." id="timKiem"
                    autocomplete="off">
                <span class="input-icon-addon">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24"
                        stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round"
                        stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path>
                        <path d="M21 21l-6 -6"></path>
                    </svg>
                </span>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="Grid1"></div>
    </div>
</div>

{{-- Create Modal --}}
<div class="modal modal-blur fade" id="mdThemMoi" tabindex="-1" aria-modal="true" role="dialog"
    data-bs-backdrop="static">
    <div class="modal-dialog modal-md modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="tieuDeModal"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="TrangThaiID" />
                <div class="row">
                    <div class="col-lg-6">
                        <div class="mb-3">
                            <label class="form-label" for="MaTrangThai">Mã số</label>
                            <input type="text" class="form-control" id="MaTrangThai" required>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label class="form-label" for="MauSac">Màu sắc</label>
                            <input type="text" class="form-control input-sm inputnhap" id="MauSac"
                                style="color: #000000 !important;">
                            <input type="color" class="form-control position-absolute" id="MauSacMau"
                                style="top: 45px; left: 370px; z-index: -1; background-color:transparent !important; color:transparent !important;width: auto !important;">
                            <span class="input-icon-addon">
                                <i class="fa fa-pencil fa-pen-MauSac"></i>
                            </span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="mb-3">
                                <label class="form-label" for="TenTrangThai">Tên trạng thái</label>
                                <input type="text" class="form-control" id="TenTrangThai" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="mb-3">
                                <label class="form-label" for="TrangThaiID_Cha">Thuộc trạng thái</label>
                                <select class="form-control" id="TrangThaiID_Cha" tabindex="0"
                                    data-dropdown-parent="#mdThemMoi">
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="mb-3">
                                <label class="form-label" for="MoTa">Mô tả</label>
                                <textarea class="form-control" id="MoTa" rows="4"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <div style="display: contents; align-items: center; justify-content: space-between">
                        <div class="col-md-6" style="display: flex; align-items: center">
                            <label style="margin-bottom: unset" class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="TrangThai">
                                <label class="form-check-label" for="TrangThai">Đang sử dụng</label>
                            </label>
                        </div>
                        <div class="col-md-6">
                            <div style="float:right;text-align: right">
                                <a href="#" class="nts-color-dong btn btn-outline-danger" data-bs-dismiss="modal">
                                    <i class="fa fa-close"></i>&nbsp;Đóng (F4)
                                </a>
                                <a href="#" id="btnLuuVaDong" class="nts-color-luu btn btn-success ms-auto">
                                    <i class="fa fa-save"></i>&ensp;Lưu và đóng (F9)
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endsection

    @push('scripts')
    <script>
    window.Laravel = window.Laravel || {}; // 👈 Bảo vệ trước khi gán
    window.Laravel.trangthai = {
        GetDSTrangThai: "{{ route('trangthai.lbl.gettrangthai') }}",
        GetDSTrangThai2: "{{ route('trangthai.lbl.gettrangthai_khongthuocno') }}",
        maTuTangUrl: `{{ route('dungchung.lay-ma-tutang', ['kyhieuLoaiPhieu' => 'MaTrangThai', 'bangDuLieu' => 'trang_thais', 'cotDuLieu' => 'MaTrangThai']) }}`,
    };
    // pre-generate the exact URL you want to call
    </script>
    <script src="{{ asset('js/danhmuc/trangthai.js') }}?v={{ time() }}"></script>
    @endpush