@extends('layouts.layouts')

{{-- tiêu đề trang --}}
@section('title', '<PERSON>h sách xếp loại')

@section('content')
<div class="row flex-row-reverse">
    <div class="col-md-8">
        <div class="text-end">
            <button type="button" class="btn btn-primary nts-color-them" id="btnThemMoi"><i
                    class="fa fa-plus"></i>&ensp;Thêm mới
                (F2)</button>
            <div class="btn-group">
                <div class="dropdown d-inline">
                    <button class="nts-color-them btn btn-primary dropdown-toggle-hide-arrow height-button-icon"
                        type="button" id="growthReportId" data-bs-toggle="dropdown" aria-haspopup="true"
                        aria-expanded="false">
                        <i class="blue fa fa-ellipsis-h"></i>
                    </button>
                    <div class="dropdown-menu dropdown-menu-lg-end w-auto">
                        <a class="nts-color-in dropdown-item textsize-item" href="javascript:void(0);" id="btnPrint"
                            onclick="previewExportv1('pdf',table);return false;"><i
                                class="text-warning fa fa-print iconsize-item"></i>&ensp; In</a>
                        <a class="nts-color-excel dropdown-item textsize-item" href="javascript:void(0);" id="btnExport"
                            onclick="previewExportv1('excel',table);return false;"><i
                                class="text-success fa fa-file-excel-o iconsize-item"></i>&ensp; Xuất
                            Excel</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="input-icon mb-2">
            <input type="text" value="" class="form-control" placeholder="Tìm kiếm ..." id="timKiem" autocomplete="off">
            <span class="input-icon-addon">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24"
                    stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <circle cx="10" cy="10" r="7"></circle>
                    <line x1="21" y1="21" x2="15" y2="15"></line>
                </svg>
            </span>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="Grid1"></div>
    </div>
</div>

{{-- Modal thêm/sửa --}}
<div class="modal modal-blur fade" id="mdThemMoi" tabindex="-1" aria-modal="true" role="dialog"
    data-bs-backdrop="static">
    <div class="modal-dialog modal-md modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="tieuDeModal"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="xepLoaiId" />
                <div class="row">
                    <div class="col-lg-6 mb-3">
                        <label class="form-label" for="maXepLoai">Mã xếp loại</label>
                        <input type="text" class="form-control" id="maXepLoai" required>
                    </div>
                    <div class="col-lg-6 mb-3">
                        <label class="form-label" for="tenXepLoai">Tên xếp loại</label>
                        <input type="text" class="form-control" id="tenXepLoai" required>
                    </div>
                    <div class="col-lg-12 mb-3">
                        <label class="form-label" for="ghiChu">Ghi chú</label>
                        <textarea class="form-control" id="ghiChu" rows="4"></textarea>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div style="display: contents; align-items: center; justify-content: space-between">
                    <div class="col-md-6" style="display: flex; align-items: center">
                        <label style="margin-bottom: unset" class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="trangThai">
                            <label class="form-check-label" for="trangThai">Đang sử dụng</label>
                        </label>
                    </div>
                    <div class="col-md-6">
                        <div style="float:right;text-align: right">
                            <a href="#" class="btn btn-outline-danger" data-bs-dismiss="modal">
                                <i class="fa fa-close"></i>&nbsp;Đóng (F4)
                            </a>
                            <a href="#" id="btnLuuVaDong" class="btn btn-success ms-auto nts-color-luu">
                                <i class="fa fa-save"></i>&ensp;Lưu và đóng (F9)
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@include('partials.nhapExcelModal', [
'id' => 'mdNhanExcel',
'title' => 'Nhận excel xếp loại',
])

@endsection

@push('scripts')
<script>
window.Laravel = window.Laravel || {};
window.Laravel.xeploai = {
    // other URLs you may already have...
    exportExcelUrl: "{{ url('export/excel_v2') }}",
    exportPdfUrl: "{{ url('export/pdf_v2') }}",
    maTuTangUrl: `{{ route('dungchung.lay-ma-tutang', ['kyhieuLoaiPhieu' => 'MaXepLoai', 'bangDuLieu' => 'xep_loais', 'cotDuLieu' => 'maXepLoai']) }}`,
    uploadDocUrl: "{{ route('api.files.upload') }}",
    loadTenSheetUrl: "{{ route('nhanvien.loadSheetNames') }}",
    checkExcel: "{{ route('nhanvien.checkExcel') }}",
    loadExcel: "{{ route('nhanvien.loadExcel') }}",
    importExcel: "{{ route('nhanvien.importExcel') }}",
    downloadTemplate: "{{ route('nhanvien.template') }}"
};
$(document).on("click", ".btnTaiFileMauNhanExcel", function() {
    // simply open the template URL in a new tab to trigger download
    window.open(window.Laravel.xeploai.downloadTemplate, '_blank');
});
</script>
<script src="{{ asset('js/danhmuc/xeploai.js') }}?v={{ time() }}"></script>
@endpush