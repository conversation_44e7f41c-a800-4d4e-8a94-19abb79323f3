@extends('layouts.layouts')

{{-- tiêu đề trang --}}
@section('title', '<PERSON>h sách khoá thi')

@section('content')
@php
// chuẩn bị mảng các row filter
$filters = [
'<div class="col-md-6">
    <label for="MaKhoaThi_Loc">Mã khoá thi</label>
    <input type="text" id="MaKhoaThi_Loc" class="form-control" placeholder="Nhập mã khoá thi…">
</div>
<div class="col-md-6">
    <label for="TenKhoaThi_Loc">Tên khoá thi</label>
    <input type="text" id="TenKhoaThi_Loc" class="form-control" placeholder="Nhập tên khoá thi…">
</div>',
'<div class="col-md-6">
    <label for="KyThi_Loc"><PERSON><PERSON> thi</label>
    <select id="KyThi_Loc" class="form-control">…options…</select>
</div>
<div class="col-md-6">
    <label for="DiaDiem_Loc"><PERSON><PERSON><PERSON> đi<PERSON><PERSON> tổ chức</label>
    <input type="text" id="DiaDiem_Loc" class="form-control">
</div>',
'
<div class="col-md-6">
    <label class="form-label" for="TuNgay_Loc">Từ ngày</label>
    <input type="text" class="form-control date-picker" id="TuNgay_Loc" name="ngayKy" autocomplete="off"
        data-date-format="dd/mm/yyyy" placeholder="dd/MM/yyyy">
</div>
<div class="col-md-6">
    <label class="form-label" for="DenNgay_Loc">Đến ngày</label>
    <input type="text" class="form-control date-picker" id="DenNgay_Loc" name="ngayKy" autocomplete="off"
        data-date-format="dd/mm/yyyy" placeholder="dd/MM/yyyy">
</div>
',
'
<div class="col-md-12">
    <label class="form-label" for="TrangThai_Loc">Trạng thái</label>
    <select class="form-control" id="TrangThai_Loc">
        <option value="">— Chọn trạng thái —</option>
        <option value="true">Đang sử dụng</option>
        <option value="false">Ngừng sử dụng</option>
    </select>
</div>
'
];

$actions = [
'<a class="nts-color-in dropdown-item textsize-item" href="javascript:void(0);" id="btnPrint"
    onclick="previewExportv1(\'pdf\', table); return false;"><i
        class="text-warning fa fa-print iconsize-item"></i>&ensp;In</a>
',
'<a class="nts-color-excel dropdown-item textsize-item" href="javascript:void(0);" id="btnExport"
    onclick="previewExportv1(\'excel\', table); return false;"><i
        class="text-success fa fa-file-excel-o iconsize-item"></i>&ensp;Xuất Excel
</a>',
];
@endphp

@include('partials.filter-panel', [
'filters' => $filters,
'actions' => $actions,
'showTimKiemNangCao' => true,
])

<div class="row mt-2">
    <div class="col-12 px-0">
        <div id="Grid1"></div>
    </div>
</div>

{{-- Modal thêm/sửa --}}
<div class="modal fade" id="mdThemMoi" tabindex="-1" aria-modal="true" role="dialog" data-bs-backdrop="static">
    <div class="modal-dialog modal-md modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="tieuDeModal">Thông tin khoá thi</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="khoaThiId">
                <div class="row">
                    <div class="col-lg-6 mb-3">
                        <label class="form-label" for="maKhoaThi">Mã khoá thi</label>
                        <input type="text" class="form-control" id="maKhoaThi" required>
                    </div>
                    <div class="col-lg-12 mb-3">
                        <label class="form-label" for="tenKhoaThi">Tên khoá thi</label>
                        <input type="text" class="form-control" id="tenKhoaThi" required>
                    </div>
                    <div class="col-lg-12 mb-3">
                        <label class="form-label" for="kyThi">Kỳ thi</label>
                        <select class="form-control" id="kyThi" required></select>
                    </div>

                    <div class="col-lg-6 mb-3">
                        <label class="form-label" for="tuNgay">Từ ngày</label>
                        <input type="text" class="form-control date-picker" id="tuNgay" name="ngayKy" autocomplete="off"
                            data-date-format="dd/mm/yyyy" placeholder="dd/MM/yyyy" required>
                    </div>
                    <div class="col-lg-6 mb-3">
                        <label class="form-label" for="denNgay">Đến ngày</label>
                        <input type="text" class="form-control date-picker" id="denNgay" name="ngayKy"
                            autocomplete="off" data-date-format="dd/mm/yyyy" placeholder="dd/MM/yyyy" required>
                    </div>
                    <div class="col-lg-12 mb-3">
                        <label class="form-label" for="diaDiem">Địa điểm tổ chức</label>
                        <input type="text" class="form-control" id="diaDiem">
                    </div>
                    <div class="col-lg-12 mb-3">
                        <label class="form-label" for="ghiChu">Ghi chú</label>

                        <textarea class="form-control" id="ghiChu" rows="3"></textarea>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <label class="form-check form-switch mb-0">
                    <input class="form-check-input" type="checkbox" id="trangThai">
                    <span class="form-check-label">Đang sử dụng</span>
                </label>
                <div>
                    <button type="button" class="btn btn-outline-danger" data-bs-dismiss="modal"><i
                            class="fa fa-close"></i> Đóng (F4)</button>
                    <button type="button" id="btnLuuVaDong" class="btn btn-success ms-2 nts-color-luu"><i
                            class="fa fa-save"></i> Lưu và đóng (F9)</button>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
window.Laravel = window.Laravel || {};

window.Laravel.khoathi = {
    maTuTangUrl: `{{ route('dungchung.lay-ma-tutang', ['kyhieuLoaiPhieu'=>'MaKhoaThi','bangDuLieu'=>'khoa_this','cotDuLieu'=>'maKhoaThi']) }}`,
    getListKyThi: `{{ route('dungchung.danhmuc.comboKyThi') }}`,
    getListNam: `{{ route('dungchung.comboNam') }}`,
    bulkDelete: `{{ route('khoathi.bulkDelete') }}`
};
</script>
<script src="{{ asset('js/danhmuc/khoathi.js') }}?v={{ time() }}"></script>
@endpush