@extends('layouts.layouts')

{{-- shorthand section, no @endsection needed --}}
@section('title', '')

@section('content')
<style type="text/css">
#mdThemMoi .modal-dialog {
    min-width: 50% !important;
}
</style>
<div class="row flex-row-reverse">
    <div class="col-md-8">
        <div class="text-end">
            <button type="button" class="nts-color-them btn btn-primary" id="btnThemMoi"><i
                    class="fa fa-plus"></i>&ensp;Thêm mới
                (F2)</button>
            <div class="btn-group">
                <div class="dropdown d-inline">
                    <button class="btn btn-primary dropdown-toggle-hide-arrow height-button-icon" type="button"
                        id="growthReportId" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="blue fa fa-ellipsis-h"></i>
                    </button>
                    <div class="dropdown-menu dropdown-menu-lg-end w-auto">
                        <a class="nts-color-in dropdown-item textsize-item" href="javascript:void(0);" id="btnPrint"
                            onclick="previewExportv1('pdf',table);return false;"><i
                                class="text-warning fa fa-print iconsize-item"></i>&ensp; In</a>
                        <a class="nts-color-excel dropdown-item textsize-item" href="javascript:void(0);" id="btnExport"
                            onclick="previewExportv1('excel',table);return false;"><i
                                class="text-success fa fa-file-excel-o iconsize-item"></i>&ensp; Xuất
                            Excel</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="input-icon mb-2">
            <input type="text" value="" class="form-control" placeholder="Nội dung tìm kiếm ..." id="timKiem"
                autocomplete="off">
            <span class="input-icon-addon">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24"
                    stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path>
                    <path d="M21 21l-6 -6"></path>
                </svg>
            </span>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="Grid1"></div>
    </div>
</div>

{{-- Create Modal --}}
<div class="modal modal-blur fade" id="mdThemMoi" tabindex="-1" aria-modal="true" role="dialog"
    data-bs-backdrop="static">
    <div class="modal-dialog modal-md modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="tieuDeModal"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="TieuChiID" />
                <div class="row">
                    <div class="col-lg-3">
                        <div class="mb-3">
                            <label class="form-label" for="MaTieuChi">Mã</label>
                            <input type="text" class="form-control" id="MaTieuChi">
                        </div>
                    </div>
                    <div class="col-lg-9">
                        <div class="mb-3">
                            <label class="form-label" for="TenTieuChi">Tên tiêu chí</label>
                            <input type="text" class="form-control" id="TenTieuChi">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-3">
                        <div class="mb-3">
                            <label class="form-label" for="MauSac">Màu sắc</label>
                            <input type="text" class="form-control" id="MauSac">
                        </div>
                    </div>
                    <div class="col-lg-9">
                        <div class="mb-3">
                            <label class="form-label" for="TenTieuChiHTML">Tên tiêu chí HTML</label>
                            <textarea class="form-control" id="TenTieuChiHTML" rows="1"></textarea>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12">
                        <div class="mb-3">
                            <label class="form-label" for="StringSQL">Câu truy vấn</label>
                            <textarea class="form-control" id="StringSQL" rows="1"></textarea>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12">
                        <label class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="InNghien">
                            <span class="form-check-label">In nghiêng</span>
                        </label>
                        <label class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="InDam">
                            <span class="form-check-label">In đậm</span>
                        </label>
                        <label class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="GachChan">
                            <span class="form-check-label">Gạch chân</span>
                        </label>
                        <label class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="InHoa">
                            <span class="form-check-label">In hoa</span>
                        </label>
                    </div>
                </div>
                <div class="row">

                    <div class="col-lg-3">
                        <div class="mb-3">
                            <label class="form-label" for="Font">Font</label>
                            <select class="form-control input-sm" id="Font"></select>
                        </div>
                    </div>
                    <div class="col-lg-3">
                        <div class="mb-3">
                            <label class="form-label" for="FontSize">FontSize</label>
                            <select class="form-control input-sm" id="FontSize"></select>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="mb-3">
                            <label class="form-label" for="LoaiTieuChi">Loại tiêu chí</label>
                            <select class="form-control" id="LoaiTieuChi" tabindex="0" required>
                                {{-- <option value="" selected>Mặc định</option>
                                    <option value="THCS_BG">Bằng tốt nghiệp THCS - Bản gốc</option>
                                    <option value="THCS_BS">Bằng tốt nghiệp THCS - Bản sao</option>
                                    <option value="THPT_BG">Bằng tốt nghiệp THPT - Bản gốc</option>
                                    <option value="THPT_BS">Bằng tốt nghiệp THPT - Bản sao</option> --}}
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-3">
                        <div class="mb-3">
                            <label class="form-label" for="Class">Class</label>
                            <input type="text" class="form-control" placeholder="col-md-12" id="Class">
                        </div>
                    </div>
                    <div class="col-lg-3">
                        <div class="mb-3">
                            <label class="form-label" for="TextAlign">TextAlign</label>
                            <select class="form-control" id="TextAlign" tabindex="0">
                                <option value="" selected>Mặc định</option>
                                <option value="left">Canh trái</option>
                                <option value="right">Canh phải</option>
                                <option value="center">Canh giữa</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-3">
                        <div class="mb-3">
                            <label class="form-label" for="Float">Float</label>
                            <select class="form-control" id="Float" tabindex="0">
                                <option value="" selected>Mặc định</option>
                                <option value="left">Canh trái</option>
                                <option value="right">Canh phải</option>
                                <option value="center">Canh giữa</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-3">
                        <div class="mb-3">
                            <label class="form-label" for="Margin">Margin</label>
                            <div class="d-flex gap-2">
                                <input type="text" class="form-control number-format" placeholder="Top" id="Margin1">
                                <input type="text" class="form-control number-format" placeholder="Right" id="Margin2">
                                <input type="text" class="form-control number-format" placeholder="Bottom" id="Margin3">
                                <input type="text" class="form-control number-format" placeholder="Left" id="Margin4">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div style="display: contents; align-items: center; justify-content: space-between">
                    <div class="col-md-6" style="display: flex; align-items: center">
                        <label style="margin-bottom: unset" class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="TrangThai">
                            <label class="form-check-label" for="TrangThai">Đang sử dụng</label>
                        </label>
                    </div>
                    <div class="col-md-6">
                        <div style="float:right;text-align: right">
                            <a href="#" class="nts-color-dong btn btn-outline-danger" data-bs-dismiss="modal">
                                <i class="fa fa-close"></i>&nbsp;Đóng (F4)
                            </a>
                            <a href="#" id="btnLuuVaDong" class="nts-color-luu btn btn-success ms-auto">
                                <i class="fa fa-save"></i>&ensp;Lưu và đóng (F9)
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
window.Laravel = window.Laravel || {};
window.Laravel.local = {
    KiemTraXoa: `{{ route('dungchung.kiem-tra-xoa-v1') }}`,
    GetFontFamily_Combo: `{{ route('mauvanbangchungchi.getfontfamily') }}`,
    GetFontSize_Combo: `{{ route('mauvanbangchungchi.getfontsize') }}`,
    getloaiphoivanbangchungchi: "{{ route('loaiphoivanbangchungchi.getloaiphoivanbangchungchi') }}",
};
</script>
<script src="{{ asset('js/danhmuc/tieuchi.js') }}?v={{ time() }}"></script>
@endpush