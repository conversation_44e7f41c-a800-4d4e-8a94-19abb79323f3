@extends('layouts.layouts')

{{-- tiêu đề trang --}}
@section('title', '<PERSON>h sách kỳ thi')
@push('scripts')
<link rel="stylesheet" href="{{ asset('/css/danhnuc/kythi.css') }}">
@endpush
@section('content')
@php
$filters = [
// 1. Mã kỳ thi / Tên kỳ thi
'<div class="row mb-3">
    <div class="col-md-6">
        <label class="form-label" for="MaKyThi_Loc">Mã kỳ thi</label>
        <input type="text" class="form-control input-sm" id="MaKyThi_Loc" placeholder="Nhập mã kỳ thi…">
    </div>
    <div class="col-md-6">
        <label class="form-label" for="TenKyThi_Loc">Tên kỳ thi</label>
        <input type="text" class="form-control input-sm" id="TenKyThi_Loc" placeholder="Nhập tên kỳ thi…">
    </div>
</div>',

// 2. <PERSON><PERSON><PERSON> thi / Cấp tổ chức
'<div class="row mb-3">
    <div class="col-md-6">
        <label class="form-label" for="NgayThi_Loc">Ngày thi</label>
        <input type="text" class="form-control input-sm date-picker" id="NgayThi_Loc" placeholder="dd/MM/yyyy"
            autocomplete="off">
    </div>
    <div class="col-md-6">
        <label class="form-label" for="CapToChuc_Loc">Cấp tổ chức</label>
        <select class="form-control input-sm" id="CapToChuc_Loc">
            <option value="">— Chọn cấp tổ chức —</option>
            <option value="cap_quoc_gia">Quốc gia</option>
            <option value="cap_tinh">Cấp tỉnh</option>
            <option value="cap_truong">Trường</option>
            <option value="cap_khac">Khác</option>
        </select>
    </div>
</div>',

// 3. Môn thi (multi-select, styled for Select2)
'<div class="row mb-3">
    <div class="col-md-12">
        <label class="form-label" for="MonThi_Loc">Môn thi</label>
        <select class="form-select" id="MonThi_Loc" multiple="multiple" style="width: 100%;">
            <!-- JS sẽ populate options động -->
        </select>
    </div>
</div>',
];

@endphp
@include('partials.filter-panel', [
'filters' => $filters,
'actions' => null,
'showTimKiemNangCao' => true,'showBulkActions' => true,

])


<div class="row" style="margin-top: 4px; padding-left: 0;">
    <div class="col-md-12 px-0">
        <div id="Grid1"></div>
    </div>
</div>

{{-- Modal thêm/sửa --}}
<div class="modal modal-blur fade" id="mdThemMoi" tabindex="-1" aria-modal="true" role="dialog"
    data-bs-backdrop="static">
    <div class="modal-dialog modal-md modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header" style="background: #ff6600; color: white">
                <h5 class="modal-title" id="tieuDeModal">Thêm mới kỳ thi</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="kyThiId" />
                <div class="row">
                    <div class="col-lg-6 mb-3">
                        <label class="form-label" for="maKyThi">
                            Mã
                        </label>
                        <input type="text" class="form-control" id="maKyThi" required>
                    </div>
                    <div class="col-lg-6 mb-3">
                        <label class="form-label" for="tenKyThi">
                            Tên kỳ thi
                        </label>
                        <input type="text" class="form-control" id="tenKyThi" required>
                    </div>
                    <div class="col-lg-6 mb-3">
                        <label class="form-label" for="ngayThi">
                            Ngày thi
                        </label>
                        <input type="text" class="form-control date-picker" id="ngayThi" name="ngayThi"
                            autocomplete="off" data-date-format="dd/mm/yyyy" placeholder="dd/MM/yyyy">
                    </div>
                    <div class="col-lg-6 mb-3">
                        <label class="form-label" for="capToChuc">
                            Cấp tổ chức
                        </label>
                        <select class="form-select" id="capToChuc">
                            <option value="">-- Chọn cấp tổ chức --</option>
                            <option value="cap_quoc_gia">Quốc gia</option>
                            <option value="cap_tinh">Cấp tỉnh</option>
                            <option value="cap_truong">Trường</option>
                            <option value="cap_khac">Khác</option>
                        </select>
                    </div>
                    <div class="col-lg-12 mb-3">
                        <label class="form-label" for="monThi">
                            Môn thi
                        </label>
                        <select class="form-select" id="monThi" multiple="multiple" style="width: 100%">
                            <!-- Options will be dynamically populated by JS/Select2 -->
                        </select>
                    </div>
                    <div class="col-lg-12 mb-3">
                        <label class="form-label" for="dienGiai">
                            Diễn giải
                        </label>
                        <textarea class="form-control" id="ghiChu" rows="4" maxlength="500"></textarea>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex align-items-center justify-content-between">
                <div style="display: flex; align-items: center;">
                    <!-- Custom status switch with dot -->
                    <span style="display: flex; align-items: center;">
                        <label class="form-check form-switch m-0" style="cursor: pointer;">
                            <input class="form-check-input" type="checkbox" id="trangThai" checked>
                            <span class="form-check-label" for="trangThai">Đang sử dụng</span>
                        </label>
                    </span>
                </div>
                <div>
                    <button type="button" class="btn btn-outline-danger" data-bs-dismiss="modal">
                        <i class="fa fa-close"></i> Đóng (F4)
                    </button>
                    <button type="button" id="btnLuuVaDong" class="btn btn-warning ms-2"
                        style="color: white; font-weight: 500">
                        <i class="fa fa-save"></i> Lưu và đóng (F9)
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@include('partials.nhapExcelModal', [
'id' => 'mdNhanExcel',
'title' => 'Nhận excel kỳ thi',
])


<div class="modal modal-blur fade" id="mdXemChiTiet" tabindex="-1" aria-modal="true" role="dialog"
    data-bs-backdrop="static">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="max-height: 95vh; overflow-y: scroll;padding-bottom: 30px">
            <div class="modal-header">
                <h5 class="modal-title" id="">Xem thông tin kỳ thi</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="card container-content ">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" data-bs-toggle="tabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <a href="#Tab1_V" id="btnTab1_V" class="nav-link active" data-bs-toggle="tab"
                                    aria-selected="true" role="tab">
                                    <svg class="icon icon-tabler icon-tabler-vocabulary" width="24" height="24"
                                        viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none"
                                        stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path
                                            d="M10 19h-6a1 1 0 0 1 -1 -1v-14a1 1 0 0 1 1 -1h6a2 2 0 0 1 2 2a2 2 0 0 1 2 -2h6a1 1 0 0 1 1 1v14a1 1 0 0 1 -1 1h-6a2 2 0 0 0 -2 2a2 2 0 0 0 -2 -2z">
                                        </path>
                                        <path d="M12 5v16"></path>
                                        <path d="M7 7h1"></path>
                                        <path d="M7 11h1"></path>
                                        <path d="M16 7h1"></path>
                                        <path d="M16 11h1"></path>
                                        <path d="M16 15h1"></path>
                                    </svg>
                                    &nbsp; Tổng quan
                                </a>
                            </li>
                            <li class="nav-item" role="presentation">
                                <a href="#Tab2_V" id="btnTab2_V" class="nav-link" data-bs-toggle="tab"
                                    aria-selected="false" tabindex="-1" role="tab">
                                    <svg class="icon icon-tabler icon-tabler-book-2" width="24" height="24"
                                        viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none"
                                        stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                        <path d="M19 4v16h-12a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2h12z" />
                                        <path d="M19 16h-12a2 2 0 0 0 -2 2" />
                                        <path d="M9 8h6" />
                                    </svg>&nbsp; Tab 2
                                </a>
                            </li>
                            <li class="nav-item" role="presentation">
                                <a href="#Tab3_V" id="btnTab3_V" class="nav-link" data-bs-toggle="tab"
                                    aria-selected="false" tabindex="-1" role="tab">
                                    <svg class="icon icon-tabler icon-tabler-file-description" width="24" height="24"
                                        viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none"
                                        stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M14 3v4a1 1 0 0 0 1 1h4"></path>
                                        <path
                                            d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z">
                                        </path>
                                        <path d="M9 17h6"></path>
                                        <path d="M9 13h6"></path>
                                    </svg>&nbsp; Tab 3
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content">
                            <div class="tab-pane fade active show" id="Tab1_V" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-12">
                                        <fieldset class="KhungVien">
                                            <legend><span>Thông tin kỳ thi</span></legend>


                                            <div class="row">
                                                <div class="col-md-4 d-flex align-items-center mb-2">
                                                    <label class="form-label mb-0 me-2">Mã kỳ thi:</label>
                                                    <span id="lblMaKyThi_V" class="fw-bold"></span>
                                                </div>
                                                <div class="col-md-4 d-flex align-items-center mb-2">
                                                    <label class="form-label mb-0 me-2">Tên kỳ thi:</label>
                                                    <span id="lblTenKyThi_V" class="fw-bold"></span>
                                                </div>
                                                <div class="col-md-4 d-flex align-items-center mb-2">
                                                    <label class="form-label mb-0 me-2">Ngày thi:</label>
                                                    <span id="lblNgayThi_V" class="fw-bold"></span>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-4 d-flex align-items-center mb-2">
                                                    <label class="form-label mb-0 me-2">Cấp tổ chức:</label>
                                                    <span id="lblCapToChuc_V" class="fw-bold"></span>
                                                </div>
                                                <div class="col-md-4 d-flex align-items-center mb-2">
                                                    <label class="form-label mb-0 me-2">Môn thi:</label>
                                                    <span id="lblMonThi_V" class="fw-bold"></span>
                                                </div>
                                                <div class="col-md-4 d-flex align-items-center mb-2">
                                                    <label class="form-label mb-0 me-2">Trạng thái:</label>
                                                    <span id="lblTrangThai_V" class="fw-bold"></span>
                                                </div>

                                            </div>

                                            <div class="row">
                                                <div class="col-md-12 d-flex align-items-center mb-2">
                                                    <label class="form-label mb-0 me-2">Ghi chú:</label>
                                                    <span id="lblGhiChu_V" class="fw-bold">—</span>
                                                </div>

                                            </div>

                                        </fieldset>
                                    </div>
                                    <br />
                                </div>
                            </div>
                            <div class="tab-pane fade" id="Tab2_V" role="tabpanel">
                                <fieldset class="KhungVien" style="padding: 5px 10px 10px 10px !important;">
                                    <legend><span>Tab2</span></legend>
                                </fieldset>
                            </div>
                            <div class="tab-pane fade" id="Tab3_V" role="tabpanel">
                                <fieldset class="KhungVien" style="padding: 5px 10px 10px 10px !important;">
                                    <legend><span>Tab3</span></legend>
                                </fieldset>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="position: fixed; bottom: 0px; left: 0px; width: 100%">
                <div style="display: flex; align-items: center; justify-content: space-between">
                    <div class="col-md-6" style="display: flex; align-items: center">
                    </div>
                    <div class="col-md-6">
                        <div style="float:right;text-align: right">
                            <a href="#" class="btn btn-outline-danger" data-bs-dismiss="modal">
                                <i class="fa fa-close"></i>&nbsp;Đóng
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
window.Laravel = window.Laravel || {};
window.Laravel.kythi = {
    // other URLs you may already have...
    maTuTangUrl: `{{ route('dungchung.lay-ma-tutang', ['kyhieuLoaiPhieu' => 'MaKyThi', 'bangDuLieu' => 'ky_this', 'cotDuLieu' => 'MaKyThi']) }}`,
    getListToChuc: `{{{ route('dungchung.danhmuc.comboCapToChuc') }}}`,
    getListMonHoc: `{{{ route('dungchung.danhmuc.comboMonHoc') }}}`,
    bulkDelete: `{{{ route('kythi.bulkDelete') }}}`
};
$(document).on("click", ".btnTaiFileMauNhanExcel", function() {
    // simply open the template URL in a new tab to trigger download
    window.open(window.Laravel.kythi.downloadTemplate, '_blank');
});
</script>
<script src="{{ asset('js/danhmuc/kythi.js') }}?v={{ time() }}"></script>
@endpush