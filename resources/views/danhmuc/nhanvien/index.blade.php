@extends('layouts.layouts')

{{-- tiêu đề trang --}}
@section('title', '<PERSON>h sách nhân viên')

@section('content')
<div class="row">
    <div class="row flex-row-reverse">
        <div class="col-md-8">
            <div class="text-end">
                <button type="button" class="nts-color-them btn btn-primary" id="btnThemMoi"><i
                        class="fa fa-plus"></i>&ensp;Thêm mới
                    (F2)</button>
                <div class="btn-group">
                    <div class="dropdown d-inline">
                        <button class="btn btn-primary dropdown-toggle-hide-arrow height-button-icon" type="button"
                            id="growthReportId" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="blue fa fa-ellipsis-h"></i>
                        </button>
                        <div class="dropdown-menu dropdown-menu-lg-end w-auto">
                            <a class="dropdown-item textsize-item" href="javascript:void(0);" id="btnNhapExcel"><i
                                    class="text-info fa fa-file-excel-o"></i> Nhập Excel</a>
                            <a class="dropdown-item textsize-item" href="javascript:void(0);" id="btnXuatExcel"><i
                                    class="text-success fa fa-file-excel-o"></i> Xuất Excel</a>
                            <a class="dropdown-item textsize-item" href="javascript:void(0);" id="btnXuatPdf"> <i
                                    class="text-danger fa fa-file-text-o"></i> Xuất PDF</a>
                            <a class="dropdown-item textsize-item" href="javascript:void(0);" id="btnPrint"> <i
                                    class="text-primary fa fa-print"></i> In</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="input-icon mb-2">
                <input type="text" value="" class="form-control" placeholder="Tìm kiếm ..." id="timKiem"
                    autocomplete="off">
                <span class="input-icon-addon">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24"
                        stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round"
                        stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <circle cx="10" cy="10" r="7"></circle>
                        <line x1="21" y1="21" x2="15" y2="15"></line>
                    </svg>
                </span>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="Grid1"></div>
    </div>
</div>

{{-- Modal thêm/sửa --}}
{{-- Modal thêm/sửa --}}
<div class="modal modal-blur fade" id="mdThemMoi" tabindex="-1" aria-modal="true" role="dialog"
    data-bs-backdrop="static">
    <div class="modal-dialog modal-md modal-dialog-centered" role="document">
        <div class="modal-content">

            <div class="modal-header">
                <h5 class="modal-title" id="tieuDeModal"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>

            <div class="modal-body">
                <input type="hidden" id="NhanVienID" />

                <div class="row">
                    <div class="col-lg-6 mb-3">
                        <label class="form-label" for="MaNhanVien">Mã</label>
                        <input type="text" class="form-control" id="MaNhanVien" required>
                    </div>

                    <div class="col-lg-12 mb-3">
                        <label class="form-label" for="TenNhanVien">Họ và tên</label>
                        <input type="text" class="form-control" id="TenNhanVien" required>
                    </div>


                    <div class="col-lg-6 mb-3">
                        <label class="form-label" for="SoDienThoai">Số điện thoại</label>
                        <input type="text" class="form-control" id="SoDienThoai">
                    </div>

                    <div class="col-lg-6 mb-3">
                        <label class="form-label" for="PhongBanID">Phòng ban</label>
                        <select class="form-select" id="PhongBanID">
                            <option value="">-- Chọn phòng ban --</option>
                            {{-- @foreach ($phongBans as $pb)
                   <option value="{{ $pb->phongBanID }}">{{ $pb->tenPhongBan }}</option>
                            @endforeach --}}
                        </select>
                    </div>

                    <div class="col-lg-6 mb-3">
                        <label class="form-label" for="ChucVuID">Chức vụ</label>
                        <select class="form-select" id="ChucVuID">
                            <option value="">-- Chọn chức vụ --</option>

                        </select>
                    </div>

                    <div class="col-lg-6 mb-3">
                        <label class="form-label" for="GioiTinhID">Giới tính</label>
                        <select class="form-select" id="GioiTinhID">
                            <option value="">-- Chọn giới tính --</option>
                        </select>
                    </div>

                    <div class="col-lg-6 mb-3">
                        <label class="form-label" for="NgaySinh">Ngày sinh</label>
                        <input type="text" class="form-control date-picker" autocomplete="off"
                            data-date-format="dd/mm/yyyy" placeholder="dd/MM/yyyy" id="NgaySinh">
                    </div>

                    <!-- CMND/CCCD -->
                    <div class="col-lg-6 mb-3">
                        <label class="form-label" for="CMND">CMND/CCCD</label>
                        <input type="text" class="form-control" id="CMND">
                    </div>

                    <!-- Ngày cấp -->
                    <div class="col-lg-6 mb-3">
                        <label class="form-label" for="NgayCap">Ngày cấp</label>
                        <input type="text" class="form-control date-picker" autocomplete="off"
                            data-date-format="dd/mm/yyyy" placeholder="dd/MM/yyyy" id="NgayCap">
                    </div>

                    <!-- Nơi cấp -->
                    <div class="col-lg-6 mb-3">
                        <label class="form-label" for="NoiCap">Nơi cấp</label>
                        <input type="text" class="form-control" id="NoiCap">
                    </div>

                    <div class="col-lg-12 mb-3">
                        <label class="form-label" for="DiaChi">Địa chỉ</label>
                        <textarea class="form-control" id="DiaChi" rows="2"></textarea>
                    </div>
                </div>
            </div>

            <div class="modal-footer d-flex justify-content-between align-items-center">
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="TrangThai">
                    <label class="form-check-label" for="TrangThai">Đang làm việc</label>
                </div>
                <div>
                    <button type="button" class="btn btn-outline-danger" data-bs-dismiss="modal">
                        <i class="fa fa-close"></i>&ensp;Đóng (F4)
                    </button>
                    <button type="button" id="btnLuuVaDong" class="btn btn-success ms-2 nts-color-luu">
                        <i class="fa fa-save"></i>&ensp;Lưu và đóng (F9)
                    </button>
                </div>
            </div>

        </div>
    </div>
</div>


{{-- Export Preview Modal --}}
<div class="modal fade" id="exportPreviewModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exportPreviewTitle">Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="previewContent" style="min-height:60vh"></div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button id="confirmDownloadBtn" type="button" class="btn btn-primary">Tải về</button>
            </div>
        </div>
    </div>
</div>


@include('partials.nhapExcelModal', [
'id' => 'mdNhanExcel',
'title' => 'Nhận excel nhân viên',
])

@endsection

@push('scripts')
<script>
window.Laravel = window.Laravel || {}
window.Laravel.nhanvien = {
    // other URLs you may already have...
    exportExcelUrl: "{{ url('export/excel_v2') }}",
    exportPdfUrl: "{{ url('export/pdf_v2') }}",
    maTuTangUrl: `{{ route('dungchung.lay-ma-tutang', ['kyhieuLoaiPhieu' => 'MaNhanVien', 'bangDuLieu' => 'nhan_viens', 'cotDuLieu' => 'maNhanVien']) }}`,
    uploadDocUrl: "{{ route('api.files.upload') }}",
    uploadDocUrlv2: "{{ route('api.files.uploadv2') }}",
    loadTenSheetUrl: "{{ route('nhanvien.loadSheetNames') }}",
    checkExcel: "{{ route('nhanvien.checkExcel') }}",
    loadExcel: "{{ route('nhanvien.loadExcel') }}",
    importExcel: "{{ route('nhanvien.importExcel') }}",
    downloadTemplate: "{{ route('nhanvien.template') }}",
    listPhongBanUrl: "{{ route('dungchung.danhmuc.comboPhongBan') }}",
    listDonViUrl: "{{ route('dungchung.danhmuc.comboDonVi') }}",
    listGioiTinhUrl: "{{ route('dungchung.danhmuc.comboGioiTinh') }}",
    listChucVuUrl: "{{ route('dungchung.danhmuc.comboChucVu') }}",
};
$(document).on("click", ".btnTaiFileMauNhanExcel", function() {
    // simply open the template URL in a new tab to trigger download
    window.open(window.Laravel.nhanvien.downloadTemplate, '_blank');
});
// pre-generate the exact URL you want to call
const maTuTangUrl =
    `{{ route('dungchung.lay-ma-tutang', ['kyhieuLoaiPhieu' => 'MaNhanVien', 'bangDuLieu' => 'nhan_viens', 'cotDuLieu' => 'maNhanVien']) }}`;
</script>
<script src="{{ asset('js/danhmuc/nhanvien/nhanvien.js') }}?v={{ time() }}"></script>
<script src="{{ asset('js/danhmuc/nhanvien/nhanvien_nhanexcel.js') }}?v={{ time() }}"></script>
@endpush