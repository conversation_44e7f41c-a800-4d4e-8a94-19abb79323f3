@extends('layouts.layouts')

{{-- shorthand section, no @endsection needed --}}
@section('title', '<PERSON><PERSON> sách mẫu văn bằng chứng chỉ')

@section('content')
<style type="text/css">
.tabulator-cell .dropdown-menu-end {
    position: fixed !important;
}

#mdThietLap .modal-lg {
    max-width: 100%;
    margin: 0 !important;
}

#mdThietLap .modal-dialog {
    margin: 0 !important;
    /* Xóa khoảng trống trên/dưới */
    width: 100%;
    height: 100vh;
    /* Chiều cao toàn bộ màn hình */
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    /* Footer luôn ở dưới */
}

#mdThietLap .modal-content {
    width: 100%;
    height: 100%;
    /* Chiếm hết chiều cao của modal-dialog */
    display: flex;
    flex-direction: column;
    border-radius: 0 !important;
    /* Bỏ bo góc */
}

#mdThietLap .modal-body {
    flex-grow: 1;
    /* Body chiếm không gian còn lại */
    overflow-y: auto;
    /* <PERSON><PERSON>m bảo nội dung có thể cuộn */
}

#mdThietLap .modal-body .modal-header {
    border-radius: unset !important;
}

#mdThietLap .modal-footer {
    flex-shrink: 0;
    /* Đảm bảo footer không bị thu nhỏ */
}

/* Xử lý thanh cuộn */
#mdThietLap .modal-body::-webkit-scrollbar {
    width: 0;
    /* Ẩn thanh cuộn */
}

#mdThietLap .modal-body {
    scrollbar-width: none;
    /* Firefox: Ẩn thanh cuộn */
}

#mdThietLap.modal {
    overflow: hidden !important;
    /* Đảm bảo modal không gây cuộn */
}

#mdThietLap .growthReportId:hover {
    border: none !important;
    background: none !important;
}

#mdThietLap .growthReportId:focus {
    border: none !important;
    background: none !important;
}

#mdThietLap input[type=text] #yourColorPicker {
    font-family: monospace;
    width: 100%;
}

#mdThietLap .certificate-fieldset {
    border: 2px solid black;
    padding: 20px;
    border-radius: 5px;
}

#mdThietLap .certificate-fieldset legend {
    font-weight: bold;
    font-size: 18px;
    padding: 0 10px;
}

#mdThietLap .certificate-container {
    font-family: "Times New Roman", Times, serif;
    font-size: 16px;
    line-height: 1.8;
}

#mdThietLap .text-center {
    text-align: center;
}

#mdThietLap .title {
    font-weight: bold;
    text-transform: uppercase;
}

#mdThietLap .subtitle {
    font-style: italic;
}

#mdThietLap .certificate-title {
    font-weight: bold;
    font-size: 20px;
}

#mdThietLap .certificate-subtitle {
    font-size: 18px;
}

#mdThietLap .english-title {
    font-style: italic;
    margin-bottom: 20px;
}

#mdThietLap .info p {
    margin: 0;
}

#mdThietLap .condition-title {
    margin-top: 20px;
    margin-bottom: 10px;
}

#mdThietLap ul {
    list-style-type: none;
    padding-left: 20px;
}

#mdThietLap ul.list-text>li {
    margin-bottom: 0;
}

#mdThietLap .footer-info p {
    margin: 0;
}

#mdThietLap .sign-section {
    margin-top: 20px;
    font-style: italic;
}

#mdThietLap .certificate-container>p {
    margin-bottom: 0px !important;
}

#htmlMauGCN1 {
    /* width: 19cm;
                                    height: 13cm;
                                    border: 1px solid #000;
                                    box-sizing: border-box;
                                    padding: 0px;
                                    background-color: white; */
    width: 21cm;
    /* A5 ngang: 21cm x 14.8cm | hoặc đổi lại 14.8cm x 21cm nếu A5 dọc */
    height: 14.8cm;
    border: 1px solid #000;
    box-sizing: border-box;
    padding: 2cm;
    /* hoặc điều chỉnh tùy nội dung */
    position: relative;
    background-color: white;

    /* Chèn background image (watermark) */
    background-image: url('/duong_dan_anh/watermark.png');
    /* Thay bằng URL ảnh của bạn */
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    opacity: 1;
    /* Không dùng opacity ở đây nếu bạn chỉ muốn ảnh nền mờ */
}

/* Nếu cần làm mờ ảnh nền */
#htmlMauGCN1::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('public/img/ThemMoiHoSo.jpg');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    opacity: 0.1;
    /* Độ mờ của watermark */
    z-index: 0;
}

/* Nội dung phía trên ảnh nền */
#htmlMauGCN1 .certificate-container-content {
    position: relative;
    z-index: 1;
}

#mdThietLap .text-line-modal {
    display: flex;
    align-items: center;
    /* Căn giữa theo chiều dọc */
}

#mdThietLap .text-line {
    display: contents;
    color: var(--primary);
    /* Màu chữ */
    font-weight: bold;
    font-size: 14px !important;
    white-space: nowrap;
    /* Đảm bảo chữ không bị xuống dòng */
}

#mdThietLap .text-line::after {
    content: "";
    width: 100%;
    margin-left: 10px;
    display: block;
    height: 1px;
    border-bottom: 1px solid var(--primary);
}

#mdThietLap ul {
    list-style-type: none;
    /* Loại bỏ bullet */
    padding: 0;
    margin: 0;
}

#mdThietLap li {
    display: flex;
    /* Sử dụng flexbox để căn ngang */
    align-items: center;
    /* Căn giữa checkbox và văn bản */
    margin-bottom: 8px;
    /* Khoảng cách giữa các dòng */
}

#mdThietLap input[type="checkbox"] {
    width: 20px;
    height: 20px;
    background-color: white;
    border: 1px solid var(--primary);
    border-radius: 2px;
    padding: 2px;
    appearance: none;
    outline: none;
    cursor: pointer;
    line-height: 18px;
    margin-right: 8px;
    /* Thêm khoảng cách giữa checkbox và văn bản */
}

#mdThietLap input[type="checkbox"]:checked {
    background-color: #FFFFFF;
    color: #3f9000;
    position: relative;
}

#mdThietLap input[type="checkbox"]:checked::after {
    content: "✔";
    display: block;
    text-align: center;
    color: var(--primary);
    font-size: 16px;
    line-height: 18px;
}

/* Loại bỏ viền và chỉnh CKEditor giao diện trống */
#mdThietLap div#cke_1_contents {
    display: none !important;
}

/* CSS for limiting the modal body height and adding scroll */
#mdXemThem .modal-body {
    max-height: 550px;
    overflow-y: auto;
    overflow-x: hidden;
}


#mdThietLap .row {
    display: flex;
    align-items: stretch;
    /* Đảm bảo các cột có cùng chiều cao */
}

#mdThietLap .KhungVien {
    flex: 1;
    /* Tự động điều chỉnh chiều cao */
    margin: 10px;
}

#mdThietLap .KhungVien-thietlap {
    background-color: #f9f9f9;
    /* Màu nền cho thiết lập */
}

#mdThietLap .KhungVien-MauGiayChungNhan {
    background-color: #ffffff;
    /* Màu nền cho mẫu văn bằng, chứng chỉ */
}
</style>
<div class="row flex-row-reverse">
    <div class="col-md-8">
        <div class="text-end">
            <button type="button" class="nts-color-them btn btn-primary" id="btnThemMoi"><i
                    class="fa fa-plus"></i>&ensp;Thêm mới
                (F2)</button>
            <div class="btn-group">
                <div class="dropdown d-inline">
                    <button class="btn btn-primary dropdown-toggle-hide-arrow height-button-icon" type="button"
                        id="growthReportId" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="blue fa fa-ellipsis-h"></i>
                    </button>
                    <div class="dropdown-menu dropdown-menu-lg-end w-auto">
                        <a class="nts-color-in dropdown-item textsize-item" href="javascript:void(0);" id="btnPrint"
                            onclick="previewExportv1('pdf',table);return false;"><i
                                class="text-warning fa fa-print iconsize-item"></i>&ensp; In</a>
                        <a class="nts-color-excel dropdown-item textsize-item" href="javascript:void(0);" id="btnExport"
                            onclick="previewExportv1('excel',table);return false;"><i
                                class="text-success fa fa-file-excel-o iconsize-item"></i>&ensp; Xuất
                            Excel</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="input-icon mb-2">
            <input type="text" value="" class="form-control" placeholder="Nội dung tìm kiếm ..." id="timKiem"
                autocomplete="off">
            <span class="input-icon-addon">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24"
                    stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path>
                    <path d="M21 21l-6 -6"></path>
                </svg>
            </span>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="Grid1"></div>
    </div>
</div>
{{-- Create Modal --}}
<div class="modal modal-blur fade" id="mdThemMoi" tabindex="-1" aria-modal="true" role="dialog"
    data-bs-backdrop="static">
    <div class="modal-dialog modal-md modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="tieuDeModal"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="MauVanBangChungChiID" />
                <input type="hidden" id="MauVanBangChungChiID_Tam" />
                <div class="row">
                    <div class="col-lg-6">
                        <div class="mb-3">
                            <label class="form-label" for="MaMauVanBangChungChi">Mã</label>
                            <input type="text" class="form-control" id="MaMauVanBangChungChi" required>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12">
                        <div class="mb-3">
                            <label class="form-label" for="TenMauVanBangChungChi">Tên mẫu văn bằng chứng chỉ</label>
                            <input type="text" class="form-control" id="TenMauVanBangChungChi" required>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12">
                        <div class="mb-3">
                            <label class="form-label" for="LoaiVanBangChungChi">Loại văn bằng, chứng chỉ</label>
                            <select class="form-control" id="LoaiVanBangChungChi" tabindex="0" required>
                                {{-- <option value="" selected> -Chọn-</option>
                                    <option value="THCS_BG">Bằng tốt nghiệp THCS - Bản gốc</option>
                                    <option value="THCS_BS">Bằng tốt nghiệp THCS - Bản sao</option>
                                    <option value="THPT_BG">Bằng tốt nghiệp THPT - Bản gốc</option>
                                    <option value="THPT_BS">Bằng tốt nghiệp THPT - Bản sao</option> --}}
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12">
                        <div class="mb-3">
                            <label class="form-label" for="CanCuPhapLy">Căn cứ pháp lý</label>
                            <textarea class="form-control" id="CanCuPhapLy" rows="2"></textarea>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12">
                        <div class="mb-3">
                            <label class="form-label" for="MoTa">Mô tả</label>
                            <textarea class="form-control" id="MoTa" rows="4"></textarea>
                        </div>
                    </div>
                </div>
                <div class="row d-none">
                    <div class="col-md-12">
                        <label class="form-label" style="margin-bottom: 8px;">Hình nền</label>
                        <div id="drop-area" class="drop-area"
                            style="padding: 15px; border-radius: 5px; background-color: #f9f9f9; display: flex; align-items: center; justify-content: space-between;">
                            <div id="divButtonUpload_" style="display: flex; align-items: center; margin-right: 10px;">
                                <i class="fa fa-cloud-upload" style="font-size: 40px; color: #888;"></i>
                            </div>
                            <div style="flex-grow: 1;">
                                <p style="margin: 0; font-size: 14px; color: #555;">Chọn 1 file hoặc kéo thả vào đây
                                </p>
                                <p style="margin: 0; font-size: 12px; color: #999;">File size không lớn hơn
                                    10MB</p>
                            </div>
                            <button id="btnChonTepVB" type="button"
                                style="background-color: #448fdc; color: #fff; border: none; padding: 8px 15px; border-radius: 3px; cursor: pointer; display: flex; align-items: center;">
                                <i class="fa fa-upload" style="margin-right: 5px;"></i>Chọn file
                            </button>
                        </div>
                        <div id="list-file-v-b" style="margin-top:4px"></div>
                        <input type="hidden" id="txtDuongDanFileVB" value="">
                        <input type="file" id="fileVB" class="d-none" accept=".jpg,.png" />
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div style="display: contents; align-items: center; justify-content: space-between">
                    <div class="col-md-6" style="display: flex; align-items: center">
                        <label style="margin-bottom: unset" class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="TrangThai">
                            <label class="form-check-label" for="TrangThai">Đang sử dụng</label>
                        </label>
                    </div>
                    <div class="col-md-6">
                        <div style="float:right;text-align: right">
                            <a href="#" class="nts-color-dong btn btn-outline-danger" data-bs-dismiss="modal">
                                <i class="fa fa-close"></i>&nbsp;Đóng (F4)
                            </a>
                            <a href="#" id="btnLuuVaDong" class="nts-color-luu btn btn-success ms-auto">
                                <i class="fa fa-save"></i>&ensp;Lưu và đóng (F9)
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="mdThietLap" data-bs-backdrop="static">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content" style="border-radius: 0px !important;">
            <div class="modal-header nts-modal" style=" padding-bottom: 5px !important; border-radius: 0px !important;">
                <h5 class="modal-title" id="tieuDeModalThietLap">Thiết lập mẫu văn bằng, chứng chỉ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-8" style="padding-right: 0px !important;">
                        <fieldset class="KhungVien p-3 text-dark KhungVien-MauGiayChungNhan"
                            style="padding: 10px 30px !important;">
                            <legend>Mẫu văn bằng, chứng chỉ</legend>
                            <div style="display: flex;
                                        justify-content: center;
                                        align-items: center;
                                        min-height: 100%;">
                                <div id="htmlMauGCN1" class="certificate-container">
                                    <div id="htmlMauGCN" class="certificate-container-content">
                                        <!-- nội dung chứng nhận -->
                                    </div>
                                </div>
                            </div>
                        </fieldset>
                    </div>
                    <div class="col-md-4" style="padding-left: 0px !important;">
                        <fieldset class="KhungVien KhungVien-thietlap">
                            <legend>Thiết lập mẫu văn bằng, chứng chỉ</legend>
                            <div class="editor-container">
                                <div class="row">
                                    <div class=" col-md-5" style="padding-right: 0;">
                                        <select class="form-control input-sm" id="FontFamily"></select>
                                    </div>
                                    <div class=" col-md-3" style="padding-right: 0;">
                                        <select class="form-control input-sm" id="FontSize"></select>
                                    </div>
                                    <div class="col-md-4">
                                        <button title="In đậm" href="#" class="btn btn-sm btnbold"
                                            style="border: 1px solid #cecece; width: 30px; height: 100%;">
                                            <i class="fa fa-bold" aria-hidden="true"></i>
                                        </button>
                                        <button title="In nghiên" href="#" class="btn btn-sm btnitalic"
                                            style="border: 1px solid #cecece; width: 30px; height: 100%;">
                                            <i class="fa fa-italic" aria-hidden="true"></i>
                                        </button>
                                        <button title="Gạch chân" href="#" class="btn btn-sm btnunderline"
                                            style="border: 1px solid #cecece; width: 30px; height: 100%;">
                                            <i class="fa fa-underline" aria-hidden="true"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class=" col-md-12"
                                        style="padding-right: 0; display: flex; align-items: center; justify-content: flex-start;height:30px;margin-top:4px ">
                                        <button title="In hoa" href="#" class="btn btn-sm btninhoa"
                                            style="border: 1px solid #cecece; width: 30px; margin-right: 4px; height: 100%;">
                                            <i class="fa fa-font" aria-hidden="true"></i>
                                        </button>
                                        <input title="Màu chữ" type="color" id="yourColorPicker"
                                            oninput="updateColorPicker();"
                                            style="height: 100%;; width: 30px; margin-right: 4px;" />
                                        <button title="Canh trái" href="#" class="btn btn-sm btnleft"
                                            style="border: 1px solid #cecece; width: 30px; margin-right: 4px; height: 100%;">
                                            <i class="fa fa-align-left" aria-hidden="true"></i>
                                        </button>
                                        <button title="Canh giữa" href="#" class="btn btn-sm btncenter"
                                            style="border: 1px solid #cecece; width: 30px; margin-right: 4px; height: 100%;">
                                            <i class="fa fa-align-center" aria-hidden="true"></i>
                                        </button>
                                        <button title="Canh phải" href="#" class="btn btn-sm btnright"
                                            style="border: 1px solid #cecece; width: 30px; margin-right: 4px; height: 100%;">
                                            <i class="fa fa-align-right" aria-hidden="true"></i>
                                        </button>
                                        <label class="form-label">
                                            Khoảng cách lề: &nbsp;</label>
                                        <input type="text" class="form-control number-format Margin-change"
                                            placeholder="Trên" style="width: 40px; margin-right: 4px;" id="Margin1">
                                        <input type="text" class="form-control number-format Margin-change"
                                            placeholder="Phải" style="width: 40px; margin-right: 4px;" id="Margin2">
                                        <input type="text" class="form-control number-format Margin-change"
                                            placeholder="Dưới" style="width: 40px; margin-right: 4px;" id="Margin3">
                                        <input type="text" class="form-control number-format Margin-change"
                                            placeholder="Trái" style="width: 40px; margin-right: 4px;" id="Margin4">
                                    </div>
                                </div>
                                <div class="line-modal mt-2">
                                    <div class="text-line-modal">
                                        <div class="text-line">Danh sách chỉ tiêu</div>
                                    </div>
                                </div>
                                <ul class="text-dark fs-6" id="dsChiTieu" style="height:57vh;overflow:scroll">
                                </ul>
                            </div>
                        </fieldset>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div style="display: contents; align-items: center; justify-content: space-between">
                    <div class="col-md-6" style="display: flex; align-items: center">
                    </div>
                    <div class="col-md-6">
                        <div style="float:right;text-align: right">
                            <a href="#" id="btnXuatWord" class="nts-color-luu btn btn-info ms-auto">
                                <i class="fa fa-file-word-o"></i>&ensp;Xuất word
                            </a>
                            <a href="#" class="nts-color-dong btn btn-outline-danger" data-bs-dismiss="modal">
                                <i class="fa fa-close"></i>&nbsp;Đóng (F4)
                            </a>
                            <a href="#" id="btnLuuVaDongThietLap" class="nts-color-luu btn btn-success ms-auto">
                                <i class="fa fa-save"></i>&ensp;Lưu và đóng (F9)
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@include('layouts.ModalPrint_Full')
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/html-docx-js/dist/html-docx.min.js"></script>

<script>
window.Laravel = window.Laravel || {};
window.Laravel.local = {
    maTuTangUrl: `{{ route('dungchung.lay-ma-tutang', ['kyhieuLoaiPhieu' => 'MaMauVanBangChungChi', 'bangDuLieu' => 'mau_van_bang_chung_chi_s', 'cotDuLieu' => 'MaMauVanBangChungChi']) }}`,
    KiemTraXoa: `{{ route('dungchung.kiem-tra-xoa-v1') }}`,
    GetFontFamily_Combo: `{{ route('mauvanbangchungchi.getfontfamily') }}`,
    GetFontSize_Combo: `{{ route('mauvanbangchungchi.getfontsize') }}`,
    KiemTraMauCT: `{{ route('mauvanbangchungchi.KiemTraMauCT') }}`,
    InsertMauGCNCT: `{{ route('mauvanbangchungchi.InsertMauGCNCT') }}`,
    GetAllCT: `{{ route('mauvanbangchungchi.GetAllCT') }}`,
    getloaiphoivanbangchungchi: "{{ route('loaiphoivanbangchungchi.getloaiphoivanbangchungchi') }}",
};
</script>
<script src="{{ asset('js/danhmuc/mauvanbangchungchi.js') }}?v={{ time() }}"></script>
@endpush