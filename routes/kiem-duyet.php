<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CongThongTin\KiemDuyetTinTucController;

/*
|--------------------------------------------------------------------------
| Kiểm Duyệt Routes
|--------------------------------------------------------------------------
|
| Các route cho chức năng kiểm duyệt tin tức
|
*/

Route::prefix('admin/kiem-duyet')->middleware(['auth'])->group(function () {
    
    // Danh sách tin tức cần kiểm duyệt
    Route::get('/', [KiemDuyetTinTucController::class, 'index'])->name('kiem-duyet.index');
    
    // Xem chi tiết tin tức để kiểm duyệt
    Route::get('/tin-tuc/{id}', [KiemDuyetTinTucController::class, 'show'])->name('kiem-duyet.show');
    
    // Duyệt tin tức
    Route::post('/tin-tuc/{id}/duyet', [KiemDuyetTinTucController::class, 'duyet'])->name('kiem-duyet.duyet');
    
    // Từ chối tin tức
    Route::post('/tin-tuc/{id}/tu-choi', [KiemDuyetTinTucController::class, 'tuChoi'])->name('kiem-duyet.tu-choi');
    
    // Cập nhật thiết lập kiểm duyệt
    Route::post('/thiet-lap', [KiemDuyetTinTucController::class, 'capNhatThietLap'])->name('kiem-duyet.thiet-lap');
    
    // Thống kê kiểm duyệt
    Route::get('/thong-ke', [KiemDuyetTinTucController::class, 'thongKe'])->name('kiem-duyet.thong-ke');
    
});
