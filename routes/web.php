<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\SoHoa\HoSoController;
use App\Http\Controllers\DanhMuc\LyDoController;
use App\Http\Controllers\HeThong\AuthController;
use App\Http\Controllers\hethong\LogsController;
use App\Http\Controllers\DanhMuc\DonViController;
use App\Http\Controllers\DanhMuc\KyThiController;
use App\Http\Controllers\SoHoa\HopHoSoController;
use App\Http\Controllers\DanhMuc\CapHocController;
use App\Http\Controllers\DanhMuc\ChucVuController;
use App\Http\Controllers\DanhMuc\DanTocController;
use App\Http\Controllers\DanhMuc\MonHocController;
use App\Http\Controllers\DanhMuc\HoiDongController;
use App\Http\Controllers\DanhMuc\KhoaThiController;
use App\Http\Controllers\DanhMuc\NgonNguController;
use App\Http\Controllers\DanhMuc\TieuChiController;
use App\Http\Controllers\DanhMuc\TonGiaoController;
use App\Http\Controllers\DanhMuc\XepLoaiController;
use App\Http\Controllers\HeThong\ProfileController;
use App\Http\Controllers\QuanLy\doituongController;
use App\Http\Controllers\BaoCao\BaoCaoMauController;
use App\Http\Controllers\DanhMuc\HeDaoTaoController;
use App\Http\Controllers\DanhMuc\KeLuuTruController;
use App\Http\Controllers\DanhMuc\NhanVienController;
use App\Http\Controllers\DanhMuc\PhongBanController;
use App\Http\Controllers\DanhMuc\QuocTichController;
use App\Http\Controllers\DungChung\QuanLyController;
use App\Http\Controllers\HeThong\ChucNangController;
use App\Http\Controllers\SoHoa\TraCuuHoSoController;
use App\Http\Controllers\DanhMuc\DonViTinhController;
use App\Http\Controllers\DanhMuc\KhoLuuTruController;
use App\Http\Controllers\DanhMuc\TrangThaiController;
use App\Http\Controllers\DungChung\DanhMucController;
use App\Http\Controllers\HeThong\NguoiDungController;
use App\Http\Controllers\DanhMuc\LoaiLuuTruController;
use App\Http\Controllers\DanhMuc\LoaiVanBanController;
use App\Http\Controllers\HeThong\DoiMatKhauController;
use App\Http\Controllers\HeThong\KiemTraXoaController;
use App\Http\Controllers\QuanLy\HuyPhoiVBCCController;
use App\Http\Controllers\DanhMuc\CheDoSuDungController;
use App\Http\Controllers\DanhMuc\LoaiChungTuController;
use App\Http\Controllers\DanhMuc\MucDoTinCayController;
use App\Http\Controllers\DanhMuc\NhomTaiLieuController;
use App\Http\Controllers\DanhMuc\PhongLuuTruController;
use App\Http\Controllers\DungChung\DungChungController;
use App\Http\Controllers\DanhMuc\LoaiDiaBanHCController;
use App\Http\Controllers\BaoCao\DanhSachBaoCaoController;
use App\Http\Controllers\DanhMuc\LoaiHinhDonViController;
use App\Http\Controllers\HeThong\NhomNguoiDungController;
use App\Http\Controllers\DanhMuc\HinhThucDaoTaoController;
use App\Http\Controllers\DanhMuc\TinhTrangVatLyController;
use App\Http\Controllers\HeThong\NhatKyDangNhapController;
use App\Http\Controllers\QuanLy\CapPhatPhoiVBCCController;
use App\Http\Controllers\DanhMuc\DiaBanHanhChinhController;
use App\Http\Controllers\DungChung\GenericExportController;
use App\Http\Controllers\HeThong\ThietLapHeThongController;
use App\Http\Controllers\QuanLy\TiepNhanPhoiVBCCController;
use App\Http\Controllers\BaoCao\ThietLapMauBaoCaoController;
use App\Http\Controllers\QuanLy\HoSoTotNghiepTHPTController;
use App\Http\Controllers\CongThongTin\CongThongTinController;
use App\Http\Controllers\DanhMuc\MauVanBangChungChiController;
use App\Http\Controllers\SoHoa\TraCuuHoSoDangThuMucController;
use App\Http\Controllers\DanhMuc\LoaiPhoiVanBangChungChiController;
use App\Http\Controllers\BaoCao\DungChungController as DungChungBaoCao;
use App\Http\Controllers\BienTapCongThongTin\TinTucController as BienTapTinTucController;
use App\Http\Controllers\BienTapCongThongTin\LoaiTinTucController as BienTapLoaiTinTucController;
use App\Http\Controllers\TraCuu\TraCuuVBCCController;
use App\Http\Controllers\QuanLy\CapBangTotNghiepController;
use App\Http\Controllers\HeThong\ThietLapMauHeThongController;
use App\Http\Controllers\BienTapCongThongTin\TrangController as BienTapTrangController;
use App\Http\Controllers\BienTapCongThongTin\ThietLapWebsiteController as BienTapThietLapWebsiteController;
use App\Http\Controllers\QuanLy\SoGocController;













#region Guest
// ====================
// Tuyến đường công khai (Guest)
// ====================
// Chỉ cho phép người dùng chưa đăng nhập
Route::middleware('guest')->group(function () {
    Route::get('login',  [AuthController::class, 'showLogin'])->name('login');
    Route::post('login', [AuthController::class, 'login']);
    Route::get('register',  [AuthController::class, 'showRegister'])->name('register');
    Route::post('register', [AuthController::class, 'register']);
});

// ====================
// Trang chính
// ====================
Route::get('/', [CongThongTinController::class, 'index'])->name('home');

// ====================
// Cổng thông tin routes
// ====================
Route::prefix('congthongtin')->name('congthongtin.')->group(function () {
    Route::post('/contact', [CongThongTinController::class, 'contact'])->name('contact');
    Route::get('/services', [CongThongTinController::class, 'getServices'])->name('services');
    Route::get('/team', [CongThongTinController::class, 'getTeam'])->name('team');
});

// ====================
// Tin tức routes (public)
// ====================
Route::prefix('tin-tuc')->name('news.')->group(function () {
    Route::get('/', [CongThongTinController::class, 'news'])->name('index');
    Route::get('/{slug}', [CongThongTinController::class, 'show'])->name('detail');
});

// ====================
// Demo routes (public)
// ====================
Route::get('/demo-header', function () {
    return view('congthongtin.demo-header');
})->name('demo.header');

Route::get('/test-responsive-header', function () {
    // Get menu data for the header
    $loaiTinTucsMenu = \App\Models\CongThongTin\LoaiTinTuc::where('DangSD', true)
        ->where('HienThiHDSD', true)
        ->orderBy('TenLoaiTinTuc')
        ->get();

    $thietLapWebsite = \App\Models\CongThongTin\ThietLapWebsite::first();

    return view('congthongtin.test-responsive', compact('loaiTinTucsMenu', 'thietLapWebsite'));
})->name('test.responsive.header');







// Upload image for Cong Thong Tin (with basic auth but no complex middleware)
Route::middleware(['auth'])->group(function () {
    Route::post('/congthongtin/upload-image', [App\Http\Controllers\CongThongTin\DungChung\UploadFileController::class, 'uploadImage'])
        ->name('congthongtin.upload.image');
});

Route::prefix('dungchung')->name('dungchung.')->group(function () {
    //Nhóm danh mục (get combo,...)
    Route::get('getlist-nam', [DanhMucController::class, 'getNam'])->name('comboNam');
});

#endregion




// ====================
// Tuyến đường bảo vệ (Auth)
// ====================
// Chỉ cho phép người dùng đã đăng nhập
Route::middleware('auth')->group(function () {
    #region API
    Route::middleware('api')
        ->prefix('api')
        ->group(base_path('routes/api.php'));
    #endregion


    #region Dùng chung
    // Đăng xuất
    Route::post('logout', [AuthController::class, 'logout'])->name('logout');

    // ====================
    // Chức năng dùng chung
    // ====================
    Route::prefix('dungchung')->name('dungchung.')->group(function () {
        Route::get('/lay-ma-tutang/{kyhieuLoaiPhieu}/{bangDuLieu}/{cotDuLieu}', [DungChungController::class, 'LayMaTuTang'])->name('lay-ma-tutang');
        Route::get('/kiem-tra-ton-tai/{collection}/{field}/{value}', [DungChungController::class, 'kiemTraTonTai'])->name('kiemTraTonTai');
        Route::post('/kiem-tra-ton-tai-nhieu-cot',            [DungChungController::class, 'kiemTraTonTaiNhieuCot'])->name('kiemTraTonTaiNhieuCot');
        Route::get('/kiem-tra-phu-thuoc',                    [DungChungController::class, 'kiemTraPhuThuoc'])->name('kiemTraPhuThuoc');
        Route::get('/kiem-tra-xoa',                          [DungChungController::class, 'kiemTraXoa'])->name('kiemTraXoa');
        Route::get('/leftbar', [DungChungController::class, 'leftbar'])->name('left-bar');
        Route::post('/luu-dang-sd', [DungChungController::class, 'LuuDangSD'])->name('luu-dang-sd');
        Route::post('/luu-dang-sd-nhieu', [DungChungController::class, 'LuuDangSDNhieu'])->name('luu-dang-sd-nhieu');
        //kiemTraXoaV1
        Route::get('/kiemTraXoaV1', [DungChungController::class, 'kiemTraXoaV1'])->name('kiem-tra-xoa-v1');
        Route::get('/quyen/{permissionKey}', [DungChungController::class, 'getQuyen'])->name('getPermission');
        Route::get('/niendo', [DungChungController::class, 'getNienDoFromSession'])->name('niendo');

        //Nhóm danh mục (get combo,...)
        Route::prefix('danhmuc')->name('danhmuc.')->group(function () {
            Route::get('getlist-phongban', [DanhMucController::class, 'getPhongBanList'])->name('comboPhongBan');
            Route::get('getlist-donvi', [DanhMucController::class, 'getDonViList'])->name('comboDonVi');
            Route::get('getlist-gioitinh', [DanhMucController::class, 'getGioiTinhList'])->name('comboGioiTinh');
            Route::get('getlist-chucvu', [DanhMucController::class, 'getChucVuList'])->name('comboChucVu');
            Route::get('getlist-nhanvien', [DanhMucController::class, 'getNhanVienList'])->name('comboNhanVien');
            Route::get('getlist-loaihinhdv', [DanhMucController::class, 'getLoaiHinhDonViList'])->name('comboLoaiHinhDonVi');
            Route::get('getlist-monhoc', [DanhMucController::class, 'getListMonHoc'])->name('comboMonHoc');
            Route::get('getlist-captochuc', [DanhMucController::class, 'getListCapToChuc'])->name('comboCapToChuc');
            Route::get('getlist-kythi', [DanhMucController::class, 'getListKyThi'])->name('comboKyThi');
            Route::get('getlist-khoathi', [DanhMucController::class, 'getListKhoaThi'])->name('comboKhoaThi');
            Route::get('getlist-quyetdinh', [DanhMucController::class, 'getListQuyetDinh'])->name('comboQuyetDinh');
            Route::get('getlist-user', [DanhMucController::class, 'getListUserByDonVi'])->name('comboUser');
            Route::get('getlist-donvi_tree', [DanhMucController::class, 'GetListDonVi_tree'])->name('comboDonVi_tree');
            Route::get('getlist-donvi', [DanhMucController::class, 'GetListDonVi'])->name('comboDonVi');
            Route::get('getlist-hinhthucdaotao', [DanhMucController::class, 'getListHinhThucDaoTao'])->name('comboHTDT');
            Route::get('getlist-hoidong', [DanhMucController::class, 'GetListHoiDong'])->name('comboHoiDong');
            Route::get('getlist-hocsinh', [DanhMucController::class, 'getDoiTuongList'])->name('comboHocSinh');
            Route::get('getlist-xeploai', [DanhMucController::class, 'getListXepLoai'])->name('comboXepLoai');
            Route::get('getlist-dvhs', [DanhMucController::class, 'GetListDonViCoHocSinh'])->name('GetListDonViCoHocSinh');
            Route::get('getlist-hsdv', [DanhMucController::class, 'GetListHocSinhByDonVi'])->name('GetListHocSinhByDonVi');
            Route::get('getlist-caphoc', [DanhMucController::class, 'getListCapHoc'])->name('comboCapHoc');
            Route::get('getlist-sogoc', [DanhMucController::class, 'getListSoGoc'])->name('comboSoGoc');
            Route::get('getlist-dienuutien', [DanhMucController::class, 'getListDienUuTien'])->name('comboDienUuTien');
        });
    });


    // ====================
    // Xuất dữ liệu
    // ====================
    Route::prefix('export')->name('export.')->group(function () {
        Route::post('excel_v2', [GenericExportController::class, 'excel_v2'])->name('excel_v2');
        Route::post('excel',    [GenericExportController::class, 'excel'])->name('excel');
        Route::post('pdf',      [GenericExportController::class, 'pdf'])->name('pdf');
        Route::post('pdf_v2',   [GenericExportController::class, 'pdf_v2'])->name('pdf_v2');
    });
    #endregion

    #region Hệ thống
    Route::prefix('hethong')->name('hethong.')->group(function () {

        Route::prefix('doimatkhau')->group(function () {
            Route::get('/', [DoiMatKhauController::class, 'index']);
            Route::post('/doimatkhau', [DoiMatKhauController::class, 'doimatkhau'])->name('doimatkhau');
        });

        Route::prefix('profile')->name('profile.')->group(function () {
            Route::get('/', [ProfileController::class, 'index']);
            Route::get('get-user-hientai', [ProfileController::class, 'getInfo'])->name('getUser');
            Route::get('get-nhanvien', [ProfileController::class, 'getNhanVienFromID'])->name('getNhanVien');
            Route::post('avatar-url', [ProfileController::class, 'updateAvatarUrl'])->name('updateAvatar');
            Route::post('luuthongtin', action: [ProfileController::class, 'luuThongTin'])->name('luuThongTin');
            Route::put('updateNienDo', [ProfileController::class, 'updateNienDo'])->name('updateNienDo');
        });

        //Nhật ký đăng nhập
        Route::prefix('nhatkydangnhap')->group(function () {
            Route::get('/', [NhatKyDangNhapController::class, 'index']);
            Route::get('/get-users', [NhatKyDangNhapController::class, 'getAllDanhSachNguoiDung'])->name('getAllDanhSachNguoiDung');
            Route::get('/get-logs',  [NhatKyDangNhapController::class, 'getAllNhatKySuDung'])->name('getAllNhatKySuDung');
            Route::get('/load-units', [NhatKyDangNhapController::class, 'loadDonVi'])->name('loadDonVi');
            Route::get('/get-by-unit', [NhatKyDangNhapController::class, 'getDonVi'])->name('getDonVi');
            Route::get('/log-details', [NhatKyDangNhapController::class, 'getLogDetails'])->name('getChiTietLog');
        });

        //Nhóm người dùng
        Route::prefix('nhomnguoidung')
            ->name('nhomnguoidung.')
            ->group(function () {

                Route::get('/', [NhomNguoiDungController::class, 'index'])
                    ->name('index');

                Route::get('/get-all', [NhomNguoiDungController::class, 'getAll'])
                    ->name('getAll');

                Route::get('/get-permissions/{id}', [NhomNguoiDungController::class, 'getPermissions'])
                    ->name('getPermissions');

                Route::get('/get-all-functions/{id}', [NhomNguoiDungController::class, 'getAllFunctions'])
                    ->name('getAllFunctions');

                Route::post('/add-functions', [NhomNguoiDungController::class, 'addFunctions'])
                    ->name('addFunctions');

                Route::put('/update-permission', [NhomNguoiDungController::class, 'updatePermission'])
                    ->name('updatePermission');

                Route::post('/save-groups', [NhomNguoiDungController::class, 'saveUserGroups'])
                    ->name('saveUserGroups');

                Route::delete('/delete-group/{id}', [NhomNguoiDungController::class, 'deleteGroup'])
                    ->name('deleteGroup');

                Route::delete('/delete-function/{id}', [NhomNguoiDungController::class, 'deleteFunction'])
                    ->name('deleteFunction');

                Route::put('/update-name', [NhomNguoiDungController::class, 'updateGroupName'])
                    ->name('updateGroupName');

                Route::put('/update-code', [NhomNguoiDungController::class, 'updateGroupCode'])
                    ->name('updateGroupCode');

                Route::post('/refresh-users/{id}', [NhomNguoiDungController::class, 'refreshUsersByGroup'])
                    ->name('refreshUsersByGroup');
            });

        //Người dùng
        Route::prefix('nguoidung')->name('nguoidung.')->group(function () {
            Route::get('/',                        [NguoiDungController::class, 'index']);
            Route::get('/get-all',                 [NguoiDungController::class, 'getAll'])->name('getAll');
            Route::get('/get-permissions/{id}',    [NguoiDungController::class, 'getPermissions'])->name('getPermiss');
            Route::get('/get-all-functions',  [NguoiDungController::class, 'getAllChucNang'])->name('getChucNang');
            Route::post('/luuthongtin',          [NguoiDungController::class, 'LuuThongTin'])->name('luuThongTin');
            Route::post('/add-functions',          [NguoiDungController::class, 'themChucNang'])->name('themChucNang');
            Route::put('/update-permission',      [NguoiDungController::class, 'updatePermission'])->name('luuThongTinQuyen');
            Route::put('/update-password',      [NguoiDungController::class, 'doiMatKhau'])->name('doiMatKhau');
            Route::post('/save-groups',            [NguoiDungController::class, 'saveUsers'])->name('luuThongTinUser');
            Route::delete('/delete-group/{id}',      [NguoiDungController::class, 'xoaUser'])->name('xoaUser');
            Route::delete('/delete-function/{id}',   [NguoiDungController::class, 'xoaChucNang'])->name('xoaChucNang');
            Route::get('loaddulieusua/{id}',             [NguoiDungController::class, 'loadDuLieuSua'])->name('loadDuLieuSua');
            Route::get('GetAllDonVi',             [NguoiDungController::class, 'getAllDonVi'])->name('getAllDonVi');
            Route::get('nhanvien/{donviId}', [NguoiDungController::class, 'getAllNhanVien'])->name('getAllNhanVien');
            Route::get('phongban',          [NguoiDungController::class, 'getAllPhongBan'])->name('getAllPhongBan');
            Route::get('GetAllNhomNguoiDung',     [NguoiDungController::class, 'getAllNhomNguoiDung'])->name('getAllNhomNguoiDung');
            Route::delete('bulk-delete-users',      [NguoiDungController::class, 'bulkDeleteUser'])->name('bulkDeleteUser');
        });

        Route::prefix('logs')->name('logs.')->group(function () {
            Route::get('/',                        [LogsController::class, 'index']);
            Route::get('/get-all',                 [LogsController::class, 'getAll'])->name('getAll');
        });
    });

    Route::module('hethong', 'chucnang', ChucNangController::class);
    Route::prefix('hethong/chucnang')->name('chucnang.')->group(function () {
        Route::post('getallchucnang', [ChucNangController::class, 'getallchucnang'])->name('cn.getallchucnang');
        Route::post('getchucnangbaocao', [ChucNangController::class, 'getchucnangbaocao'])->name('cn.getchucnangbaocao');
        Route::get('kiemTraXoa', [ChucNangController::class, 'kiemTraXoa'])->name('cn.kiemTraXoa');
    });

    // ====================
    // Thiết lập hệ thống
    // ====================
    Route::prefix('hethong/thietlaphethong')->name('thietlaphethong.')->group(function () {
        Route::get('/',                [ThietLapHeThongController::class, 'index'])->name('index');
        Route::get('/getdata',                [ThietLapHeThongController::class, 'getdata'])->name('getdata');
        Route::post('/luuthongtin', [ThietLapHeThongController::class, 'luuthongtin'])->name('luuthongtin');
        Route::post('luuthongtinCauHinh', [ThietLapHeThongController::class, 'luuthongtinCauHinh'])->name('luuthongtinCauHinh');
        Route::get('/getdataCauHinh',                [ThietLapHeThongController::class, 'getdataCauHinh'])->name('getdataCauHinh');
        Route::get('/moveFileToPermanent',                [ThietLapHeThongController::class, 'moveFileToPermanent'])->name('moveFileToPermanent');
    });
    // ====================
    // Kiểm tra xóa
    // ====================
    Route::module('hethong', 'kiemtraxoa', KiemTraXoaController::class);

     // ====================
    // Thiết lập màu hệ thống
    // ====================
    Route::module('hethong', 'thietlapmauhethong', ThietLapMauHeThongController::class);
    Route::prefix('hethong/thietlapmauhethong')->name('thietlapmauhethong.')->group(function () {
        Route::get('/',                [ThietLapMauHeThongController::class, 'index'])->name('index');
        Route::get('/getdata',                [ThietLapMauHeThongController::class, 'getdata'])->name('getdata');
        Route::post('/luuthongtin', [ThietLapMauHeThongController::class, 'luuthongtin'])->name('luuthongtin');
    });
    #endregion





    #region Danh mục
    // ====================
    // Danh mục nhân viên
    // ====================
    // Route::module('danhmuc','nhanvien', NhanVienController::class);
    Route::module('danhmuc', 'nhanvien', NhanVienController::class);
    Route::prefix('danhmuc/nhanvien')->name('nhanvien.')->group(function () {
        Route::get('xuatword', [DonViTinhController::class, 'xuatword'])->name('xuat.word');
        Route::get('xuatexcel', [DonViTinhController::class, 'xuatexcel'])->name('xuat.excel');
        Route::post('check-excel',     [NhanVienController::class, 'checkExcel'])->name('checkExcel');
        Route::post('load-excel',      [NhanVienController::class, 'loadExcel'])->name('loadExcel');
        Route::post('import-excel',    [NhanVienController::class, 'importExcel'])->name('importExcel');
        Route::get('template',         [NhanVienController::class, 'downloadTemplate'])->name('template');
        Route::post('load-sheet-names', [NhanVienController::class, 'loadSheetNames'])->name('loadSheetNames');
    });

    Route::module('danhmuc', 'loaichungtu', LoaiChungTuController::class);


    // ====================
    // Danh mục đơn vị tính
    // ====================
    Route::module('danhmuc', 'donvitinh', DonViTinhController::class);
    Route::prefix('danhmuc/donvitinh')->name('donvitinh.')->group(function () {
        Route::get('/xuatword', [DonViTinhController::class, 'xuatword'])->name('xuat.word');
        Route::get('/xuatexcel', [DonViTinhController::class, 'xuatexcel'])->name('xuat.excel');
        Route::post('getdonvitinh', [LoaiDiaBanHCController::class, 'getdonvitinh'])->name('dvt.getdonvitinh');
    });

    // ====================
    // Danh mục địa bàn hành chính
    // ====================
    Route::module('danhmuc', 'diabanhanhchinh', DiaBanHanhChinhController::class);
    Route::prefix('danhmuc/diabanhanhchinh')->name('diabanhanhchinh.')->group(function () {
        Route::get('kiemTraXoa', [DiaBanHanhChinhController::class, 'kiemTraXoa'])->name('db.kiemTraXoa');
        Route::get('loadDatachart', [DiaBanHanhChinhController::class, 'loadDatachart'])->name('db.loadDatachart');
        Route::post('getdiabanhc_byloai', [DiaBanHanhChinhController::class, 'getdiabanhc_byloai'])->name('db.getdiabanhc_byloai');
        Route::post('getdiabanhc_tinh', [DiaBanHanhChinhController::class, 'getdiabanhc_tinh'])->name('db.getdiabanhc_tinh');
        Route::post('getdiabanhc_byidcha', [DiaBanHanhChinhController::class, 'getdiabanhc_byidcha'])->name('db.getdiabanhc_byidcha');

        Route::post('check-excel',     [DiaBanHanhChinhController::class, 'checkExcel'])->name('checkExcel');
        Route::post('load-excel',      [DiaBanHanhChinhController::class, 'loadExcel'])->name('loadExcel');
        Route::post('importExcel',    [DiaBanHanhChinhController::class, 'importExcel'])->name('importExcel');
        Route::get('TaiFileMau',         [DiaBanHanhChinhController::class, 'downloadTemplate'])->name('TaiFileMau');
        Route::post('load-sheet-names', [DiaBanHanhChinhController::class, 'loadSheetNames'])->name('loadSheetNames');
    });

    // ====================
    // Danh mục loại địa bàn hành chính
    // ====================
    Route::module('danhmuc', 'loaidiabanhc', LoaiDiaBanHCController::class);
    Route::prefix('danhmuc/loaidiabanhc')->name('loaidiabanhc.')->group(function () {
        Route::post('getloaidiabanhc', [LoaiDiaBanHCController::class, 'getloaidiabanhc'])->name('ldb.getloaidiabanhc');
    });

    // ====================
    // Danh mục đơn vị
    // ====================
    Route::module('danhmuc', 'donvi', DonViController::class);
    Route::prefix('danhmuc/donvi')->name('donvi.')->group(function () {
        Route::post('getalldonvi', [DonViController::class, 'getalldonvi'])->name('dv.getalldonvi');
        Route::get('kiemTraXoa', [DonViController::class, 'kiemTraXoa'])->name('dv.kiemTraXoa');

        Route::post('check-excel',     [DonViController::class, 'checkExcel'])->name('checkExcel');
        Route::post('load-excel',      [DonViController::class, 'loadExcel'])->name('loadExcel');
        Route::post('importExcel',    [DonViController::class, 'importExcel'])->name('importExcel');
        Route::get('TaiFileMau',         [DonViController::class, 'downloadTemplate'])->name('TaiFileMau');
        Route::post('load-sheet-names', [DonViController::class, 'loadSheetNames'])->name('loadSheetNames');
    });

    // ====================
    // Danh mục chức vụ
    // ====================
    Route::module('danhmuc', 'chucvu', ChucVuController::class);

    // ====================
    // Danh mục loại hình đơn vị
    // ====================
    Route::module('danhmuc', 'loaihinhdonvi', LoaiHinhDonViController::class);

    // ====================
    // Danh mục môn học
    // ====================
    Route::module('danhmuc', 'monhoc', MonHocController::class);
    Route::prefix('danhmuc/monhoc')->name('monhoc.')->group(function () {
        Route::post('check-excel',     [MonHocController::class, 'checkExcel'])->name('checkExcel');
        Route::post('load-excel',      [MonHocController::class, 'loadExcel'])->name('loadExcel');
        Route::post('importExcel',    [MonHocController::class, 'importExcel'])->name('importExcel');
        Route::get('TaiFileMau',         [MonHocController::class, 'downloadTemplate'])->name('TaiFileMau');
        Route::post('load-sheet-names', [MonHocController::class, 'loadSheetNames'])->name('loadSheetNames');
    });
    // ====================
    // Danh mục cấp học
    // ====================
    Route::module('danhmuc', 'caphoc', CapHocController::class);
    Route::prefix('danhmuc/caphoc')->name('caphoc.')->group(function () {
        Route::post('getcaphoc', [CapHocController::class, 'getcaphoc'])->name('ldb.getcaphoc');
    });

    // ====================
    // Danh mục xếp loại
    // ====================
    Route::module('danhmuc', 'xeploai', XepLoaiController::class);

    // ====================
    // Danh mục kỳ thi
    // ====================
    Route::module('danhmuc', 'kythi', KyThiController::class);
    Route::prefix('danhmuc/kythi')->name('kythi.')->group(function () {
        Route::delete('bulkDelete', [KyThiController::class, 'bulkDelete'])->name('bulkDelete');
    });
    Route::module('danhmuc', 'khoathi', KhoaThiController::class);
    Route::prefix('danhmuc/khoathi')->name('khoathi.')->group(function () {
        Route::delete('bulkDelete', [KhoaThiController::class, 'bulkDelete'])->name('bulkDelete');
    });
    // ====================

    // ====================
    // Danh mục kho lưu trữ
    // ====================
    Route::module('danhmuc', 'kholuutru', KhoLuuTruController::class);
    Route::prefix('danhmuc/kholuutru')->name('kholuutru.')->group(function () {
        Route::post('getkholuutru', [KhoLuuTruController::class, 'getkholuutru'])->name('ldb.getkholuutru');
    });

    // ====================
    // Danh mục kệ lưu trữ
    // ====================
    Route::module('danhmuc', 'keluutru', KeLuuTruController::class);
    Route::prefix('danhmuc/keluutru')->name('keluutru.')->group(function () {
        Route::post('getkeluutru', [KeLuuTruController::class, 'getkeluutru'])->name('ldb.getkeluutru');
        Route::post('getkeluutru_theoKho', [KeLuuTruController::class, 'getkeluutru_theoKho'])->name('ldb.getkeluutru_theoKho');
    });

    // ====================
    // Danh mục loại văn bản
    // ====================
    Route::module('danhmuc', 'loaivanban', LoaiVanBanController::class);
    Route::prefix('danhmuc/loaivanban')->name('loaivanban.')->group(function () {
        Route::post('getloaivanban', [LoaiVanBanController::class, 'getloaivanban'])->name('ldb.getloaivanban');
    });

    // ====================
    // Danh mục mức độ tin cậy
    // ====================
    Route::module('danhmuc', 'mucdotincay', MucDoTinCayController::class);
    Route::prefix('danhmuc/mucdotincay')->name('mucdotincay.')->group(function () {
        Route::post('getmucdotincay', [MucDoTinCayController::class, 'getmucdotincay'])->name('ldb.getmucdotincay');
    });

    // ====================
    // Danh mục nhóm tài liệu
    // ====================
    Route::module('danhmuc', 'nhomtailieu', NhomTaiLieuController::class);
    Route::prefix('danhmuc/nhomtailieu')->name('nhomtailieu.')->group(function () {
        Route::post('getnhomtailieu', [NhomTaiLieuController::class, 'getnhomtailieu'])->name('ldb.getnhomtailieu');
        Route::get('getMaNhomTaiLieu', [NhomTaiLieuController::class, 'getMaNhomTaiLieu'])->name('ldb.getMaNhomTaiLieu');
    });

    // ====================
    // Danh mục chế độ sử dụng
    // ====================
    Route::module('danhmuc', 'chedosudung', CheDoSuDungController::class);
    Route::prefix('danhmuc/chedosudung')->name('chedosudung.')->group(function () {
        Route::post('getchedosudung', [CheDoSuDungController::class, 'getchedosudung'])->name('ldb.getchedosudung');
    });

    // ====================
    // Danh mục tình trạng vật lý
    // ====================
    Route::module('danhmuc', 'tinhtrangvatly', TinhTrangVatLyController::class);
    Route::prefix('danhmuc/tinhtrangvatly')->name('tinhtrangvatly.')->group(function () {
        Route::post('gettinhtrangvatly', [TinhTrangVatLyController::class, 'gettinhtrangvatly'])->name('ldb.gettinhtrangvatly');
    });

    // ====================
    // Danh mục ngôn ngữ
    // ====================
    Route::module('danhmuc', 'ngonngu', NgonNguController::class);
    Route::prefix('danhmuc/ngonngu')->name('ngonngu.')->group(function () {
        Route::post('getngonngu', [NgonNguController::class, 'getngonngu'])->name('ldb.getngonngu');
    });

    // ====================
    // Danh mục Phông lưu trữ
    // ====================
    Route::module('danhmuc', 'phongluutru', PhongLuuTruController::class);
    Route::prefix('danhmuc/phongluutru')->name('phongluutru.')->group(function () {
        Route::post('getphongluutru', [PhongLuuTruController::class, 'getphongluutru'])->name('ldb.getphongluutru');
    });

    // ====================
    // Danh mục loại luu trữ
    // ====================
    Route::module('danhmuc', 'loailuutru', LoaiLuuTruController::class);
    Route::prefix('danhmuc/loailuutru')->name('loailuutru.')->group(function () {
        Route::post('getloailuutru', [LoaiLuuTruController::class, 'getloailuutru'])->name('ldb.getloailuutru');
    });

    // ====================
    // Danh mục hình thức đào tạo
    // ====================
    Route::module('danhmuc', 'hinhthucdaotao', HinhThucDaoTaoController::class);
    Route::prefix('danhmuc/hinhthucdaotao')->name('hinhthucdaotao.')->group(function () {
        Route::post('gethinhthucdaotao', [HinhThucDaoTaoController::class, 'getHinhThucDaoTao'])->name('ldb.gethinhthucdaotao');
    });

    // ====================
    // Danh mục quốc tịch
    // ====================
    Route::module('danhmuc', 'quoctich', QuocTichController::class);
    Route::prefix('danhmuc/quoctich')->name('quoctich.')->group(function () {
        Route::post('getquoctich', [QuocTichController::class, 'getQuocTich'])->name('ldb.getquoctich');
    });

    // ====================
    // Danh mục tôn giáo
    // ====================
    Route::module('danhmuc', 'tongiao', TonGiaoController::class);
    Route::prefix('danhmuc/tongiao')->name('tongiao.')->group(function () {
        Route::post('gettongiao', [TonGiaoController::class, 'getTonGiao'])->name('ldb.gettongiao');
    });

    // ====================
    // Danh mục dân tộc
    // ====================
    Route::module('danhmuc', 'dantoc', DanTocController::class);
    Route::prefix('danhmuc/dantoc')->name('dantoc.')->group(function () {
        Route::post('getdantoc', [DanTocController::class, 'getdantoc'])->name('ldb.getdantoc');
    });



    // ====================
    // Danh mục trạng thái
    // ====================
    Route::module('danhmuc', 'trangthai', TrangThaiController::class);
    Route::prefix('danhuc/trangthai')->name('trangthai.')->group(function () {
        Route::post('gettrangthai', [TrangThaiController::class, 'gettrangthai'])->name('lbl.gettrangthai');
        Route::post('gettrangthai_khongthuocno', [TrangThaiController::class, 'gettrangthai_khongthuocno'])->name('lbl.gettrangthai_khongthuocno');
    });

    // ====================
    // Danh mục phòng ban
    // ====================
    Route::module('danhmuc', 'phongban', PhongBanController::class);
    Route::prefix('danhuc/phongban')->name('phongban.')->group(function () {
        Route::post('getphongban', [PhongBanController::class, 'getphongban'])->name('lbl.getphongban');
        Route::post('getphongban_khongthuocno', [PhongBanController::class, 'getphongban_khongthuocno'])->name('lbl.getphongban_khongthuocno');
    });

    // ====================
    // Danh mục lý do
    // ====================
    Route::module('danhmuc', 'lydo', LyDoController::class);
    Route::prefix('danhuc/lydo')->name('lydo.')->group(function () {
        Route::post('getlydo', [LyDoController::class, 'getlydo'])->name('lbl.getlydo');
        Route::post('getlydo_khongthuocno', [LyDoController::class, 'getlydo_khongthuocno'])->name('lbl.getlydo_khongthuocno');
    });
    Route::module('danhmuc', 'hoidong', HoiDongController::class);
    Route::prefix('danhmuc/hoidong')
        ->name('hoidong.')
        ->middleware('checkchucnang')
        ->controller(HoiDongController::class)
        ->group(function () {
            Route::get('all-ct', 'getAllCT')
                ->name('getAllCT');
            Route::get('nam-xet', 'getAllNamXet')
                ->name('getAllNamXet');

            Route::post('load-ct', 'loadDuLieuSuaCT')
                ->name('loadCT');

            Route::get('exists', 'kiemTraTonTai')
                ->name('exists');

            Route::post('save-ct', 'luuThongTinCT')
                ->name('saveCT');

            Route::put('save-ct', 'luuThongTinCT')
                ->name('saveCT');


            Route::delete('delete-ct', 'xoaHoiDong_ThanhPhan')
                ->name('deleteCT');

            Route::get('autocomplete-chuc-vu', 'loadDuLieuAutoComplete')
                ->name('autocompleteChucVu');
        });


    // ====================
    // Danh mục loại văn bằng chứng chỉ
    // ====================
    Route::module('danhmuc', 'mauvanbangchungchi', MauVanBangChungChiController::class);
    Route::prefix('danhmuc/mauvanbangchungchi')->name('mauvanbangchungchi.')->group(function () {
        Route::post('getmauvanbangchungchi', [MauVanBangChungChiController::class, 'getmauvanbangchungchi'])->name('lbl.getmauvanbangchungchi');
        Route::post('getfontsize', [MauVanBangChungChiController::class, 'GetFontSize_Combo'])->name('getfontsize');
        Route::post('getfontfamily', [MauVanBangChungChiController::class, 'GetFontFamily_Combo'])->name('getfontfamily');
        Route::get('KiemTraMauCT', [MauVanBangChungChiController::class, 'KiemTraMauCT'])->name('KiemTraMauCT');
        Route::post('InsertMauGCNCT', [MauVanBangChungChiController::class, 'InsertMauGCNCT'])->name('InsertMauGCNCT');
        Route::get('GetAllCT', [MauVanBangChungChiController::class, 'GetAllCT'])->name('GetAllCT');
        Route::delete('xoact', [MauVanBangChungChiController::class, 'xoact'])->name('xoact');
        Route::put('LuuThongTinCT', [MauVanBangChungChiController::class, 'LuuThongTinCT'])->name('LuuThongTinCT');
        Route::post('getFileWord', [MauVanBangChungChiController::class, 'getFileWord'])->name('getFileWord');
        Route::post('getFileWordV2', [MauVanBangChungChiController::class, 'getFileWordV2'])->name('getFileWordV2');
    });
    // ====================
    // Danh mục tiêu chí
    // ====================
    Route::module('danhmuc', 'tieuchi', TieuChiController::class);
    Route::prefix('danhmuc/tieuchi')->name('tieuchi.')->group(function () {
        Route::post('NhanBanDuLieuCT', [TieuChiController::class, 'NhanBanDuLieuCT'])->name('NhanBanDuLieuCT');
    });
    // ====================
    // Danh mục hệ đào tạo
    // ====================
    Route::module('danhmuc', 'hedaotao', HeDaoTaoController::class);
    Route::prefix('danhmuc/hedaotao')->name('hedaotao.')->group(function () {
        Route::post('gethedaotao', [HeDaoTaoController::class, 'gethedaotao'])->name('gethedaotao');
    });
    // ====================
    // Danh mục loại văn bằng chứng chỉ
    // ====================
    Route::module('danhmuc', 'loaiphoivanbangchungchi', LoaiPhoiVanBangChungChiController::class);
    Route::prefix('danhmuc/loaiphoivanbangchungchi')->name('loaiphoivanbangchungchi.')->group(function () {
        Route::post('getloaiphoivanbangchungchi', [LoaiPhoiVanBangChungChiController::class, 'getloaiphoivanbangchungchi'])->name('getloaiphoivanbangchungchi');
    });
    #endregion

    #region Số hoá
    // ====================
    // Số hóa hop hồ sơ
    // ====================
    Route::module('sohoa', 'hophoso', HopHoSoController::class);
    Route::prefix('sohoa/hophoso')->name('hophoso.')->group(function () {
        Route::post('gethophoso', [HopHoSoController::class, 'gethophoso'])->name('ldb.gethophoso');
    });

    // ====================
    // Số hóa hồ sơ
    // ====================
    Route::module('sohoa', 'hoso', HoSoController::class);
    Route::prefix('sohoa/hoso')->name('hoso.')->group(function () {
        Route::post('gethoso', [HoSoController::class, 'gethoso'])->name('ldb.gethoso');
        Route::post('getNam', [HoSoController::class, 'getNam'])->name('ldb.getNam');
    });

    // ====================
    // Số hóa tra cứu hồ sơ
    // ====================
    Route::module('sohoa', 'tracuuhoso', TraCuuHoSoController::class);

    // ====================
    // Số hóa tra cứu hồ sơ dạng thư mục
    // ====================
    Route::module('sohoa', 'tracuuhosodangthumuc', TraCuuHoSoDangThuMucController::class);
    #endregion

    #region Quản lý
    Route::prefix('quanly/dungchung')->name('quanly.dungchung.')->group(function () {
        Route::get('get-doituong-by-truonghoc/{donviId_TruongHoc}', [QuanLyController::class, 'getDoiTuongByDonViTruongHoc'])->name('getHSByTruongHoc');
        Route::get('get-truonghoc', [QuanLyController::class, 'getDanhSachTruongHoc'])
            ->name('getDanhSachTruongHoc');
    });

    // ====================
    // Quản lý học sinh
    // ====================
    Route::module('quanly', 'doituong', doituongController::class);
    Route::prefix('quanly/doituong')->name('doituong.')->group(function () {
        Route::post('getdoituong', [doituongController::class, 'getdoituong'])->name('hs.getdoituong');
    });

    Route::module('quanly', 'quyetdinhtotnghiep', HoSoTotNghiepTHPTController::class);
    Route::prefix('quanly/quyetdinhtotnghiep')->name('quyetdinhtotnghiep.')->group(function () {
        Route::put('{quyetdinhid}/banhanh', [HoSoTotNghiepTHPTController::class, 'luuthongtinBanHanh'])->name('luuthongtinBanHanh');
        Route::put('{quyetdinhid}/thuhoi', [HoSoTotNghiepTHPTController::class, 'luuthongtinThuHoi'])->name('luuthongtinThuHoi');
        Route::post('{quyetdinhid}/themmoihocsinh', [HoSoTotNghiepTHPTController::class, 'ThemMoiHocSinhTN'])->name('ThemMoiHocSinhTN');
        Route::post('luuhocsinh', [HoSoTotNghiepTHPTController::class, 'luuthongtinHocSinhTN'])->name('luuthongtinHocSinhTN');
        Route::put('luuhocsinh', [HoSoTotNghiepTHPTController::class, 'luuthongtinHocSinhTN'])->name('luuthongtinHocSinhTN');
        Route::get('{quyetdinhid}/listHocSinh', [HoSoTotNghiepTHPTController::class, 'getHocSinhTNWithDoiTuongByQuyetDinhId'])->name('listHocSinhByQuyetDinh');
        Route::get('hocsinhtn/{id}', [HoSoTotNghiepTHPTController::class, 'getHocSinhTNById'])->name('getHocSinhTNById');
        Route::delete('hocsinhtn', [HoSoTotNghiepTHPTController::class, 'xoaHocSinhTN'])->name('xoaHocSinhTN');
        Route::get('mau-excel', [HoSoTotNghiepTHPTController::class, 'downloadTemplate'])->name('getMauExcel');
        Route::post('check-excel',     [HoSoTotNghiepTHPTController::class, 'checkExcel'])->name('checkExcel');
        Route::post('load-excel',      [HoSoTotNghiepTHPTController::class, 'loadExcel'])->name('loadExcel');
        Route::post('import-excel',    [HoSoTotNghiepTHPTController::class, 'importExcel'])->name('importExcel');
        Route::post('load-sheet-names', [HoSoTotNghiepTHPTController::class, 'loadSheetNames'])->name('loadSheetNames');
    });
    // ====================
    // Quản lý Tiếp nhận, nhập kho phôi VBCC
    // ====================
    Route::module('quanly', 'tiepnhanphoivbcc', TiepNhanPhoiVBCCController::class);
    Route::prefix('quanly/tiepnhanphoivbcc')->name('tiepnhanphoivbcc.')->group(function () {
        Route::post('getTiepNhanPhoiVBCC', [TiepNhanPhoiVBCCController::class, 'getTiepNhanPhoiVBCC'])->name('getTiepNhanPhoiVBCC');
        Route::get('getMauVanBangChungChiByLoai', [TiepNhanPhoiVBCCController::class, 'getMauVanBangChungChiByLoai'])->name('getMauVanBangChungChiByLoai');
        Route::get('getAllTiepNhanPhoiVBCC', [TiepNhanPhoiVBCCController::class, 'getAllTiepNhanPhoiVBCC'])->name('getAllTiepNhanPhoiVBCC');
    });
    // ====================
    // Quản lý cấp phát, xuất kho phôi VBCC
    // ====================
    Route::module('quanly', 'capphatphoivbcc', CapPhatPhoiVBCCController::class);
    Route::module('quanly', 'capphatphoivbcc', CapPhatPhoiVBCCController::class); //getDanhSachTiepNhanCoCapPhat
    Route::prefix('quanly/capphatphoivbcc')->name('capphatphoivbcc.')->group(function () {
        Route::get('getDanhSachTiepNhanCoCapPhat', [CapPhatPhoiVBCCController::class, 'getDanhSachTiepNhanCoCapPhat'])->name('getDanhSachTiepNhanCoCapPhat');
    });
    // ====================
    // Quản lý hủy phôi VBCC
    // ====================
    Route::module('quanly', 'huyphoivbcc', HuyPhoiVBCCController::class);

    Route::module('quanly', 'huyphoivbcc', HuyPhoiVBCCController::class); //getDanhSachTiepNhanCoCapPhat
    Route::prefix('quanly/huyphoivbcc')->name('huyphoivbcc.')->group(function () {});
    // ====================
    // Quản lý sổ gốc
    // ====================
    Route::module('quanly', 'sogoc', SoGocController::class);
    Route::prefix('quanly/sogoc')->name('sogoc.')->group(function () {
        Route::post('luuthongtinct', [SoGocController::class, 'luuthongtinct'])->name('luuthongtinct');
        Route::put('luuthongtinct', [SoGocController::class, 'luuthongtinct'])->name('luuthongtinct');
        Route::Get('getallct', [SoGocController::class, 'getallct'])->name('getallct');
        Route::delete('xoact', [SoGocController::class, 'xoact'])->name('xoact');
        Route::get('loaddulieusuact', [SoGocController::class, 'loaddulieusuact'])->name('loaddulieusuact');
    });

    Route::module('quanly', 'capbangtotnghiep', CapBangTotNghiepController::class);
    Route::prefix('quanly/capbangtotnghiep')->name('capbangtotnghiep.')->group(function () {
        Route::get('GetListDonViCoHocSinh', [CapBangTotNghiepController::class, 'GetListDonViCoHocSinh'])->name('GetListDonViCoHocSinh');
        Route::get('GetListHocSinhByDonVi', [CapBangTotNghiepController::class, 'GetListHocSinhByDonVi'])->name('GetListHocSinhByDonVi');
        Route::post('luuthongtinct', [CapBangTotNghiepController::class, 'luuthongtinct'])->name('luuthongtinct');
        Route::put('luuthongtinct', [CapBangTotNghiepController::class, 'luuthongtinct'])->name('luuthongtinct');
        Route::Get('getallct', [CapBangTotNghiepController::class, 'getallct'])->name('getallct');
        Route::delete('xoact', [CapBangTotNghiepController::class, 'xoact'])->name('xoact');
        Route::get('getMauVanBang', [CapBangTotNghiepController::class, 'getMauVanBang'])->name('getMauVanBang');
        Route::post('getFileWordV2', [CapBangTotNghiepController::class, 'getFileWordV2'])->name('getFileWordV2');
    });
    Route::module('quanly', 'huyphoivbcc', HuyPhoiVBCCController::class);
    Route::prefix('quanly/huyphoivbcc')->name('huyphoivbcc.')->group(function () {
        // Add specific routes for huyphoivbcc here if needed
    });
    #endregion

    #region Biên tập công thông tin
    // ====================
    // Biên tập loại tin tức
    // ====================
    Route::module('bientapcongthongtin', 'loaitintuc', BienTapLoaiTinTucController::class);
    Route::prefix('bientapcongthongtin/loaitintuc')->name('bientapcongthongtin.loaitintuc.')->group(function () {
        Route::delete('bulkDelete', [BienTapLoaiTinTucController::class, 'bulkDelete'])->name('bulkDelete');
        Route::post('{id}/toggleStatus', [BienTapLoaiTinTucController::class, 'toggleStatus'])->name('toggleStatus');
        Route::get('getComboData', [BienTapLoaiTinTucController::class, 'getComboData'])->name('getComboData');
    });

    // ====================
    // Biên tập tin tức
    // ====================
    Route::module('bientapcongthongtin', 'tintuc', BienTapTinTucController::class);
    Route::prefix('bientapcongthongtin/tintuc')->name('bientapcongthongtin.tintuc.')->group(function () {
        Route::delete('bulkDelete', [BienTapTinTucController::class, 'bulkDelete'])->name('bulkDelete');
        Route::post('{id}/toggleStatus', [BienTapTinTucController::class, 'toggleStatus'])->name('toggleStatus');
        Route::get('getComboData', [BienTapTinTucController::class, 'getComboData'])->name('getComboData');
        Route::post('deleteOldImage', [BienTapTinTucController::class, 'deleteOldImage'])->name('deleteOldImage');
    });

    // ====================
    // Biên tập trang
    // ====================
    Route::module('bientapcongthongtin', 'trang', BienTapTrangController::class);
    Route::prefix('bientapcongthongtin/trang')->name('bientapcongthongtin.trang.')->group(function () {
        Route::post('{id}/toggleStatus', [BienTapTrangController::class, 'toggleStatus'])->name('toggleStatus');
        Route::get('generateAutoCode', [BienTapTrangController::class, 'generateAutoCode'])->name('generateAutoCode');
    });

    // ====================
    // Thiết lập website
    // ====================
    Route::prefix('bientapcongthongtin/thietlapwebsite')->name('bientapcongthongtin.thietlapwebsite.')->group(function () {
        Route::get('/', [BienTapThietLapWebsiteController::class, 'index'])->name('index');
        Route::get('/getall', [BienTapThietLapWebsiteController::class, 'getAll'])->name('getall');
        Route::get('/loaddulieusua', [BienTapThietLapWebsiteController::class, 'loadDuLieuSua'])->name('loaddulieusua');
        Route::put('/capnhat', [BienTapThietLapWebsiteController::class, 'capNhat'])->name('capnhat');
    });

    #endregion

    #region Báo cáo
    // ====================
    // Dùng chung báo cáo
    // ====================
    Route::prefix('baocao/dungchung')->group(function () {
        Route::get('GetTuyChonIn', [DungChungBaoCao::class, 'GetTuyChonIn'])->name('dungchungbaocao.GetTuyChonIn');
        Route::get('getUserGroupCode', [DungChungBaoCao::class, 'getUserGroupCode'])->name('dungchungbaocao.getUserGroupCode');
        Route::post('LuuThamSoBaoCao', [DungChungBaoCao::class, 'LuuThamSoBaoCao'])->name('dungchungbaocao.LuuThamSoBaoCao');
        Route::post('XuatBaoCao', [DungChungBaoCao::class, 'XuatBaoCao'])->name('dungchungbaocao.XuatBaoCao');
        Route::post('taoMauMacDinhUser', [DungChungBaoCao::class, 'taoMauMacDinhUser'])->name('dungchungbaocao.taoMauMacDinhUser');
        Route::get('getThietLapTheoUser', [DungChungBaoCao::class, 'getThietLapTheoUser'])->name('dungchungbaocao.getThietLapTheoUser');
        Route::get('getAllMauBaoCaoTheoUser', [DungChungBaoCao::class, 'getAllMauBaoCaoTheoUser'])->name('dungchungbaocao.getAllMauBaoCaoTheoUser');
    });
    // ====================
    // Danh sách báo cáo
    // ====================
    Route::module('baocao', 'danhsachbaocao', DanhSachBaoCaoController::class);
    Route::prefix('baocao/danhsachbaocao')->name('danhsachbaocao.')->group(function () {
        Route::get('GetBaoCaoDaLuu', [DanhSachBaoCaoController::class, 'getAllBaoCaoDaLuu'])->name('GetBaoCaoDaLuu');
        Route::put('LuuThongTin_BaoCaoDaLuu', [DanhSachBaoCaoController::class, 'LuuThongTin_BaoCaoDaLuu'])->name('LuuThongTin_BaoCaoDaLuu');
        Route::post('LuuThongTin_BaoCaoDaLuu', [DanhSachBaoCaoController::class, 'LuuThongTin_BaoCaoDaLuu'])->name('LuuThongTin_BaoCaoDaLuu');
    });
    // ====================
    // Thiết lập mẫu báo cáo
    // ====================
    Route::module('baocao', 'thietlapmaubaocao', ThietLapMauBaoCaoController::class);
    Route::prefix('baocao/thietlapmaubaocao')->name('thietlapmaubaocao.')->group(function () {
        Route::post('getMauBaoCaoThietLapCha', [ThietLapMauBaoCaoController::class, 'getMauBaoCaoThietLapCha'])->name('getMauBaoCaoThietLapCha');
        Route::get('getThietLapBaoCaoTheoMau', [ThietLapMauBaoCaoController::class, 'getThietLapBaoCaoTheoMau'])->name('getThietLapBaoCaoTheoMau');
        Route::post('CapNhatViTriDuLieuThietLap', [ThietLapMauBaoCaoController::class, 'updateViTriThietLap'])->name('CapNhatViTriDuLieuThietLap');
        Route::get('GetSoThuTuLonNhat', [ThietLapMauBaoCaoController::class, 'getNextSTTAndTenCot'])->name('GetSoThuTuLonNhat');
        Route::POST('luuthongtinthietlap', [ThietLapMauBaoCaoController::class, 'luuthongtinthietlap'])->name('luuthongtinthietlap');
        Route::PUT('luuthongtinthietlap', [ThietLapMauBaoCaoController::class, 'luuthongtinthietlap'])->name('luuthongtinthietlap');
        Route::get('getThietLapTheoID', [ThietLapMauBaoCaoController::class, 'getThietLapTheoID'])->name('getThietLapTheoID');
        Route::delete('xoathietlap', [ThietLapMauBaoCaoController::class, 'xoathietlap'])->name('xoathietlap');
        Route::PUT('luuthongtintheouser', [ThietLapMauBaoCaoController::class, 'luuthongtintheouser'])->name('luuthongtintheouser');
        Route::post('luuthongtintheouser', [ThietLapMauBaoCaoController::class, 'luuthongtintheouser'])->name('luuthongtintheouser');
    });
    ////BÁO CÁO MẪU
    Route::prefix('baocao/baocaomau')->name('baocaomau.')->group(function () {
        Route::get('/', [BaoCaoMauController::class, 'index'])->name('index');
        Route::get('ViewBaoCao', [BaoCaoMauController::class, 'ViewBaoCao'])->name('ViewBaoCao');
    });
    #endregion

    // ====================
    // Tra cúu thông tin VBCC
    // ====================
    Route::module('tracuu', 'tracuuvbcc', TraCuuVBCCController::class);
    Route::prefix('tracuu/tracuuvbcc')->name('tracuuvbcc.')->group(function () {
        Route::post('getdiabanhc_tinh', [DiaBanHanhChinhController::class, 'getdiabanhc_tinh'])->name('db.getdiabanhc_tinh');
        Route::post('getdiabanhc_byidcha', [DiaBanHanhChinhController::class, 'getdiabanhc_byidcha'])->name('db.getdiabanhc_byidcha');
    });
});
// ====================
// Test route for debugging
// ====================

// ====================
// Dynamic page routes (must be at the end)
// ====================
Route::get('/{slug}', [BienTapTrangController::class, 'showBySlug'])
    ->where('slug', '[a-z0-9\-]+')
    ->name('page.show');