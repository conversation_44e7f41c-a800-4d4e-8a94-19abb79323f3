<?php

namespace App\Models\CongThongTin;

use MongoDB\Laravel\Eloquent\Model;

class ThietLapWebsite extends Model
{
    protected $connection = 'mongodb';
    protected $collection = 'thiet_lap_website';

    protected $fillable = [
        'Ma',
        'TenDonVi',
        '<PERSON>a<PERSON><PERSON>',
        'SoDienThoai',
        'Fax',
        'Email',
        'TenPhanMem',
        'TenPhienBan',
        'Website',
        'Facebook',
        'Youtube',
        'Banner',
        'NgayTao',
        'NgayCapNhat',
        'UserID_NguoiTao',
        'UserID_CapNhat',
        'NguoiTao',
        'DonViID',
        'AnhDaiDienBaiViet',
        'BannerLoginMoi',
        'BannerLoginNho',
        'FaviconUrl',
        'LogoUrl',
        'NoiDungChanTrang',
        'TieuDeWebsite',
        'ck_YeuCauKiemDuyet',
        'ck_QuanTriNgayTrenWebsite',
        'ck_KhongHoiKhiRoiTrangChinh',
        'ck_KhoaKhongChoCopyNoiDung',
        'AdminTimeOut',
        'ck_XacThucTaiKhoan',
        'ck_TamNgungTaiKhoan',
        'ck_TuDongDuyetBai',   
        'ck_KiemDuyetBinhLuan', 
        'ck_CoDinhKhungWebsite',
        'ck_KhoaBaiDang',  
        'ck_CoNutLenDauTrang',
    ];

    protected $casts = [
        'NgayTao' => 'datetime',
        'NgayCapNhat' => 'datetime',
    ];

    public function scopeCurrent($query)
    {
        return $query->first();
    }
    public function scopeOfDonVi($query, $donViId)
    {
        return $query->where('DonViID', $donViId);
    }

    public function scopeMoiNhat($query)
    {
        return $query->orderByDesc('NgayCapNhat');
    }

    public function scopeCoFacebook($query)
    {
        return $query->whereNotNull('Facebook')->where('Facebook', '!=', '');
    }

    public function scopeCoYoutube($query)
    {
        return $query->whereNotNull('Youtube')->where('Youtube', '!=', '');
    }

    public function scopeHasWebsite($query)
    {
        return $query->whereNotNull('Website')->where('Website', '!=', '');
    }

    public function scopeSearchByTenDonVi($query, $keyword)
    {
        return $query->where('TenDonVi', 'like', '%' . $keyword . '%');
    }
    public function scopeCreatedBy($query, $userId)
    {
        return $query->where('UserID_NguoiTao', $userId);
    }

}

