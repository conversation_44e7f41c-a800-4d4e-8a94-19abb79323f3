<?php

namespace App\Models\CongThongTin;

use MongoDB\Laravel\Eloquent\Model;
use Carbon\Carbon;
use Illuminate\Support\Str;

class TinTuc extends Model
{
    protected $connection = 'mongodb';
    protected $collection = 'tin_tuc';

    protected $fillable = [
        'TieuDe',
        'LoaiTinTucID',
        'Hinh<PERSON><PERSON>',
        '<PERSON>Kho<PERSON>',
        'NoiDung',
        'LuotXem',
        'LaNoiBat',
        'DinhDanh',
        'UserID_NguoiTao',
        'NgayTao',
        'DangSD',
        'NguoiTao',
        'DonViID',
        'NoiDungTomTat',
        'HienThiHDSD',
        'DonViXem',
    ];

    protected $casts = [
        'LuotXem' => 'integer',
        'LaNoiBat' => 'boolean',
        'DangSD' => 'boolean',
        'HienThiHDSD' => 'boolean',
        'DonViXem' => 'boolean',
        'NgayTao' => 'datetime',
    ];

    // Relationship với loại tin tức
    public function loaiTinTuc()
    {
        return $this->belongsTo(LoaiTinTuc::class, 'LoaiTinTucID', '_id');
    }

    // Scope để lấy tin tức đang sử dụng
    public function scopeActive($query)
    {
        return $query->where('DangSD', true);
    }

    // Scope để lấy tin tức nổi bật
    public function scopeNoiBat($query)
    {
        return $query->where('LaNoiBat', true);
    }

    // Accessor để format ngày tạo
    public function getFormattedNgayTaoAttribute()
    {
        return $this->NgayTao ? Carbon::parse($this->NgayTao)->format('d/m/Y') : '';
    }

    // Accessor để tạo URL slug từ tiêu đề
    public function getSlugAttribute()
    {
        return $this->DinhDanh ?: Str::slug($this->TieuDe);
    }

    // Method để tăng lượt xem
    public function incrementViews()
    {
        $this->increment('LuotXem');
    }
}
