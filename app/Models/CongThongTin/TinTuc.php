<?php

namespace App\Models\CongThongTin;

use MongoDB\Laravel\Eloquent\Model;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;

class TinTuc extends Model
{
    protected $connection = 'mongodb';
    protected $collection = 'tin_tuc';

    protected $fillable = [
        'TieuDe',
        '<PERSON>aiTinTucID',
        'Hinh<PERSON><PERSON>',
        '<PERSON><PERSON>ho<PERSON>',
        'NoiDung',
        'LuotXem',
        'LaNoiBat',
        'DinhDanh',
        'UserID_NguoiTao',
        'NgayTao',
        'Dang<PERSON>',
        'NguoiTao',
        'DonViID',
        'NoiDungTomTat',
        'HienThiHDSD',
        'DonViXem',
        // Các trường kiểm duyệt
        'TrangThaiKiemDuyet', // 0: Chờ duyệt, 1: Đã duyệt, 2: Từ chối
        'UserID_NguoiKiemDuyet',
        'NguoiKiemDuyet',
        'NgayKiemDuyet',
        '<PERSON><PERSON><PERSON>hu<PERSON>iemDuyet',
        'YeuCauKiemDuyet', // true/false - có yêu cầu kiểm duyệt không
        'TuDongDuyet', // true/false - có tự động duyệt không
    ];

    protected $casts = [
        'LuotXem' => 'integer',
        'LaNoiBat' => 'boolean',
        'DangSD' => 'boolean',
        'HienThiHDSD' => 'boolean',
        'DonViXem' => 'boolean',
        'NgayTao' => 'datetime',
        'TrangThaiKiemDuyet' => 'integer',
        'YeuCauKiemDuyet' => 'boolean',
        'TuDongDuyet' => 'boolean',
        'NgayKiemDuyet' => 'datetime',
    ];

    // Relationship với loại tin tức
    public function loaiTinTuc()
    {
        return $this->belongsTo(LoaiTinTuc::class, 'LoaiTinTucID', '_id');
    }

    // Scope để lấy tin tức đang sử dụng
    public function scopeActive($query)
    {
        return $query->where('DangSD', true);
    }

    // Scope để lấy tin tức nổi bật
    public function scopeNoiBat($query)
    {
        return $query->where('LaNoiBat', true);
    }

    // Scope để lấy tin tức đã được duyệt
    public function scopeDaDuyet($query)
    {
        return $query->where('TrangThaiKiemDuyet', 1);
    }

    // Scope để lấy tin tức chờ duyệt
    public function scopeChoDuyet($query)
    {
        return $query->where('TrangThaiKiemDuyet', 0);
    }

    // Scope để lấy tin tức bị từ chối
    public function scopeTuChoi($query)
    {
        return $query->where('TrangThaiKiemDuyet', 2);
    }

    // Scope để lấy tin tức có thể hiển thị (đã duyệt hoặc không yêu cầu kiểm duyệt)
    public function scopeCoTheHienThi($query)
    {
        return $query->where(function ($q) {
            $q->where('TrangThaiKiemDuyet', 1) // Đã duyệt
                ->orWhere('YeuCauKiemDuyet', false) // Hoặc không yêu cầu kiểm duyệt
                ->orWhere('TuDongDuyet', true); // Hoặc tự động duyệt
        });
    }

    // Accessor để format ngày tạo
    public function getFormattedNgayTaoAttribute()
    {
        return $this->NgayTao ? Carbon::parse($this->NgayTao)->format('d/m/Y') : '';
    }

    // Accessor để format ngày kiểm duyệt
    public function getFormattedNgayKiemDuyetAttribute()
    {
        return $this->NgayKiemDuyet ? Carbon::parse($this->NgayKiemDuyet)->format('d/m/Y H:i') : '';
    }

    // Accessor để lấy tên trạng thái kiểm duyệt
    public function getTenTrangThaiKiemDuyetAttribute()
    {
        switch ($this->TrangThaiKiemDuyet) {
            case 0:
                return 'Chờ duyệt';
            case 1:
                return 'Đã duyệt';
            case 2:
                return 'Từ chối';
            default:
                return 'Không xác định';
        }
    }

    // Accessor để lấy màu trạng thái kiểm duyệt
    public function getMauTrangThaiKiemDuyetAttribute()
    {
        switch ($this->TrangThaiKiemDuyet) {
            case 0:
                return 'warning'; // Vàng
            case 1:
                return 'success'; // Xanh lá
            case 2:
                return 'danger'; // Đỏ
            default:
                return 'secondary'; // Xám
        }
    }

    // Accessor để tạo URL slug từ tiêu đề
    public function getSlugAttribute()
    {
        return $this->DinhDanh ?: Str::slug($this->TieuDe);
    }

    // Method để tăng lượt xem
    public function incrementViews()
    {
        $this->increment('LuotXem');
    }

    // Method để thiết lập trạng thái kiểm duyệt khi tạo tin tức mới
    public function setTrangThaiKiemDuyetKhiTao()
    {
        $thietLap = ThietLapWebsite::current();

        if (!$thietLap) {
            // Nếu không có thiết lập, mặc định không yêu cầu kiểm duyệt
            $this->YeuCauKiemDuyet = false;
            $this->TuDongDuyet = true;
            $this->TrangThaiKiemDuyet = 1; // Đã duyệt
            return;
        }

        // Kiểm tra thiết lập yêu cầu kiểm duyệt
        if ($thietLap->ck_YeuCauKiemDuyet) {
            $this->YeuCauKiemDuyet = true;

            // Kiểm tra thiết lập tự động duyệt bài
            if ($thietLap->ck_TuDongDuyetBai) {
                $this->TuDongDuyet = true;
                $this->TrangThaiKiemDuyet = 1; // Đã duyệt
                $this->NgayKiemDuyet = now();
                $this->NguoiKiemDuyet = 'Hệ thống (Tự động)';
            } else {
                $this->TuDongDuyet = false;
                $this->TrangThaiKiemDuyet = 0; // Chờ duyệt
            }
        } else {
            // Không yêu cầu kiểm duyệt
            $this->YeuCauKiemDuyet = false;
            $this->TuDongDuyet = true;
            $this->TrangThaiKiemDuyet = 1; // Đã duyệt
        }
    }

    // Method để duyệt tin tức
    public function duyetTinTuc($nguoiDuyet, $userIdNguoiDuyet = null, $ghiChu = null)
    {
        $this->TrangThaiKiemDuyet = 1;
        $this->UserID_NguoiKiemDuyet = $userIdNguoiDuyet ?: Auth::id();
        $this->NguoiKiemDuyet = $nguoiDuyet;
        $this->NgayKiemDuyet = now();
        $this->GhiChuKiemDuyet = $ghiChu;
        $this->save();
    }

    // Method để từ chối tin tức
    public function tuChoiTinTuc($nguoiDuyet, $userIdNguoiDuyet = null, $ghiChu = null)
    {
        $this->TrangThaiKiemDuyet = 2;
        $this->UserID_NguoiKiemDuyet = $userIdNguoiDuyet ?: Auth::id();
        $this->NguoiKiemDuyet = $nguoiDuyet;
        $this->NgayKiemDuyet = now();
        $this->GhiChuKiemDuyet = $ghiChu;
        $this->save();
    }

    // Method để kiểm tra có thể hiển thị không
    public function coTheHienThi()
    {
        // Nếu không yêu cầu kiểm duyệt hoặc tự động duyệt
        if (!$this->YeuCauKiemDuyet || $this->TuDongDuyet) {
            return true;
        }

        // Nếu yêu cầu kiểm duyệt thì phải đã được duyệt
        return $this->TrangThaiKiemDuyet === 1;
    }

    // Boot method để tự động thiết lập trạng thái kiểm duyệt khi tạo mới
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($tinTuc) {
            $tinTuc->setTrangThaiKiemDuyetKhiTao();
        });
    }
}
