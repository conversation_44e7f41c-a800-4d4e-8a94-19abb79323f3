<?php

namespace App\Models\CongThongTin;

use MongoDB\Laravel\Eloquent\Model;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class BinhLuan extends Model
{
    protected $connection = 'mongodb';
    protected $collection = 'binh_luans';

    protected $fillable = [
        'TinTucID',
        'TieuDe',
        'HoVaTen',
        'Em<PERSON>',
        'BinhLuan',
        'TrangThaiKiemDuyet', // 0: Chờ duyệt, 1: Đã duyệt, 2: Từ chối
        'NguoiKiemDuyet',
        'UserID_NguoiKiemDuyet',
        'NgayKiemDuyet',
        'G<PERSON><PERSON>huKiemDuyet',
        'UserID_NguoiTao',
        'NgayTao',
        'DangSD',
    ];

    protected $casts = [
        'TrangThaiKiemDuyet' => 'integer',
        'DangSD' => 'boolean',
        'NgayKiemDuyet' => 'datetime',
        'NgayTao' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $attributes = [
        'TrangThaiKiemDuyet' => 0, // Mặc định chờ duyệt
        'DangSD' => true,
    ];

    // Relationship với tin tức
    public function tinTuc()
    {
        return $this->belongsTo(TinTuc::class, 'TinTucID', '_id');
    }

    // Scope để lấy bình luận đang sử dụng
    public function scopeActive($query)
    {
        return $query->where('DangSD', true);
    }

    // Scope để lấy bình luận đã được duyệt
    public function scopeDaDuyet($query)
    {
        return $query->where('TrangThaiKiemDuyet', 1);
    }

    // Scope để lấy bình luận chờ duyệt
    public function scopeChoDuyet($query)
    {
        return $query->where('TrangThaiKiemDuyet', 0);
    }

    // Scope để lấy bình luận bị từ chối
    public function scopeTuChoi($query)
    {
        return $query->where('TrangThaiKiemDuyet', 2);
    }

    // Scope để lấy bình luận có thể hiển thị (đã duyệt hoặc không yêu cầu kiểm duyệt)
    public function scopeCoTheHienThi($query)
    {
        $thietLap = ThietLapWebsite::first();
        
        if (!$thietLap || !$thietLap->ck_KiemDuyetBinhLuan) {
            // Không yêu cầu kiểm duyệt bình luận
            return $query->where('DangSD', true);
        }
        
        // Yêu cầu kiểm duyệt - chỉ hiển thị bình luận đã duyệt
        return $query->where('TrangThaiKiemDuyet', 1)->where('DangSD', true);
    }

    // Scope để lấy bình luận theo tin tức
    public function scopeByTinTuc($query, $tinTucId)
    {
        return $query->where('TinTucID', $tinTucId);
    }

    // Accessor để format ngày tạo
    public function getFormattedNgayTaoAttribute()
    {
        return $this->NgayTao ? Carbon::parse($this->NgayTao)->format('d/m/Y H:i') : '';
    }

    // Accessor để format ngày kiểm duyệt
    public function getFormattedNgayKiemDuyetAttribute()
    {
        return $this->NgayKiemDuyet ? Carbon::parse($this->NgayKiemDuyet)->format('d/m/Y H:i') : '';
    }

    // Accessor để lấy tên trạng thái kiểm duyệt
    public function getTenTrangThaiKiemDuyetAttribute()
    {
        switch ($this->TrangThaiKiemDuyet) {
            case 0:
                return 'Chờ duyệt';
            case 1:
                return 'Đã duyệt';
            case 2:
                return 'Từ chối';
            default:
                return 'Không xác định';
        }
    }

    // Accessor để lấy màu trạng thái kiểm duyệt
    public function getMauTrangThaiKiemDuyetAttribute()
    {
        switch ($this->TrangThaiKiemDuyet) {
            case 0:
                return 'warning'; // Vàng
            case 1:
                return 'success'; // Xanh lá
            case 2:
                return 'danger'; // Đỏ
            default:
                return 'secondary'; // Xám
        }
    }

    // Method để duyệt bình luận
    public function duyet($nguoiKiemDuyet = null, $ghiChu = null)
    {
        $this->TrangThaiKiemDuyet = 1;
        $this->NguoiKiemDuyet = $nguoiKiemDuyet ?: (Auth::user()->name ?? 'Hệ thống');
        $this->UserID_NguoiKiemDuyet = Auth::id();
        $this->NgayKiemDuyet = now();
        $this->GhiChuKiemDuyet = $ghiChu;
        $this->save();
    }

    // Method để từ chối bình luận
    public function tuChoi($nguoiKiemDuyet = null, $ghiChu = null)
    {
        $this->TrangThaiKiemDuyet = 2;
        $this->NguoiKiemDuyet = $nguoiKiemDuyet ?: (Auth::user()->name ?? 'Hệ thống');
        $this->UserID_NguoiKiemDuyet = Auth::id();
        $this->NgayKiemDuyet = now();
        $this->GhiChuKiemDuyet = $ghiChu;
        $this->save();
    }

    // Method để kiểm tra có thể hiển thị không
    public function coTheHienThi()
    {
        $thietLap = ThietLapWebsite::first();
        
        // Nếu không yêu cầu kiểm duyệt bình luận
        if (!$thietLap || !$thietLap->ck_KiemDuyetBinhLuan) {
            return $this->DangSD;
        }

        // Nếu yêu cầu kiểm duyệt thì phải đã được duyệt
        return $this->TrangThaiKiemDuyet === 1 && $this->DangSD;
    }

    // Method để thiết lập trạng thái kiểm duyệt khi tạo mới
    public function setTrangThaiKiemDuyetKhiTao()
    {
        $thietLap = ThietLapWebsite::first();
        
        if (!$thietLap || !$thietLap->ck_KiemDuyetBinhLuan) {
            // Không yêu cầu kiểm duyệt → Tự động duyệt
            $this->TrangThaiKiemDuyet = 1;
            $this->NguoiKiemDuyet = 'Hệ thống';
            $this->NgayKiemDuyet = now();
        } else {
            // Yêu cầu kiểm duyệt → Chờ duyệt
            $this->TrangThaiKiemDuyet = 0;
        }
    }

    // Boot method để tự động thiết lập trạng thái kiểm duyệt khi tạo mới
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($binhLuan) {
            // Thiết lập thông tin người tạo
            $binhLuan->NgayTao = now();
            if (Auth::check()) {
                $binhLuan->UserID_NguoiTao = Auth::id();
            }
            
            // Thiết lập trạng thái kiểm duyệt
            $binhLuan->setTrangThaiKiemDuyetKhiTao();
        });
    }
}
