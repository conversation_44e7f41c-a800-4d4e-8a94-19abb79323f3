<?php

namespace App\Models\QuanLy;

use App\Models\DanhMuc\XepLoai;
use App\Models\DienUuTien;
use MongoDB\Laravel\Eloquent\Model;

class HocSinhTN extends Model
{
    protected $connection = 'mongodb';
    protected $collection = 'hocsinhtn';

    protected $fillable = [
        'HocSinhID',
        'SoCMND',
        'LopID',
        'DonVi_Truong',
        'KetQua_HT',
        'KetQua_RL',
        'KetQuaTN',
        'ThuocDienUuTien',
        'GhiChu',
        'QuyetDinhId'
    ];

    public function xepLoaiHocTap()
{
    return $this->belongsTo(XepLoai::class, 'KetQua_HT', '_id');
}

public function xepLoaiRenLuyen()
{
    return $this->belongsTo(XepLoai::class, 'KetQua_RL', '_id');
}

public function xepLoaiTotNghiep()
{
    return $this->belongsTo(XepLoai::class, 'KetQuaTN', '_id');
}
public function hocSinh()
{
    return $this->belongsTo(DoiTuong::class, 'HocSinhID', '_id');
}
public function dienUuTien()
{
    return $this->belongsTo(DienUuTien::class, 'ThuocDienUuTien', '_id');
}
}