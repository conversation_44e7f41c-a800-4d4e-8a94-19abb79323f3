<?php

namespace App\Models\DanhMuc;

use MongoDB\Laravel\Eloquent\Model;

/**
 * @method static \Jenssegers\Mongodb\Eloquent\Builder|\App\Models\NhanVien all(array $columns = ['*'])
 */
class NhanVien extends Model

{
     // Sử dụng cấu hình MongoDB
     protected $connection = 'mongodb';
    protected $collection = 'nhanvien';
 
     // Các trường có thể gán giá trị hàng loạt
     protected $fillable = [
        'maNhanVien',
        'tenNhanVien',
        'soDienThoai',
        'diaChi',
        'phongBanID',
        'chucVuID',
        'gioiTinhID',
        'ngaySinh',
        'cmnd',
        'ngayCap',
        'noiCap',
        'trangThai',

        // auditing / metadata
        'donViID',
        'nguoiTao',
        'ngayTao',
        'nguoiCapNhat',
        'ngayCapNhat',
        'ngayThaoTac',
        'userID',
    ];
 
     // <PERSON>y<PERSON><PERSON> đổi sang kiểu DateTime
      protected $casts = [
        'ngaySinh'    => 'date',
        'ngayCap'     => 'date',
        'trangThai'   => 'boolean',

        'ngayTao'     => 'datetime',
        'ngayCapNhat' => 'datetime',
        'ngayThaoTac' => 'datetime',

        // leave your ID‐fields as strings (or cast to string)
        'phongBanID'  => 'string',
        'chucVuID'    => 'string',
        'gioiTinhID'  => 'string',
        'donViID'     => 'string',
        'userID'      => 'string',
    ];


     public function chucVu()
     {
         return $this->belongsTo(ChucVu::class, 'chucVuID', 'chucVuID');
     }
 
     public function phongBan()
     {
         return $this->belongsTo(PhongBan::class, 'phongBanID', 'phongBanID');
     }
 
     public function gioiTinh()
     {
         return $this->belongsTo(GioiTinh::class, 'gioiTinhID', 'gioiTinhID');
     }
 
     public function donVi()
     {
         return $this->belongsTo(DonVi::class, 'donViID', 'donViID');
     }
}