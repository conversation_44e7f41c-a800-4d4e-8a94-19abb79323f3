<?php

namespace App\Models;

use MongoDB\Laravel\Eloquent\Model;

class DienUuTien extends Model
{
    /**
     * Kết nối đến MongoDB
     */
    protected $connection = 'mongodb';

    /**
     * Tên collection trong MongoDB
     */
    protected $collection = 'dien_uutien';

    /**
     * C<PERSON>c trường có thể gán giá trị hàng loạt
     */
    protected $fillable = [
        'MaDienUuTien',
        'TenDienUuTien',
        'TrangThai',          // thêm trường này
    ];

    /**
     * Casts
     */
    protected $casts = [
        '_id'       => 'string',
        'TrangThai' => 'boolean',  // cast thành bool
    ];

    /**
     * Giá trị mặc định
     */
    protected $attributes = [
        'TrangThai' => true,        // default = true
    ];
}