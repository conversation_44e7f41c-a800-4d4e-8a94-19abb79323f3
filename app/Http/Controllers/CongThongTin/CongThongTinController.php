<?php

namespace App\Http\Controllers\CongThongTin;

use App\Http\Controllers\Controller;
use App\Models\CongThongTin\TinTuc;
use App\Models\CongThongTin\LoaiTinTuc;
use App\Models\CongThongTin\ThietLapWebsite;
use Illuminate\Http\Request;

class CongThongTinController extends Controller
{
    /**
     * Display the Cổng Thông Tin page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Lấy tin tức nổi bật để hiển thị (lấy nhiều hơn để tránh trùng lặp)
        $tinTucNoiBat = TinTuc::with('loaiTinTuc')
            ->active()
            ->coTheHienThi() // Chỉ lấy tin tức đã được duyệt
            ->noiBat()
            ->orderBy('NgayTao', 'desc')
            ->limit(12)
            ->get();

        // Nếu không có tin nổi bật, lấy tin tức mới nhất
        if ($tinTucNoiBat->isEmpty()) {
            $tinTucNoiBat = TinTuc::with('loaiTinTuc')
                ->active()
                ->coTheHienThi() // Chỉ lấy tin tức đã được duyệt
                ->orderBy('NgayTao', 'desc')
                ->limit(12)
                ->get();
        }

        // Lấy thông tin thiết lập website
        $thietLapWebsite = ThietLapWebsite::first();

        return view('congthongtin.index', compact('tinTucNoiBat', 'thietLapWebsite'));
    }

    /**
     * Display news listing page.
     *
     * @return \Illuminate\View\View
     */
    public function news(Request $request)
    {
        $query = TinTuc::with('loaiTinTuc')->active()->coTheHienThi();

        // Lọc theo loại tin tức nếu có
        if ($request->has('loai') && $request->loai) {
            $loaiTinTuc = LoaiTinTuc::where('DinhDanh', $request->loai)->first();
            if ($loaiTinTuc) {
                $query->where('LoaiTinTucID', $loaiTinTuc->_id);
            }
        }

        // Tìm kiếm theo từ khóa
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('TieuDe', 'like', "%{$search}%")
                    ->orWhere('NoiDungTomTat', 'like', "%{$search}%")
                    ->orWhere('TuKhoa', 'like', "%{$search}%");
            });
        }

        $tinTucs = $query->orderBy('NgayTao', 'desc')->paginate(12);
        $loaiTinTucs = LoaiTinTuc::where('DangSD', true)->get();

        // Lấy thông tin thiết lập website
        $thietLapWebsite = ThietLapWebsite::first();

        return view('congthongtin.news.index', compact('tinTucs', 'loaiTinTucs', 'thietLapWebsite'));
    }

    /**
     * Display news detail page.
     *
     * @param string $slug
     * @return \Illuminate\View\View
     */
    public function show($slug)
    {
        $tinTuc = TinTuc::with('loaiTinTuc')
            ->where('DinhDanh', $slug)
            ->active()
            ->coTheHienThi() // Chỉ hiển thị tin tức đã được duyệt
            ->firstOrFail();

        // Tăng lượt xem
        $tinTuc->incrementViews();

        // Lấy tin tức liên quan (cùng loại, khác bài hiện tại)
        $tinTucLienQuan = TinTuc::with('loaiTinTuc')
            ->where('LoaiTinTucID', $tinTuc->LoaiTinTucID)
            ->where('_id', '!=', $tinTuc->_id)
            ->active()
            ->coTheHienThi() // Chỉ lấy tin tức đã được duyệt
            ->orderBy('NgayTao', 'desc')
            ->limit(12)
            ->get();

        // Lấy thông tin thiết lập website
        $thietLapWebsite = ThietLapWebsite::first();

        return view('congthongtin.news.detail', compact('tinTuc', 'tinTucLienQuan', 'thietLapWebsite'));
    }
}
