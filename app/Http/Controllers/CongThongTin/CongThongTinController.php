<?php

namespace App\Http\Controllers\CongThongTin;

use App\Http\Controllers\Controller;
use App\Models\CongThongTin\TinTuc;
use App\Models\CongThongTin\LoaiTinTuc;
use App\Models\CongThongTin\ThietLapWebsite;
use App\Models\CongThongTin\BinhLuan;
use Illuminate\Http\Request;

class CongThongTinController extends Controller
{
    /**
     * Display the Cổng Thông Tin page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Lấy tin tức nổi bật để hiển thị (lấy nhiều hơn để tránh trùng lặp)
        $tinTucNoiBat = TinTuc::with('loaiTinTuc')
            ->active()
            ->coTheHienThi() // Chỉ lấy tin tức đã được duyệt
            ->noiBat()
            ->orderBy('NgayTao', 'desc')
            ->limit(12)
            ->get();

        // Nếu không có tin nổi bật, lấy tin tức mới nhất
        if ($tinTucNoiBat->isEmpty()) {
            $tinTucNoiBat = TinTuc::with('loaiTinTuc')
                ->active()
                ->coTheHienThi() // Chỉ lấy tin tức đã được duyệt
                ->orderBy('NgayTao', 'desc')
                ->limit(12)
                ->get();
        }

        // Lấy thông tin thiết lập website
        $thietLapWebsite = ThietLapWebsite::first();

        return view('congthongtin.index', compact('tinTucNoiBat', 'thietLapWebsite'));
    }

    /**
     * Display news listing page.
     *
     * @return \Illuminate\View\View
     */
    public function news(Request $request)
    {
        $query = TinTuc::with('loaiTinTuc')->active()->coTheHienThi();

        // Lọc theo loại tin tức nếu có
        if ($request->has('loai') && $request->loai) {
            $loaiTinTuc = LoaiTinTuc::where('DinhDanh', $request->loai)->first();
            if ($loaiTinTuc) {
                $query->where('LoaiTinTucID', $loaiTinTuc->_id);
            }
        }

        // Tìm kiếm theo từ khóa
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('TieuDe', 'like', "%{$search}%")
                    ->orWhere('NoiDungTomTat', 'like', "%{$search}%")
                    ->orWhere('TuKhoa', 'like', "%{$search}%");
            });
        }

        $tinTucs = $query->orderBy('NgayTao', 'desc')->paginate(12);
        $loaiTinTucs = LoaiTinTuc::where('DangSD', true)->get();

        // Lấy thông tin thiết lập website
        $thietLapWebsite = ThietLapWebsite::first();

        return view('congthongtin.news.index', compact('tinTucs', 'loaiTinTucs', 'thietLapWebsite'));
    }

    /**
     * Display news detail page.
     *
     * @param string $slug
     * @return \Illuminate\View\View
     */
    public function show($slug)
    {
        $tinTuc = TinTuc::with('loaiTinTuc')
            ->where('DinhDanh', $slug)
            ->active()
            ->coTheHienThi() // Chỉ hiển thị tin tức đã được duyệt
            ->firstOrFail();

        // Tăng lượt xem
        $tinTuc->incrementViews();

        // Lấy tin tức liên quan (cùng loại, khác bài hiện tại)
        $tinTucLienQuan = TinTuc::with('loaiTinTuc')
            ->where('LoaiTinTucID', $tinTuc->LoaiTinTucID)
            ->where('_id', '!=', $tinTuc->_id)
            ->active()
            ->coTheHienThi() // Chỉ lấy tin tức đã được duyệt
            ->orderBy('NgayTao', 'desc')
            ->limit(12)
            ->get();

        // Lấy thông tin thiết lập website
        $thietLapWebsite = ThietLapWebsite::first();

        return view('congthongtin.news.detail', compact('tinTuc', 'tinTucLienQuan', 'thietLapWebsite'));
    }

    /**
     * Store a new comment for a news article.
     *
     * @param Request $request
     * @param string $slug
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeBinhLuan(Request $request, $slug)
    {
        try {
            // Validate input
            $request->validate([
                'HoVaTen' => 'required|string|max:255',
                'Email' => 'required|email|max:255',
                'BinhLuan' => 'required|string|max:1000',
            ], [
                'HoVaTen.required' => 'Vui lòng nhập họ và tên',
                'HoVaTen.max' => 'Họ và tên không được vượt quá 255 ký tự',
                'Email.required' => 'Vui lòng nhập email',
                'Email.email' => 'Email không đúng định dạng',
                'Email.max' => 'Email không được vượt quá 255 ký tự',
                'BinhLuan.required' => 'Vui lòng nhập nội dung bình luận',
                'BinhLuan.max' => 'Bình luận không được vượt quá 1000 ký tự',
            ]);

            // Find the news article
            $tinTuc = TinTuc::where('DinhDanh', $slug)
                ->active()
                ->coTheHienThi()
                ->firstOrFail();

            // Create new comment
            $binhLuan = BinhLuan::create([
                'TinTucID' => $tinTuc->_id,
                'TieuDe' => $tinTuc->TieuDe,
                'HoVaTen' => $request->HoVaTen,
                'Email' => $request->Email,
                'BinhLuan' => $request->BinhLuan,
                'TrangThaiKiemDuyet' => 0, // Chờ duyệt
                'NgayTao' => now(),
                'DangSD' => true,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Bình luận của bạn đã được gửi và đang chờ kiểm duyệt.',
                'data' => [
                    'id' => $binhLuan->_id,
                    'HoVaTen' => $binhLuan->HoVaTen,
                    'BinhLuan' => $binhLuan->BinhLuan,
                    'NgayTao' => $binhLuan->formatted_ngay_tao,
                    'TrangThaiKiemDuyet' => $binhLuan->TrangThaiKiemDuyet,
                ]
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Dữ liệu không hợp lệ',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi gửi bình luận. Vui lòng thử lại.'
            ], 500);
        }
    }

    /**
     * Get comments for a news article.
     *
     * @param Request $request
     * @param string $slug
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBinhLuan(Request $request, $slug)
    {
        try {
            // Find the news article
            $tinTuc = TinTuc::where('DinhDanh', $slug)
                ->active()
                ->coTheHienThi()
                ->firstOrFail();

            $page = $request->get('page', 1);
            $perPage = 10;

            // Get approved comments with pagination
            $binhLuans = BinhLuan::where('TinTucID', $tinTuc->_id)
                ->coTheHienThi() // Chỉ lấy bình luận có thể hiển thị
                ->orderBy('NgayTao', 'desc')
                ->skip(($page - 1) * $perPage)
                ->take($perPage)
                ->get();

            // Get total count for pagination
            $totalCount = BinhLuan::where('TinTucID', $tinTuc->_id)
                ->coTheHienThi()
                ->count();

            $hasMore = ($page * $perPage) < $totalCount;

            // Format comments data
            $formattedComments = $binhLuans->map(function ($binhLuan) {
                return [
                    'id' => $binhLuan->_id,
                    'HoVaTen' => $binhLuan->HoVaTen,
                    'BinhLuan' => $binhLuan->BinhLuan,
                    'NgayTao' => $binhLuan->formatted_ngay_tao,
                    'TrangThaiKiemDuyet' => $binhLuan->TrangThaiKiemDuyet,
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $formattedComments,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $totalCount,
                    'has_more' => $hasMore,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tải bình luận.'
            ], 500);
        }
    }
}
