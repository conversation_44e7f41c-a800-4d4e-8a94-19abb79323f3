<?php

namespace App\Http\Controllers\CongThongTin;

use App\Http\Controllers\Controller;
use App\Models\CongThongTin\TinTuc;
use App\Models\CongThongTin\ThietLapWebsite;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class KiemDuyetTinTucController extends Controller
{
    /**
     * Hiển thị danh sách tin tức cần kiểm duyệt
     */
    public function index()
    {
        $tinTucChoDuyet = TinTuc::choDuyet()
            ->orderBy('NgayTao', 'desc')
            ->paginate(20);

        $tinTucDaDuyet = TinTuc::daDuyet()
            ->orderBy('NgayKiemDuyet', 'desc')
            ->paginate(20);

        $tinTucTuChoi = TinTuc::tuChoi()
            ->orderBy('NgayKiemDuyet', 'desc')
            ->paginate(20);

        return view('admin.kiem-duyet.index', compact(
            'tinTucChoDuyet',
            'tinTucDaDuyet', 
            'tinTucTuChoi'
        ));
    }

    /**
     * Duy<PERSON>t tin tức
     */
    public function duyet(Request $request, $id)
    {
        $request->validate([
            'ghi_chu' => 'nullable|string|max:500'
        ]);

        $tinTuc = TinTuc::findOrFail($id);
        
        $tinTuc->duyetTinTuc(
            Auth::user()->name ?? 'Admin',
            Auth::id(),
            $request->ghi_chu
        );

        return response()->json([
            'success' => true,
            'message' => 'Đã duyệt tin tức thành công!'
        ]);
    }

    /**
     * Từ chối tin tức
     */
    public function tuChoi(Request $request, $id)
    {
        $request->validate([
            'ghi_chu' => 'required|string|max:500'
        ]);

        $tinTuc = TinTuc::findOrFail($id);
        
        $tinTuc->tuChoiTinTuc(
            Auth::user()->name ?? 'Admin',
            Auth::id(),
            $request->ghi_chu
        );

        return response()->json([
            'success' => true,
            'message' => 'Đã từ chối tin tức!'
        ]);
    }

    /**
     * Xem chi tiết tin tức để kiểm duyệt
     */
    public function show($id)
    {
        $tinTuc = TinTuc::with('loaiTinTuc')->findOrFail($id);
        
        return view('admin.kiem-duyet.show', compact('tinTuc'));
    }

    /**
     * Cập nhật thiết lập kiểm duyệt
     */
    public function capNhatThietLap(Request $request)
    {
        $request->validate([
            'ck_YeuCauKiemDuyet' => 'boolean',
            'ck_TuDongDuyetBai' => 'boolean'
        ]);

        $thietLap = ThietLapWebsite::current();
        
        if (!$thietLap) {
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy thiết lập website!'
            ]);
        }

        $thietLap->update([
            'ck_YeuCauKiemDuyet' => $request->boolean('ck_YeuCauKiemDuyet'),
            'ck_TuDongDuyetBai' => $request->boolean('ck_TuDongDuyetBai')
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Đã cập nhật thiết lập kiểm duyệt!'
        ]);
    }

    /**
     * Lấy thống kê kiểm duyệt
     */
    public function thongKe()
    {
        $thongKe = [
            'cho_duyet' => TinTuc::choDuyet()->count(),
            'da_duyet' => TinTuc::daDuyet()->count(),
            'tu_choi' => TinTuc::tuChoi()->count(),
            'tong_so' => TinTuc::count()
        ];

        return response()->json($thongKe);
    }
}
