<?php

namespace App\Http\Controllers\QuanLy;

use App\Http\Controllers\Controller;
use App\Models\DanhMuc\DonVi;
use App\Models\DanhMuc\XepLoai;
use App\Models\DienUuTien;
use App\Models\QuanLy\DoiTuong;
use App\Models\QuanLy\HocSinhTN;
use App\Models\QuanLy\QuyetDinh;
use App\Services\CurrentUserService;
use App\Services\DungChungDb;
use App\Services\DungChungNoDb;
use App\Services\ThongBao;
use Carbon\Carbon;
use Date;
use Exception;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Cell\DataValidation;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Storage;
use Str;
use Validator;
use MongoDB\BSON\ObjectId;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Shared\Date as ExcelDate;


class HoSoTotNghiepTHPTController extends Controller
{
    protected DungChungDb $dungChungDb;
    protected DungChungNoDb $logService;
    protected CurrentUserService $currentUser;

    public function __construct(DungChungDb $dungChungDb, DungChungNoDb $logService, CurrentUserService $currentUser)
    {
        $this->dungChungDb = $dungChungDb;
        $this->currentUser = $currentUser;  
        $this->logService = $logService;
    }
    //
    function index()
    {
        $steps = [];
        
        $stepContents = [];
        
        
        $stepOptions = [
            ['style' => 'color:green;'],         // Step 1
            ['icon' => "<i class='ti ti-user'></i>"], // Step 2
            []                                   // Step 3
        ];
        
        return view('quanly.hosototnghiepthpt.index', compact('steps', 'stepContents', 'stepOptions'));
    }


     /**
     * Lấy tất cả quyết định (JSON), hỗ trợ filter tùy chọn:
     * - Từ ngày (NgayKy)
     * - Đến ngày (NgayKy)
     * - Cấp học (CapHocID)
     * - Trạng thái (TrangThai)
     */
    public function getAll(Request $request)
    {
        try {
            $tuNgay = $request->input('tuNgay');
            $denNgay = $request->input('denNgay');
            $capHocID = $request->input('capHocID');
            $trangThai = $request->input('trangThai');
    
            $tuNgay = empty($tuNgay) ? null : $tuNgay;
            $denNgay = empty($denNgay) ? null : $denNgay;
            $capHocID = empty($capHocID) ? null : $capHocID;
            $trangThai = ($trangThai === '' || is_null($trangThai)) ? null : $trangThai;
    
            $query = QuyetDinh::with([
                'hoiDong', 'kyThi', 'chucVu', 'hinhThucDaoTao',
                'donVi', 'nhanVienBanHanh.chucVu', 'capHoc', 'TrangThaiLabel'
            ]);
    
            if (!is_null($capHocID) && $capHocID !== '-1') {
                $query->where('CapHocID', $capHocID);
            }
    
           
    
            // Get all filtered records except date filter
            $items = $query->get();

            
            if (!is_null($trangThai)) {
                $items = $items->filter(function ($item) use ($trangThai) {
                    return isset($item->TrangThai) && (string)$item->TrangThai === (string)$trangThai;
                });
            }
            // Filter by date in PHP after fetching
            if (!is_null($tuNgay)) {
                try {
                    $from = Carbon::createFromFormat('d/m/Y', $tuNgay)->startOfDay();
                    $items = $items->filter(function ($item) use ($from) {
                        if (empty($item->NgayKy)) return false;
                        try {
                            $ngayKy = Carbon::createFromFormat('d/m/Y', $item->NgayKy);
                            return $ngayKy->gte($from);
                        } catch (\Exception $e) {
                            return false;
                        }
                    });
                } catch (\Exception $e) {
                    return response()->json([
                        'Err' => true,
                        'Msg' => 'Có lỗi xảy ra: ' . $e->getMessage(),
                    ]);
                }
            }
    
            if (!is_null($denNgay)) {
                try {
                    $to = Carbon::createFromFormat('d/m/Y', $denNgay)->endOfDay();
                    $items = $items->filter(function ($item) use ($to) {
                        if (empty($item->NgayKy)) return false;
                        try {
                            $ngayKy = Carbon::createFromFormat('d/m/Y', $item->NgayKy);
                            return $ngayKy->lte($to);
                        } catch (\Exception $e) {
                            return false;
                        }
                    });
                } catch (\Exception $e) {
                    return response()->json([
                        'Err' => true,
                        'Msg' => 'Có lỗi xảy ra: ' . $e->getMessage(),
                    ]);
                }
            }
    
            // Re-index collection and sort by NgayKy desc
            $items = $items->values()->sortByDesc(function ($item) {
                try {
                    return Carbon::createFromFormat('d/m/Y', $item->NgayKy);
                } catch (\Exception $e) {
                    return null;
                }
            })->values();
    
            return response()->json([
                'Err' => false,
                'Result' => $items,
            ]);
        } catch (Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra: ' . $e->getMessage(),
            ]);
        }
    }
    
    public function getHocSinhTNWithDoiTuongByQuyetDinhId($quyetDinhId)
{
    try {
        // Convert to ObjectId if needed
        $quyetDinhObjectId = new ObjectId($quyetDinhId);

        // Query HocSinhTN directly by quyetDinhId
        $hocSinhList = HocSinhTN::with(['dienUuTien', 'xepLoaiHocTap', 'xepLoaiRenLuyen', 'xepLoaiTotNghiep'])
            ->where('QuyetDinhId', $quyetDinhObjectId)
            ->get();

        if ($hocSinhList->isEmpty()) {
            return response()->json([
                'Err' => false,
                'Result' => [],
            ]);
        }

        // Extract HocSinhID list to get corresponding DoiTuong documents
        $doiTuongIds = $hocSinhList->pluck('HocSinhID')->filter()->unique();

        // Get DoiTuong list keyed by string _id
        $doiTuongList = DoiTuong::whereIn('_id', $doiTuongIds)
            ->get()
            ->keyBy(fn($item) => (string) $item->_id);

        // Attach DoiTuong info to each HocSinhTN
        $hocSinhList->transform(function ($hocSinh) use ($doiTuongList) {
            $doiTuongIdStr = (string) $hocSinh->HocSinhID;
            $hocSinh->DoiTuong = $doiTuongList->get($doiTuongIdStr);
            return $hocSinh;
        });

        return response()->json([
            'Err' => false,
            'Result' => $hocSinhList,
        ]);
    } catch (Exception $e) {
        return response()->json([
            'Err' => true,
            'Msg' => 'Có lỗi xảy ra khi lấy danh sách học sinh tốt nghiệp.',
        ], 500);
    }
}

    public function loadDuLieuSua(Request $request)
{
    $id = $request->input('id');
    if (!$id) {
        return response()->json([
            'Err' => true,
            'Msg' => 'ID không được để trống',
        ], 400);
    }

    try {
        $quyetDinh = QuyetDinh::with(['hoiDong', 'kyThi', 'chucVu', 'hinhThucDaoTao', 'donVi'])
            ->where('_id', $id)
            ->first();

        if (!$quyetDinh) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Không tìm thấy quyết định với ID đã cho',
            ], 404);
        }

        return response()->json([
            'Err' => false,
            'Result' => $quyetDinh,
        ]);
    } catch (Exception $e) {
        return response()->json([
            'Err' => true,
            'Msg' => 'Lỗi khi lấy dữ liệu: ' . $e->getMessage(),
        ], 500);
    }
}



    public function luuThongTin(Request $request)
    {
        $data = $request->all();
        $quyetDinh = null;
        $msg = '';
        $thaoTac = '';
        try {
            // Parse date from d/m/Y to Y-m-d
            $donViid  = $this->currentUser->donViId();
            $username = $this->currentUser->username();
    
            $tempPrefix = "uploads/{$donViid}/{$username}/files/";
            $targetDir = "uploads/{$donViid}/{$username}/files/QuyetDinhTotNghiep";
    
            $fileDisk = Storage::disk('public');
    
            $fileUrls = $data['DinhKemQD'] ?? []; // or $data['FileDinhKem']?
    
            $newFileUrls = [];
    
            foreach ($fileUrls as $fileUrl) {
                // Normalize URL/path (remove leading slashes)
                $relativePath = ltrim(str_replace('/storage/', '', $fileUrl), '/');
                
                if (Str::startsWith($relativePath, $tempPrefix)) {
                    // This file is still in temp folder, move it
    
                    // Build new filename and path to avoid conflicts
                    $filename = basename($relativePath);
                    $newPath = $targetDir . '/' . $filename;
    
                    // Make sure filename is unique
                    $base = pathinfo($filename, PATHINFO_FILENAME);
                    $ext = pathinfo($filename, PATHINFO_EXTENSION);
                    $finalPath = $newPath;
                    while ($fileDisk->exists($finalPath)) {
                        $salt = Str::random(6);
                        $finalPath = $targetDir . '/' . $base . '_' . $salt . '.' . $ext;
                    }
    
                    // Move file: copy then delete original
                    if ($fileDisk->exists($relativePath)) {
                        $fileDisk->copy($relativePath, $finalPath);
                        $fileDisk->delete($relativePath);
                    }
    
                    // Save new URL
                    $newFileUrls[] = '/storage/' . $finalPath;
                } else {
                    // Already in permanent folder, keep as is
                    $newFileUrls[] = $fileUrl;
                }
            }
    
            // Replace file list with new URLs after moving
            $data['DinhKemQD'] = $newFileUrls;

            $quyetDinhData = [
                'SoQuyetDinh'    => $data['SoQuyetDinh'] ?? null,
                'NguoiKy'        => $data['NguoiKy'] ?? null,
                'ChucVuID_NK'    => $data['ChucVu'] ?? null,
                'NgayKy'         => $data['NgayKy']?? null,
                'CoQuanBanHanh'  => $data['CoQuanBanHanh'] ?? null,
                'KyThiID'        => $data['KyThi'] ?? null,
                'HinhThucID'     => $data['HinhThucDaoTao'] ?? null,
                'HoiDongID'      => $data['HoiDong'] ?? null,
                'DonViID'        => $data['DonVi']['id'] ?? null,
                'FileDinhKem'    => $data['DinhKemQD'] ?? null,
                'TrichYeu'       => $data['TrichYeu'] ?? null,
            ];

            if ($request->isMethod('post')) {
                // Create new
                $quyetDinh = QuyetDinh::create($quyetDinhData);
                $msg = 'Tạo mới quyết định thành công';
            $thaoTac = 'Tạo mới quyết định';

            } elseif ($request->isMethod('put') || $request->isMethod('patch')) {
                // Update existing by SoQuyetDinh
                $quyetDinh = QuyetDinh::where('_id', $data['QuyetDinhID'])->first();
            $thaoTac = 'Cập nhật quyết định';

                if (!$quyetDinh) {
                    return response()->json([
                        'Err' => true,
                        'canhbao' => true,
                        'Msg' => ThongBao::coLoiXayRa('Không tìm thấy quyết định để cập nhật'),
                    ], 404);
                }

                $quyetDinh->update($quyetDinhData);
                $msg = 'Cập nhật quyết định thành công';
                $this->logService->ghiLogs(
                    $quyetDinh->toArray(),
                    'Cập nhật quyết định',
                    $this->currentUser->id(),
                    $this->currentUser->donViId()
                );
            } else {

                return response()->json([
                    'Err' => true,
                    'canhbao' => true,
                    'Msg' => ThongBao::coLoiXayRa('Phương thức HTTP không được hỗ trợ'),
                ], 405);
            }

            return response()->json([
                'Err' => false,
                'canhbao' => false,
                'Msg' => $msg,
                'Data' => $quyetDinh,
            ]);
        } catch (\Exception $ex) {
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => ThongBao::coLoiXayRa($ex->getMessage()),
            ], 500);
        } finally {
        // Ghi log nếu có đối tượng $quyetDinh
            if ($quyetDinh !== null) {
                try {
                    $this->logService->ghiLogs(
                        $quyetDinh->toArray(),
                        $thaoTac ?: 'Lưu quyết định',
                        $this->currentUser->id(),
                        $this->currentUser->donViId()
                    );
                } catch (\Exception $logEx) {
                    \Log::error('Ghi log thất bại: ' . $logEx->getMessage());
                }
            }
        }
    }

    
    public function luuthongtinHocSinhTN(Request $request)
{
    // Validate input
    $validator = Validator::make($request->all(), [
        'quyetDinhId'      => 'required|string',
        'hocSinhID'        => 'required|string',  // Mongo _id of DoiTuong / HocSinhTN
        'ketQuaRenLuyen'   => 'nullable|string',
        'ketQuaHocTap'     => 'nullable|string',
        'uuTien'           => 'nullable|string',
        'ketQuaTotNghiep'  => 'nullable|string',
        'ghiChu'           => 'nullable|string',
    ]);
    if ($validator->fails()) {
        return response()->json([
            'Err' => true,
            'Msg' => $validator->errors()->first(),
        ], 422);
    }

    try {
        $data = $validator->validated();

        $quyetDinhObjectId = new ObjectId($data['quyetDinhId']);
        $doiTuongObjectId = new ObjectId($data['hocSinhID']);

        // Check if DoiTuong exists
        $doiTuong = DoiTuong::find($doiTuongObjectId);
        if (!$doiTuong) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Không tìm thấy học sinh.',
                'Result' => [],
            ], 404);
        }

        // Determine if POST (create) or PUT (update)
        if ($request->isMethod('post')) {
            $hocSinhTN = new HocSinhTN();
            $hocSinhTN->_id = new ObjectId(); // new ID for HocSinhTN
            $hocSinhTN->HocSinhID = (string) $doiTuongObjectId; // link to DoiTuong

        } elseif ($request->isMethod('put') || $request->isMethod('patch')) {
            $hocSinhTN = HocSinhTN::where('HocSinhID', (string)$doiTuongObjectId)->first();
            if (!$hocSinhTN) {
                return response()->json([
                    'Err' => true,
                    'Msg' => 'Học sinh tốt nghiệp chưa tồn tại, không thể cập nhật.',
                ], 404);
            }
        } else {
            return response()->json([
                'Err' => true,
                'Msg' => 'Phương thức HTTP không được hỗ trợ.',
            ], 405);
        }

        // Update HocSinhTN fields
        $hocSinhTN->QuyetDinhId = $quyetDinhObjectId;  // save the ObjectId here ONLY
        $hocSinhTN->KetQua_RL       = $data['ketQuaRenLuyen'] ?? null;
        $hocSinhTN->KetQua_HT       = $data['ketQuaHocTap'] ?? null;
        $hocSinhTN->ThuocDienUuTien = $data['uuTien'] ?? null;
        $hocSinhTN->KetQuaTN        = $data['ketQuaTotNghiep'] ?? null;
        $hocSinhTN->GhiChu          = $data['ghiChu'] ?? null;

        $hocSinhTN->save();

        // REMOVE this block so we do NOT update QuyetDinh's HocSinhTN array
        /*
        $quyetDinh = QuyetDinh::find($quyetDinhObjectId);

        if ($quyetDinh) {
            $hocSinhTNArray = is_array($quyetDinh->HocSinhTN) ? $quyetDinh->HocSinhTN : [];

            $hocSinhTNStrings = array_map(fn ($id) => (string) $id, $hocSinhTNArray);
            $hocSinhTNIdStr = (string) $hocSinhTN->_id;

            if (!in_array($hocSinhTNIdStr, $hocSinhTNStrings, true)) {
                $hocSinhTNArray[] = $hocSinhTN->_id;
                $quyetDinh->HocSinhTN = $hocSinhTNArray;
                $quyetDinh->save();
            }
        }
        */

        $msg = $request->isMethod('post') ? 'Tạo mới học sinh tốt nghiệp thành công.' : 'Cập nhật thông tin thành công.';

        return response()->json([
            'Err' => false,
            'Msg' => $msg,
            'Result' => $hocSinhTN,
        ]);
    } catch (Exception $e) {
        \Log::error('Error in luuthongtinHocSinhTN: ' . $e->getMessage());
        return response()->json([
            'Err' => true,
            'Msg' => 'Có lỗi xảy ra khi lưu thông tin.',
        ], 500);
    } finally{
        $this->logService->ghiLogs(
            $hocSinhTN->toArray(),
            $request->isMethod('post') ? 'Tạo học sinh tốt nghiệp' : 'Cập nhật học sinh tốt nghiệp',
            $this->currentUser->id(),
            $this->currentUser->donViId()
        );
    }
}



public function getHocSinhTNById($id)
{
    try {

        $hocSinhTN = HocSinhTN::with(['hocSinh','xepLoaiHocTap', 'xepLoaiRenLuyen', 'xepLoaiTotNghiep'])
            ->where('_id', $id)
            ->first();

        if (!$hocSinhTN) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Không tìm thấy học sinh tốt nghiệp.',
            ], 404);
        }

        return response()->json([
            'Err' => false,
            'Result' => $hocSinhTN,
        ]);
    } catch (Exception $e) {
        \Log::error('Error fetching HocSinhTN by ID: ' . $e->getMessage());
        return response()->json([
            'Err' => true,
            'Msg' => 'Có lỗi xảy ra khi lấy thông tin học sinh tốt nghiệp.',
        ], 500);
    }
}
    public function luuthongtinBanHanh(Request $request, $quyetdinhid)
    {
        $quyetDinh = null;
        $thaoTac = 'Ban hành quyết định tốt nghiệp';

        try {
            $data = $request->only(['NgayBanHanh', 'NhanVienID', 'ChucVuQL_BH', 'NoiDungBH']);
            $quyetDinh = QuyetDinh::find($quyetdinhid);

            if (!$quyetDinh) {
                 return response()->json([
                'Err' => true,
                'Msg' => 'Không tìm thấy quyết định với ID đã cho',
            ], 404);
            }

            if (!empty($data['NgayBanHanh'])) {
                try {
                            $quyetDinh->NgayBanHanh = Carbon::createFromFormat('d/m/Y', $data['NgayBanHanh'])->startOfDay();
                            $quyetDinh->TrangThai = "20"; // Ban hành
                } catch (Exception $e) {
                    return response()->json([
                        'Err' => true,
                        'Msg' => 'Ngày ban hành không hợp lệ',
                    ], 422);
                }
            } else {
                $quyetDinh->NgayBanHanh = null;
            }

            // Assign other fields
            $quyetDinh->NhanVienID = $data['NhanVienID'] ?? null;
            $quyetDinh->ChucVuQL_BH = $data['ChucVuQL_BH'] ?? null;
            $quyetDinh->NoiDungBH = $data['NoiDungBH'] ?? null;

            // Clear thu hoi fields
            $quyetDinh->NgayThuHoi = null;
            $quyetDinh->NhanVienID_thuhoi = null;
            $quyetDinh->ChucVuQL_thuhoi = null;
            $quyetDinh->NoiDung_thuhoi = null;

            $quyetDinh->save();

            return response()->json([
                'Err' => false,
                'Msg' => 'Lưu thông tin ban hành thành công',
                'Result' => $quyetDinh,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Lỗi: ' . $e->getMessage(),
            ], 500);
        } finally {
            if ($quyetDinh !== null) {
                try {
                    $this->logService->ghiLogs(
                        $quyetDinh->toArray(),
                        $thaoTac,
                        $this->currentUser->id(),
                        $this->currentUser->donViId()
                    );
                } catch (\Exception $logEx) {
                    \Log::error('Ghi log thất bại: ' . $logEx->getMessage());
                }
            }
        }
    }

    public function luuthongtinThuHoi(Request $request, $quyetdinhid)
    {
        $quyetDinh = null;
        $thaoTac = 'Thu hồi quyết định tốt nghiệp';

        try {
            $data = $request->only(['NgayThuHoi', 'NhanVienID_thuhoi', 'ChucVuQL_thuhoi', 'NoiDung_thuhoi']);
            $quyetDinh = QuyetDinh::find($quyetdinhid);

            if (!$quyetDinh) {
                return response()->json([
                    'Err' => true,
                    'Msg' => 'Không tìm thấy quyết định với ID đã cho',
                ], 404);
            }

            // Parse NgayThuHoi (expecting d/m/Y format)
            if (!empty($data['NgayThuHoi'])) {
                try {
                    $quyetDinh->NgayThuHoi = Carbon::createFromFormat('d/m/Y', $data['NgayThuHoi'])->startOfDay();
                    $quyetDinh->TrangThai = "21"; // Ban hành
                } catch (\Exception $e) {
                    return response()->json([
                        'Err' => true,
                        'Msg' => 'Ngày thu hồi không hợp lệ',
                    ], 422);
                }
            } else {
                $quyetDinh->NgayThuHoi = null;
            }

            // Assign other thu hoi fields
            $quyetDinh->NhanVienID_thuhoi = $data['NhanVienID_thuhoi'] ?? null;
            $quyetDinh->ChucVuQL_thuhoi = $data['ChucVuQL_thuhoi'] ?? null;
            $quyetDinh->NoiDung_thuhoi = $data['NoiDung_thuhoi'] ?? null;
            $quyetDinh->save();

            return response()->json([
                'Err' => false,
                'Msg' => 'Lưu thông tin thu hồi thành công',
                'Result' => $quyetDinh,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Lỗi: ' . $e->getMessage(),
            ], 500);
        } finally {
            if ($quyetDinh !== null) {
                try {
                    $this->logService->ghiLogs(
                        $quyetDinh->toArray(),
                        $thaoTac,
                        $this->currentUser->id(),
                        $this->currentUser->donViId()
                    );
                } catch (\Exception $logEx) {
                    \Log::error('Ghi log thất bại: ' . $logEx->getMessage());
                }
            }
        }
    }

     /**
     * Xóa đối tượng
     */
    public function xoa(Request $request)
    {
        $model = null;
        $thaoTac = 'Xóa quyết định tốt nghiệp';

        try {
            $id = $request->input('ma');
            $model = QuyetDinh::whereKey($id)->firstOrFail();

            $model->delete();

            return response()->json([
                'Err' => false,
                'Msg' => 'Xóa quyết định thành công!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => $e instanceof \Illuminate\Database\Eloquent\ModelNotFoundException
                    ? 'Không tìm thấy quyết định để xóa!'
                    : 'Lỗi: ' . $e->getMessage(),
            ], 500);
        } finally {
            if ($model !== null) {
                try {
                    $this->logService->ghiLogs(
                        $model->toArray(),
                        $thaoTac,
                        $this->currentUser->id(),
                        $this->currentUser->donViId()
                    );
                } catch (\Exception $logEx) {
                    \Log::error('Ghi log thất bại: ' . $logEx->getMessage());
                }
            }
        }
    }
    public function xoaHocSinhTN(Request $request)
    {
        $hocSinhTN = null;
        $thaoTac = 'Xóa học sinh tốt nghiệp';

        try {
            $validator = Validator::make($request->all(), [
                'quyetDinhId' => 'required|string',
                'ma' => 'required|string',
            ]);
            if ($validator->fails()) {
                return response()->json([
                    'Err' => true,
                    'Msg' => $validator->errors()->first(),
                ], 422);
            }

            $data = $validator->validated();
            $hocSinhTN = HocSinhTN::find(new ObjectId($data['ma']));
            if (!$hocSinhTN) {
                return response()->json([
                    'Err' => true,
                    'Msg' => 'Không tìm thấy học sinh tốt nghiệp.',
                ], 404);
            }

            $hocSinhTN->delete();

            $quyetDinh = QuyetDinh::find($request->quyetDinhId);
            $hocSinhTNId = new ObjectId($data['ma']);
            
            if ($quyetDinh && is_array($quyetDinh->HocSinhTN)) {
                $hocSinhTNArray = $quyetDinh->HocSinhTN;

                // Filter out the deleted hocSinhTNId
                $hocSinhTNArray = array_filter($hocSinhTNArray, function ($id) use ($hocSinhTNId) {
                    return (string) $id !== (string) $hocSinhTNId;
                });

                // Re-index array if needed
                $quyetDinh->HocSinhTN = array_values($hocSinhTNArray);
                $quyetDinh->save();
            }

            return response()->json([
                'Err' => false,
                'Msg' => 'Xóa học sinh tốt nghiệp thành công.',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Lỗi: ' . $e->getMessage(),
            ], 500);
        } finally {
            if ($hocSinhTN !== null) {
                try {
                    $this->logService->ghiLogs(
                        $hocSinhTN->toArray(),
                        $thaoTac,
                        $this->currentUser->id(),
                        $this->currentUser->donViId()
                    );
                } catch (\Exception $logEx) {
                    \Log::error('Ghi log thất bại: ' . $logEx->getMessage());
                }
            }
        }
    }


#region Nhận excel
public function checkExcel(Request $request)
{
$path  = $request->input('path', '');
$sheet = $request->input('sheet', '');

if (! $path || ! $sheet) {
    return response()->json(['Err' => true, 'Msg' => 'Missing path or sheet'], 422);
}

$urlPath  = parse_url($path, PHP_URL_PATH);
$urlPath  = preg_replace('#^/+/#', '/', $urlPath);
$relative = Str::after($urlPath, '/storage/');

if (! Storage::disk('public')->exists($relative)) {
    return response()->json(['Err' => true, 'Msg' => 'File not found on server'], 404);
}
$fullPath = Storage::disk('public')->path($relative);

try {
    $reader      = IOFactory::createReaderForFile($fullPath);
    $reader->setReadDataOnly(true);
    $spreadsheet = $reader->load($fullPath);

    if (! $spreadsheet->sheetNameExists($sheet)) {
        return response()->json([
            'Err' => true,
            'Msg' => "Sheet “{$sheet}” không tồn tại"
        ], 422);
    }

    $ws   = $spreadsheet->getSheetByName($sheet);
    $rows = $ws->toArray(null, true, true, false);

    $header = array_map('trim', array_shift($rows));

    $required = [
        'Mã học sinh',
        'Họ và tên',
        'Số CMND/CCCD',
        'Ngày sinh',
        'Giới tính',
        'Nơi sinh',
        'Trường',
        'Lớp',
        'Thuộc diện ưu tiên',
        'Kết quả học tập',
        'Kết quả rèn luyện',
        'Kết quả tốt nghiệp',
        'Ghi chú',
    ];

    if ($missing = array_diff($required, $header)) {
        return response()->json([
            'Err' => true,
            'Msg' => 'Thiếu cột: ' . implode(', ', $missing),
        ], 422);
    }

    // Load allowed values for dropdown columns
    $allowedGioiTinh = ['Nam', 'Nữ', 'Khác'];
    $allowedThuocDien = DienUuTien::where('TrangThai', true)->pluck('TenDienUuTien')->toArray();

    $allowedTruong = DonVi::where('TrangThai', true)->pluck('TenDonVi')->toArray();
    $allowedXepLoai = XepLoai::where('trangThai', true)->pluck('tenXepLoai')->toArray();

    // Map column names to allowed value lists
    $dropdownColumns = [
        'Giới tính'           => $allowedGioiTinh,
        'Thuộc diện ưu tiên'  => $allowedThuocDien,
        'Trường'              => $allowedTruong,
        'Kết quả học tập'      => $allowedXepLoai,
        'Kết quả rèn luyện'    => $allowedXepLoai,
        'Kết quả tốt nghiệp'   => $allowedXepLoai,
    ];

    // Build header => index map for quick lookup
    $headerIndexes = array_flip($header);

    $errors = [];
    $assoc = [];

    foreach ($rows as $rowIndex => $r) {
        if (count($r) !== count($header)) {
            $errors[] = "Dòng " . ($rowIndex + 2) . ": số cột không đúng.";
            continue;
        }

        $rowAssoc = array_combine($header, $r);

        // Check dropdown columns
        foreach ($dropdownColumns as $colName => $allowedValues) {
            $val = trim($rowAssoc[$colName] ?? '');
            if ($val !== '') {
                // Convert both user input and allowed values to lowercase for comparison
                $valLower = mb_strtolower($val);
                $allowedLower = array_map('mb_strtolower', $allowedValues);

                if (!in_array($valLower, $allowedLower, true)) {
                    $errors[] = sprintf(
                        "Dòng %d: giá trị '%s' không hợp lệ tại cột '%s'. Giá trị hợp lệ: %s.",
                        $rowIndex + 2,
                        $val,
                        $colName,
                        implode(', ', $allowedValues)
                    );
                }
            }
        }

        $assoc[] = $rowAssoc;
    }

    session(['nhanvien_excel' => $assoc]);

    if ($errors) {
        return response()->json([
            'Err' => true,
            'Msg' => 'Có lỗi dữ liệu:',
            'Details' => $errors,
        ], 422);
    }

    return response()->json(['Err' => false]);

} catch (\Throwable $e) {
    return response()->json([
        'Err' => true,
        'Msg' => 'Lỗi đọc file: ' . $e->getMessage(),
    ], 500);
}
}


public function loadExcel()
{
$rows = collect(session('nhanvien_excel', []));

$data = $rows->map(function(array $r) {
    $val = trim($r['Ngày sinh'] ?? '');

// Check if the value is numeric (Excel serial date)
if (is_numeric($val)) {
    try {
        $dt = ExcelDate::excelToDateTimeObject((float)$val);
    } catch (\Throwable $e) {
        $dt = null;
    }
} elseif ($val !== '') {
    $dt = $this->parseDateFlexible($val);  // your flexible parser
} else {
    $dt = null;
}

        

    return [
        'MaHocSinh'       => $r['Mã học sinh'] ?? '',
        'HoVaTen'         => $r['Họ và tên'] ?? '',
        'SoCMND'          => $r['Số CMND/CCCD'] ?? '',
        'NgaySinh'        => $dt ? $dt->format('Y-m-d') : null,  // format as ISO string or your preferred format
        'GioiTinh'        => $r['Giới tính'] ?? '',
        'NoiSinh'         => $r['Nơi sinh'] ?? '',
        'Truong'          => $r['Trường'] ?? '',
        'Lop'             => $r['Lớp'] ?? '',
        'ThuocDienUuTien' => $r['Thuộc diện ưu tiên'] ?? '',
        'KetQuaHocTap'    => $r['Kết quả học tập'] ?? '',
        'KetQuaRenLuyen'  => $r['Kết quả rèn luyện'] ?? '',
        'KetQuaTotNghiep' => $r['Kết quả tốt nghiệp'] ?? '',
        'GhiChu'          => $r['Ghi chú'] ?? '',
        'TrangThai'       => 'Chờ nhận',
    ];
});

return response()->json($data);
}
function isValidObjectId($id) {
return is_string($id) && preg_match('/^[a-f\d]{24}$/i', $id);
}
protected function parseDateFlexible($dateInput): ?Carbon
{
// Handle Excel serial date numbers
if (is_numeric($dateInput)) {
    try {
        $dt = ExcelDate::excelToDateTimeObject((float)$dateInput);
        return Carbon::instance($dt);
    } catch (\Throwable $e) {
        // fall through to try parsing as string
    }
}

$dateString = trim((string)$dateInput);

$formats = [
    'd/m/Y',
    'd-m-Y',
    'd/m/y',
    'd-m-y',
    'Y-m-d',
    'Y/m/d',
    'y-m-d',
    'y/m/d',
];

foreach ($formats as $format) {
    try {
        $dt = Carbon::createFromFormat($format, $dateString);
        if ($dt !== false) {
            return $dt;
        }
    } catch (\Exception $e) {
        // try next format
    }
}

// fallback to generic parse (may still fail)
try {
    return Carbon::parse($dateString);
} catch (\Exception $e) {
    return null;
}

}

function findKeyByValue(array $array, string $value): ?string
{
$key = array_search(trim($value), $array);
return $key !== false ? $key : null;
}

public function importExcel(Request $request)
{
    $selected = $request->input('rows', []);
    $quyetDinhId = $request->input('quyetDinhId');

    if (! $quyetDinhId || ! $this->isValidObjectId($quyetDinhId)) {
        return response()->json(['Err' => true, 'Msg' => 'QuyetDinhID không hợp lệ'], 422);
    }
    $quyetDinhObjectId = new ObjectId($quyetDinhId);

    $allRows = collect(session('nhanvien_excel', []));

    $out = [];
    
    $truongOptions  = DonVi::where('TrangThai', true)
    ->get()
    ->mapWithKeys(function($item) {
        return [(string)$item->_id => $item->TenDonVi];
    })
    ->toArray();

    $xepLoaiOptions = XepLoai::where('trangThai', true)
    ->get()
    ->mapWithKeys(function($item) {
        return [(string)$item->_id => $item->tenXepLoai];
    })
    ->toArray();

    $DienUuTienOptions  = DienUuTien::where('TrangThai', true)
    ->get()
    ->mapWithKeys(function($item) {
        return [(string)$item->_id => $item->TenDienUuTien];
    })
    ->toArray();

    foreach ($selected as $idx) {
        $row = $allRows->get($idx);

        if (! $row || ! is_array($row)) {
            $out[$idx] = ['Err' => true, 'Msg' => 'Dữ liệu hàng không tồn tại'];
            continue;
        }

        $validator = Validator::make($row, [
            'Mã học sinh'  => 'required',
            'Họ và tên'    => 'required',
            'Số CMND/CCCD' => 'nullable',
            'Ngày sinh'   => 'required',
        ]);

        if ($validator->fails()) {
            $out[$idx] = ['Err' => true, 'Msg' => $validator->errors()->first()];
            continue;
        }
        $ngaySinh = $this->parseDateFlexible($row['Ngày sinh']);
        if (!$ngaySinh) {
            
            $out[$idx] = ['Err' => true, 'Msg' => 'Ngày sinh không hợp lệ' .  $row['Ngày sinh'] ];
            continue;  // skip this row
        }

        $doiTuong = DoiTuong::firstOrCreate(
            ['MaDoiTuong' => $row['Mã học sinh'], 'CCCD' => $row['Số CMND/CCCD']],
            [
                'Hovaten' => trim($row['Họ và tên']),
                'Ngaysinh' => $ngaySinh,
                'Gioitinh' => $row['Giới tính'] ?? null,
                'Noisinh' => $row['Nơi sinh'] ?? null,
                'LopHoc' => $row['Lớp'] ?? null, // if you have this field in your model, or map accordingly
                'DonViID_Hoc' => $truongId ? new ObjectId($truongId) : null, // link to school if you want
                'TrangThai' => true,
                'NgayThaoTac' => now(),
                'NgayCapNhat' => now(),
                'UserID_CapNhat' => new ObjectId(auth()->id()),
                'UserID_ThaoTac' => new ObjectId(auth()->id()),
            ]
        );

        $hocSinhTNExists = HocSinhTN::where('HocSinhID', (string)$doiTuong->_id)
            ->where('QuyetDinhId', $quyetDinhObjectId)
            ->first();

        if ($hocSinhTNExists) {
            $out[$idx] = ['Err' => true, 'Msg' => 'HocSinhTN đã tồn tại cho học sinh này và quyết định'];
            continue;
        }

        $truongName = trim($row['Trường'] ?? '');
        $truongId = $this->findKeyByValue($truongOptions, $truongName);
        if (!$truongId && $truongName !== '') {
            $newTruong = DonVi::create([
                'TenDonVi' => $truongName,
                'TrangThai' => true,
            ]);
            $truongId = (string) $newTruong->_id;

            // Update your options array so subsequent lookups work
            $truongOptions[$truongId] = $truongName;
        }
        
        $ketQuaHTId =$this-> findKeyByValue($xepLoaiOptions, $row['Kết quả học tập'] ?? '');
        $ketQuaRLId = $this->findKeyByValue($xepLoaiOptions, $row['Kết quả rèn luyện'] ?? '');
        $ketQuaTNId = $this->findKeyByValue($xepLoaiOptions, $row['Kết quả tốt nghiệp'] ?? '');
        $dienUuTienId =  $this->findKeyByValue($DienUuTienOptions, $row['Thuộc diện ưu tiên'] ?? '');
        HocSinhTN::create([
            'HocSinhID'       => (string)$doiTuong->_id,
            'QuyetDinhId'     => $quyetDinhObjectId,
            'DonVi_Truong'    => $truongId ? (string)$truongId : $row['Trường'],
            'KetQua_HT'       => $ketQuaHTId ? (string)$ketQuaHTId : null,
            'KetQua_RL'       => $ketQuaRLId ?(string) $ketQuaRLId : null,
            'KetQuaTN'        => $ketQuaTNId ? (string)$ketQuaTNId : null,
            'GhiChu'          => $row['Ghi chú'] ?? null,
            'ThuocDienUuTien' => $dienUuTienId ?? null,
        ]);

        $out[$idx] = ['Err' => false];
    }

    return response()->json($out);
}

public function loadSheetNames(Request $request)
{
    $path = $request->input('path');
    if (! $path) {
        return response()->json(['Err'=>true,'Msg'=>'Missing file path'], 422);
    }

    // 1) extract just the path part, e.g. "//storage/uploads/…"
    $urlPath = parse_url($path, PHP_URL_PATH);

    // 2) collapse multiple leading slashes: "/storage/uploads/…"
    $urlPath = preg_replace('#^/+/#','/', $urlPath);

    // 3) grab everything after "/storage/" → "uploads/…"
    $relative = Str::after($urlPath, '/storage/');

    // 4) now check on the public disk
    if (! Storage::disk('public')->exists($relative)) {
        return response()->json(['Err'=>true,'Msg'=>'File not found on disk'], 404);
    }
    
    $fullPath = Storage::disk('public')->path($relative);

    try {
        // create the best reader for this file
        $reader = IOFactory::createReaderForFile($fullPath);
        // only list sheet names, no data
        $sheetNames = $reader->listWorksheetNames($fullPath);
        $Result = array_map(fn($name) => ['TenSheet' => $name], $sheetNames);

        $payload = [
            'CanhBao'     => false,
            'Xem'         => false,
            'Them'        => false,
            'Sua'         => false,
            'Xoa'         => false,
            'InAn'        => false,
            'Nap'         => false,
            'Quyen1'      => false,
            'Quyen2'      => false,
            'Quyen3'      => false,
            'Err'         => false,
            'ErrCode'     => '',
            'ErrCatch'    => '',
            'Result'      => $Result,
            'Msg'         => '',
            'Logs'        => '',
            'Redirect'    => false,
            'RedirectUrl' => '',
        ];
    
        // encode as JSON string
        $json = json_encode($payload);

        // return it as a plain-text response
        return response($json, 200)
            ->header('Content-Type', 'text/html');
    } catch (\Throwable $e) {
        // return the real exception message so we can see why it fails
        return response()->json([
            'Err' => true,
            'Msg' => 'Error reading file: ' . $e->getMessage()
        ], 500);
    }
}
public function downloadTemplate()
{
    return Excel::download(
        new HocSinhTNTemplateExport,
        'MauExcel_HocSinhTN.xlsx'
    );
}
#endregion
#endregion
}
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class HocSinhTNTemplateExport implements FromCollection, WithHeadings, ShouldAutoSize, WithStyles, WithEvents
{
/** No data rows, just headings */
public function collection()
{
    return collect();
}

/** The template’s columns */
public function headings(): array
{
    return [
        'Mã học sinh',
        'Họ và tên',
        'Số CMND/CCCD',
        'Ngày sinh',
        'Giới tính',
        'Nơi sinh',
        'Trường',
        'Lớp',
        'Thuộc diện ưu tiên',
        'Kết quả học tập',
        'Kết quả rèn luyện',
        'Kết quả tốt nghiệp',
        'Ghi chú',
    ];
}
public function styles(Worksheet $sheet)
{
    // Find column indexes for required fields (1-based index)
    $headings = $this->headings();
    $colHoVaTen = array_search('Họ và tên *', $headings) + 1;
    $colCMND = array_search('Số CMND/CCCD *', $headings) + 1;

    return [
        1 => [ // header row
            'font' => [
                'name' => 'Times New Roman',
                'bold' => true,
                'size' => 12,
            ],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'D9E1F2'], // light blue
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
                        'wrapText' => true, // enable text wrapping

            ],
        ],
        'B1' => [ // specifically style cell B1
            'font' => [
                'color' => ['rgb' => 'FF0000'],
                'name' => 'Times New Roman',
                'bold' => true,
                'size' => 12,
            ],
        ],
        'C1' => [ // specifically style cell C1
            'font' => [
                'color' => ['rgb' => 'FF0000'],
                'name' => 'Times New Roman',
                'bold' => true,
                'size' => 12,
            ],
        ],
            
    ];
}

// Helper function: convert 1-based col number to Excel column letter(s)
protected function numToColumn(int $num): string
{
    $letters = '';
    while ($num > 0) {
        $num--;
        $letters = chr(65 + ($num % 26)) . $letters;
        $num = intval($num / 26);
    }
    return $letters;
}


    public function registerEvents(): array
{
    return [
        AfterSheet::class => function(AfterSheet $event) {
            $sheet = $event->sheet->getDelegate();

            // hard-coded columns
            $colGioiTinh          = 'E';
            $colThuocDienUuTien   = 'I';
            $colKetQuaHocTap      = 'J';
            $colKetQuaRenLuyen    = 'K';
            $colKetQuaTotNghiep   = 'L';
            $colTruong            = 'G';

            // fixed-list dropdowns
            $applyDropdown = function($col, array $options) use ($sheet) {
                for ($row = 2; $row <= 1000; $row++) {
                    $cell = $sheet->getCell("{$col}{$row}");
                    $dv = $cell->getDataValidation();
                    $dv->setType(DataValidation::TYPE_LIST);
                    $dv->setErrorStyle(DataValidation::STYLE_STOP);
                    $dv->setAllowBlank(true);
                    $dv->setShowInputMessage(true);
                    $dv->setShowErrorMessage(true);
                    $dv->setShowDropDown(true);
                    $dv->setFormula1('"' . implode(',', $options) . '"');
                    $cell->setDataValidation($dv);
                }
            };

            // 1) fixed
            $applyDropdown($colGioiTinh,        ['Nam','Nữ','Khác']);
            $applyDropdown($colThuocDienUuTien, ['Có','Không']);

            // 2) dynamic — only name fields
            $truongOptions  = DonVi::where('TrangThai', true)
                                ->pluck('TenDonVi')
                                ->toArray();

            $xepLoaiOptions = XepLoai::where('trangThai', true)
                                    ->pluck('tenXepLoai')
                                    ->toArray();

            $applyDropdown($colTruong,         $truongOptions);
            $applyDropdown($colKetQuaHocTap,   $xepLoaiOptions);
            $applyDropdown($colKetQuaRenLuyen, $xepLoaiOptions);
            $applyDropdown($colKetQuaTotNghiep,$xepLoaiOptions);
        },
    ];
}
}