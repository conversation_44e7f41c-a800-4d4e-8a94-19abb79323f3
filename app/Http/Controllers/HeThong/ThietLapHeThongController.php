<?php

namespace App\Http\Controllers\HeThong;
use App\Http\Controllers\Controller;
use Storage;
use Str;
use App\Services\ThongBao;
use App\Models\HeThong\ThietLapHeThong;
use App\Models\CongThongTin\ThietLapWebsite;
use App\Services\DungChungDb;
use Illuminate\Http\Request;
use MongoDB\BSON\ObjectId;
use PhpOffice\PhpWord\TemplateProcessor;
use Illuminate\Support\Facades\Auth;
use App\Services\DungChungNoDb;
use App\Services\CurrentUserService;
use Carbon\Carbon;

class ThietLapHeThongController extends Controller
{
    protected DungChungDb $dungChungDb;
    protected DungChungNoDb $logService;
    protected $currentUser;
    public function __construct(DungChungDb $dungChungDb, DungChungNoDb $logService, CurrentUserService $currentUser)
    {
        $this->dungChungDb = $dungChungDb;
        $this->logService = $logService;
        $this->currentUser = $currentUser;
    }

    public function index()
    {
        return view('hethong.thietlaphethong.index');
    }

    public function getdata()
    {
        try {
            $userId = auth()->id() ? new ObjectId(auth()->id()) : null;

            $result = ThietLapHeThong::where('UserID', $userId)->get();
            if ($result->count() == 0) {
                return response()->json([
                    'Err' => true,
                    'Result' => $result,
                    'Msg' => "Không có dữ liệu!",
                    'canhbao' => "Không có dữ liệu!",
                ]);
            }
            // Map lại từng dòng để thêm Tên Cha
            return response()->json([
                'Err' => false,
                'Result' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Không thể lấy danh sách!',
                'debug' => $e->getMessage(),
            ]);
        }
    }
    public function luuthongtin(Request $request)
    {
        try {
            $DonViCapTren = $request->input('DonViCapTren');
            $DonViBaoCao = $request->input('DonViBaoCao');
            $KyThay = $request->input('KyThay');
            $ChucDanhNguoiKy = $request->input('ChucDanhNguoiKy');
            $NguoiKT = $request->input('NguoiKT');
            $ChucDanhNguoiKT = $request->input('ChucDanhNguoiKT');
            $NguoiLap = $request->input('NguoiLap');
            $ChucDanhNguoiLap = $request->input('ChucDanhNguoiLap');
            $DiaDanh = $request->input('DiaDanh');
            $DangNgayBaoCao = $request->input('DangNgayBaoCao');
            $NgayLapBaoCao1 = $request->input('NgayLapBaoCao1');
            $NgayLapBaoCao2 = $request->input('NgayLapBaoCao2');
            $TinhID = (is_string($request->input('TinhID')) && preg_match('/^[a-f\d]{24}$/i', $request->input('TinhID')))
                ? new ObjectId($request->input('TinhID'))
                : null;
            $XaID = (is_string($request->input('XaID')) && preg_match('/^[a-f\d]{24}$/i', $request->input('XaID')))
                ? new ObjectId($request->input('XaID'))
                : null;
            $ThonID = (is_string($request->input('ThonID')) && preg_match('/^[a-f\d]{24}$/i', $request->input('ThonID')))
                ? new ObjectId($request->input('ThonID'))
                : null;
            $NguoiKy = $request->input('NguoiKy');
            $userId = auth()->id() ? new ObjectId(auth()->id()) : null;
            $donViId = $this->currentUser->donViId() ? new ObjectId($this->currentUser->donViId()) : null;
            $now = now();
            $result = ThietLapHeThong::where('UserID', $userId)->get();
            if ($result->count() == 0) {
                // Insert
                try {
                    $ngay = Carbon::createFromFormat('d/m/Y', $NgayLapBaoCao1);
                } catch (\Exception $e) {
                    // Lỗi định dạng, xử lý nếu cần
                    $ngay = null;
                }

                ThietLapHeThong::create([
                    'DonViCapTren' => $DonViCapTren,
                    'DonViBaoCao' => $DonViBaoCao,
                    'KyThay' => $KyThay,
                    'ChucDanhNguoiKy' => $ChucDanhNguoiKy,
                    'NguoiKT' => $NguoiKT,
                    'ChucDanhNguoiKT' => $ChucDanhNguoiKT,
                    'NguoiLap' => $NguoiLap,
                    'ChucDanhNguoiLap' => $ChucDanhNguoiLap,
                    'DiaDanh' => $DiaDanh,
                    'DangNgayBaoCao' => $DangNgayBaoCao,
                    'NgayLapBaoCao1' => $ngay,

                    'NgayLapBaoCao2' => $NgayLapBaoCao2,
                    'DiaBanHCID_Tinh' => $TinhID,
                    'DiaBanHCID_Xa' => $XaID,
                    'DiaBanHCID_Thon' => $ThonID,
                    'NguoiKy' => $NguoiKy,
                    'UserID' => $userId,
                    'DonViID' => $donViId,
                    'NgayThaoTac' => $now,
                    'NgayCapNhat' => $now,
                ]);

                return response()->json([
                    'Err' => false,
                    'Msg' => 'Thêm dữ liệu thành công!'
                ]);
            } else {
                // Update
                $ThietLapHeThong = ThietLapHeThong::where('UserID', $userId)->first();
                if (!$ThietLapHeThong) {
                    return response()->json([
                        'Err' => true,
                        'Msg' => 'Không tìm thấy dữ liệu cần cập nhật!'
                    ]);
                }
                try {
                    $ngay = Carbon::createFromFormat('d/m/Y', $NgayLapBaoCao1);
                } catch (\Exception $e) {
                    // Lỗi định dạng, xử lý nếu cần
                    $ngay = null;
                }
                // Insert
                $ThietLapHeThong->update([
                    'DonViCapTren' => $DonViCapTren,
                    'DonViBaoCao' => $DonViBaoCao,
                    'KyThay' => $KyThay,
                    'ChucDanhNguoiKy' => $ChucDanhNguoiKy,
                    'NguoiKT' => $NguoiKT,
                    'ChucDanhNguoiKT' => $ChucDanhNguoiKT,
                    'NguoiLap' => $NguoiLap,
                    'ChucDanhNguoiLap' => $ChucDanhNguoiLap,
                    'DiaDanh' => $DiaDanh,
                    'DangNgayBaoCao' => $DangNgayBaoCao,
                    'NgayLapBaoCao1' => $ngay,
                    'NgayLapBaoCao2' => $NgayLapBaoCao2,
                    'DiaBanHCID_Tinh' => $TinhID,
                    'DiaBanHCID_Xa' => $XaID,
                    'DiaBanHCID_Thon' => $ThonID,
                    'NguoiKy' => $NguoiKy,
                    'DonViID' => $donViId,
                    'NgayCapNhat' => $now,
                ]);
                return response()->json([
                    'Err' => false,
                    'Msg' => 'Cập nhật dữ liệu thành công!'
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra!',
                'debug' => $e->getMessage(),
            ]);
        }
    }

    public function luuthongtinCauHinh(Request $request)
    {
        try {
            $request->validate([
                'TieuDeWebsite' => 'nullable|string|max:255',
                'NoiDungChanTrang' => 'nullable|string',
                'FaviconUrl' => 'nullable|string',
                'LogoUrl' => 'nullable|string',
                'DiaChi' => 'nullable|string',
                'SoDienThoai' => 'nullable|string|max:20',
                'Email' => 'nullable|email|max:255',
                'Website' => 'nullable|url|max:255',
                'AnhDaiDienBaiViet' => 'nullable|string',
                'BannerLoginNho' => 'nullable|string',
                'BannerLoginMoi' => 'nullable|string',
                'AdminTimeOut' => 'nullable|string|max:255',

                'ck_YeuCauKiemDuyet' => 'nullable|boolean',
                'ck_QuanTriNgayTrenWebsite' => 'nullable|boolean',
                'ck_KhongHoiKhiRoiTrangChinhSua' => 'nullable|boolean',
                'ck_KhoaKhongChoCopyNoiDung' => 'nullable|boolean',
                'ck_XacThucTaiKhoan' => 'nullable|boolean',
                'ck_TamNgungTaiKhoan' => 'nullable|boolean',
                'ck_TuDongDuyetBai' => 'nullable|boolean',
                'ck_KiemDuyetBinhLuan' => 'nullable|boolean',
                'ck_CoDinhKhungWebsite' => 'nullable|boolean',
                'ck_KhoaBaiDang' => 'nullable|boolean',
                'ck_CoNutLenDauTrang' => 'nullable|boolean',
            ]);

            $userId = $request->input('UserID') ?? (auth()->id() ? auth()->id() : null);
            $donViId = $request->input('DonViID') ?? null;
            $now = now();
            $userName = $request->input('NguoiTao') ?? (auth()->user() ? auth()->user()->name : 'Admin');

            $thietLap = ThietLapWebsite::first();

            $commonData = [
                'TieuDeWebsite' => $request->input('TieuDeWebsite'),
                'NoiDungChanTrang' => $request->input('NoiDungChanTrang'),
                'FaviconUrl' => $request->input('FaviconUrl'),
                'LogoUrl' => $request->input('LogoUrl'),
                'DiaChi' => $request->input('DiaChi'),
                'SoDienThoai' => $request->input('SoDienThoai'),
                'Email' => $request->input('Email'),
                'Website' => $request->input('Website'),
                'AnhDaiDienBaiViet' => $request->input('AnhDaiDienBaiViet'),
                'BannerLoginNho' => $request->input('BannerLoginNho'),
                'BannerLoginMoi' => $request->input('BannerLoginMoi'),
                'AdminTimeOut' => $request->input('AdminTimeOut'),

                'ck_YeuCauKiemDuyet' => filter_var($request->input('ck_YeuCauKiemDuyet'), FILTER_VALIDATE_BOOLEAN),
                'ck_QuanTriNgayTrenWebsite' => filter_var($request->input('ck_QuanTriNgayTrenWebsite'), FILTER_VALIDATE_BOOLEAN),
                'ck_KhongHoiKhiRoiTrangChinhSua' => filter_var($request->input('ck_KhongHoiKhiRoiTrangChinhSua'), FILTER_VALIDATE_BOOLEAN),
                'ck_KhoaKhongChoCopyNoiDung' => filter_var($request->input('ck_KhoaKhongChoCopyNoiDung'), FILTER_VALIDATE_BOOLEAN),
                'ck_XacThucTaiKhoan' => filter_var($request->input('ck_XacThucTaiKhoan'), FILTER_VALIDATE_BOOLEAN),
                'ck_TamNgungTaiKhoan' => filter_var($request->input('ck_TamNgungTaiKhoan'), FILTER_VALIDATE_BOOLEAN),
                'ck_TuDongDuyetBai' => filter_var($request->input('ck_TuDongDuyetBai'), FILTER_VALIDATE_BOOLEAN),
                'ck_KiemDuyetBinhLuan' => filter_var($request->input('ck_KiemDuyetBinhLuan'), FILTER_VALIDATE_BOOLEAN),
                'ck_CoDinhKhungWebsite' => filter_var($request->input('ck_CoDinhKhungWebsite'), FILTER_VALIDATE_BOOLEAN),
                'ck_KhoaBaiDang' => filter_var($request->input('ck_KhoaBaiDang'), FILTER_VALIDATE_BOOLEAN),
                'ck_CoNutLenDauTrang' => filter_var($request->input('ck_CoNutLenDauTrang'), FILTER_VALIDATE_BOOLEAN),
            ];

            if (!$thietLap) {
                $thietLap = ThietLapWebsite::create(array_merge($commonData, [
                    'Ma' => $request->input('Ma', '2'),
                    'TenDonVi' => $request->input('TenDonVi'),
                    'Fax' => $request->input('Fax'),
                    'TenPhanMem' => $request->input('TenPhanMem'),
                    'TenPhienBan' => $request->input('TenPhienBan'),
                    'Facebook' => $request->input('Facebook'),
                    'Youtube' => $request->input('Youtube'),
                    'Banner' => $request->input('Banner'),
                    'NgayTao' => $now,
                    'UserID_NguoiTao' => $userId,
                    'NguoiTao' => $userName,
                    'DonViID' => $donViId,
                ]));

                $message = 'Thêm cấu hình website thành công!';
            } else {
                $thietLap->update(array_merge($commonData, [
                    'TenDonVi' => $request->input('TenDonVi'),
                    'Fax' => $request->input('Fax'),
                    'TenPhanMem' => $request->input('TenPhanMem'),
                    'TenPhienBan' => $request->input('TenPhienBan'),
                    'Facebook' => $request->input('Facebook'),
                    'Youtube' => $request->input('Youtube'),
                    'Banner' => $request->input('Banner'),
                    'NgayCapNhat' => $now,
                    'UserID_CapNhat' => $userId,
                ]));

                $message = 'Cập nhật cấu hình website thành công!';
            }

            return response()->json([
                'Err' => false,
                'Msg' => $message,
                'data' => $thietLap
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra!',
                'debug' => $e->getMessage(),
            ], 500);
        }
    }


    public function getdataCauHinh()
    {
        try {

            $result = ThietLapWebsite::createdBy(auth()->id())->get();
            if ($result->count() == 0) {
                return response()->json([
                    'Err' => true,
                    'Result' => $result,
                    'Msg' => "Không có dữ liệu!",
                    'canhbao' => "Không có dữ liệu!",
                ]);
            }
            return response()->json([
                'Err' => false,
                'Result' => $result,


            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Không thể lấy danh sách!',
                'debug' => $e->getMessage(),
            ]);
        }
    }
    public function moveFileToPermanent(Request $request)
    {
        $donViid = $this->currentUser->donViId();
        $username = $this->currentUser->username();
        $disk = Storage::disk('public');

        $permanentBase = "uploads/{$donViid}/{$username}/files/thietlapwebsite";
        $results = [];
        $paths = $request->input('paths', '');
        foreach (explode('|', $paths) as $p) {
            $p = trim($p);
            if (!$p) {
                continue;
            }

            // grab only the path portion (e.g. "/storage/uploads/…")
            $urlPath = parse_url($p, PHP_URL_PATH);

            // if it already lives under our permanent folder, keep it
            if (Str::startsWith($urlPath, "/storage/{$permanentBase}/")) {
                $results[] = $urlPath;
                continue;
            }

            // otherwise treat it as a "temp" file under /storage/…
            // strip leading "/" and "storage/" to get the disk key
            $diskKey = preg_replace('#^/storage/#', '', $urlPath);

            // build a new filename in the permanent folder
            $name = pathinfo($diskKey, PATHINFO_FILENAME);
            $ext = pathinfo($diskKey, PATHINFO_EXTENSION);
            $slug = Str::slug($name);
            $newName = "{$slug}-" . time() . ".{$ext}";
            $newKey = "{$permanentBase}/{$newName}";

            // move on the 'public' disk (move = copy + delete original)
            $disk->move($diskKey, $newKey);

            // push the new public URL
            $results[] = '/storage/' . $newKey;
        }

        // re‐implode with '|'
        return implode('|', $results);
    }
}