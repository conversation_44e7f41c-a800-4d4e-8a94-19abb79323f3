<?php

namespace App\Http\Controllers\BienTapCongThongTin;

use App\Http\Controllers\Controller;
use App\Models\CongThongTin\TinTuc;
use App\Models\CongThongTin\LoaiTinTuc;
use App\Services\DungChungDb;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class TinTucController extends Controller
{
    protected $dungChungDb;

    public function __construct(DungChungDb $dungChungDb)
    {
        $this->dungChungDb = $dungChungDb;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('bientapcongthongtin.tintuc.index');
    }

    /**
     * Get all tin tuc for Tabulator
     */
    public function getAll(Request $request)
    {
        try {
            $query = TinTuc::with('loaiTinTuc');

            // Apply filters
            if ($request->filled('loaiTinTucId')) {
                $query->where('LoaiTinTucID', $request->loaiTinTucId);
            }

            if ($request->filled('dangSD')) {
                $query->where('DangSD', $request->dangSD == '1');
            }

            if ($request->filled('laNoiBat')) {
                $query->where('LaNoiBat', $request->laNoiBat == '1');
            }

            $tinTucs = $query->orderBy('NgayTao', 'desc')->get();

            // Format data for Tabulator
            $data = $tinTucs->map(function ($item) {
                return [
                    'id' => $item->_id,
                    'TieuDe' => $item->TieuDe,
                    'LoaiTinTuc' => $item->loaiTinTuc ? $item->loaiTinTuc->TenLoaiTinTuc : 'Chưa phân loại',
                    'NguoiTao' => $item->NguoiTao,
                    'NgayTao' => $item->NgayTao ? Carbon::parse($item->NgayTao)->format('d/m/Y H:i') : '',
                    'LuotXem' => number_format($item->LuotXem ?? 0),
                    'DangSD' => $item->DangSD,
                    'LaNoiBat' => $item->LaNoiBat,
                    // Thêm thông tin kiểm duyệt
                    'TrangThaiKiemDuyet' => $item->TrangThaiKiemDuyet ?? 1,
                    'TenTrangThaiKiemDuyet' => $item->ten_trang_thai_kiem_duyet ?? 'Đã duyệt',
                    'MauTrangThaiKiemDuyet' => $item->mau_trang_thai_kiem_duyet ?? 'success',
                    'NguoiKiemDuyet' => $item->NguoiKiemDuyet,
                    'NgayKiemDuyet' => $item->NgayKiemDuyet ? Carbon::parse($item->NgayKiemDuyet)->format('d/m/Y H:i') : '',
                    'GhiChuKiemDuyet' => $item->GhiChuKiemDuyet,
                ];
            });

            return response()->json([
                'err' => false,
                'result' => $data
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'err' => true,
                'msg' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $request->validate([
                'TieuDe' => 'required|string|max:255',
                'LoaiTinTucID' => 'required|string',
                'NoiDungTomTat' => 'required|string',
                'NoiDung' => 'required|string',
                'HinhAnh' => 'nullable|string',
                'TuKhoa' => 'nullable|string',
                'DangSD' => 'boolean',
                'LaNoiBat' => 'boolean',
                'HienThiHDSD' => 'boolean',
                'DonViXem' => 'boolean',
                'MonHocIDs' => 'array',
                'HinhThucDaoTaoIDs' => 'array',
                'KyThiIDs' => 'array',
                'XepLoaiIDs' => 'array',
                'CapHocIDs' => 'array'
            ]);

            $data = [
                'TieuDe' => $request->TieuDe,
                'LoaiTinTucID' => $request->LoaiTinTucID,
                'NoiDungTomTat' => $request->NoiDungTomTat,
                'NoiDung' => $request->NoiDung,
                'HinhAnh' => $request->HinhAnh,
                'TuKhoa' => $request->TuKhoa,
                'DangSD' => $request->has('DangSD') ? true : false,
                'LaNoiBat' => $request->has('LaNoiBat') ? true : false,
                'HienThiHDSD' => $request->has('HienThiHDSD') ? true : false,
                'DonViXem' => $request->has('DonViXem') ? true : false,
                'DinhDanh' => Str::slug($request->TieuDe),
                'LuotXem' => 0,
                'NgayTao' => now(),
                'NguoiTao' => 'Admin', // auth()->user()->name ?? 'Admin',
                'UserID_NguoiTao' => null, // auth()->id(),
                'DonViID' => null, // auth()->user()->DonViID ?? null,
                'MonHocIDs' => $request->input('MonHocIDs', []),
                'HinhThucDaoTaoIDs' => $request->input('HinhThucDaoTaoIDs', []),
                'KyThiIDs' => $request->input('KyThiIDs', []),
                'XepLoaiIDs' => $request->input('XepLoaiIDs', []),
                'CapHocIDs' => $request->input('CapHocIDs', []),
                'created_at' => now(),
                'updated_at' => now()
            ];

            TinTuc::create($data);

            return response()->json([
                'Err' => false,
                'Msg' => 'Thêm tin tức thành công!',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        try {
            $tinTuc = TinTuc::with('loaiTinTuc')->findOrFail($id);
            return response()->json([
                'Err' => false,
                'data' => $tinTuc
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Không tìm thấy tin tức'
            ], 404);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        try {
            $request->validate([
                'TieuDe' => 'required|string|max:255',
                'LoaiTinTucID' => 'required|string',
                'NoiDungTomTat' => 'required|string',
                'NoiDung' => 'required|string',
                'HinhAnh' => 'nullable|string',
                'TuKhoa' => 'nullable|string',
                'DangSD' => 'boolean',
                'LaNoiBat' => 'boolean',
                'HienThiHDSD' => 'boolean',
                'MonHocIDs' => 'array',
                'HinhThucDaoTaoIDs' => 'array',
                'KyThiIDs' => 'array',
                'XepLoaiIDs' => 'array',
                'CapHocIDs' => 'array'
            ]);

            $tinTuc = TinTuc::findOrFail($id);

            // Xóa ảnh cũ nếu có ảnh mới và khác ảnh cũ
            if (
                !empty($request->HinhAnh) &&
                !empty($tinTuc->HinhAnh) &&
                $request->HinhAnh !== $tinTuc->HinhAnh
            ) {
                $this->deleteImageFile($tinTuc->HinhAnh);
            }

            $data = [
                'TieuDe' => $request->TieuDe,
                'LoaiTinTucID' => $request->LoaiTinTucID,
                'NoiDungTomTat' => $request->NoiDungTomTat,
                'NoiDung' => $request->NoiDung,
                'HinhAnh' => $request->HinhAnh,
                'TuKhoa' => $request->TuKhoa,
                'DangSD' => $request->has('DangSD') ? true : false,
                'LaNoiBat' => $request->has('LaNoiBat') ? true : false,
                'HienThiHDSD' => $request->has('HienThiHDSD') ? true : false,
                'DonViXem' => $request->has('DonViXem') ? true : false,
                'DinhDanh' => Str::slug($request->TieuDe),
                'MonHocIDs' => $request->input('MonHocIDs', []),
                'HinhThucDaoTaoIDs' => $request->input('HinhThucDaoTaoIDs', []),
                'KyThiIDs' => $request->input('KyThiIDs', []),
                'XepLoaiIDs' => $request->input('XepLoaiIDs', []),
                'CapHocIDs' => $request->input('CapHocIDs', []),
                'updated_at' => now()
            ];

            $tinTuc->update($data);

            return response()->json([
                'Err' => false,
                'Msg' => 'Cập nhật tin tức thành công!',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        try {
            $tinTuc = TinTuc::findOrFail($id);

            // Xóa ảnh liên quan nếu có
            if (!empty($tinTuc->HinhAnh)) {
                $this->deleteImageFile($tinTuc->HinhAnh);
            }

            $tinTuc->delete();

            return response()->json([
                'Err' => false,
                'Msg' => 'Xóa tin tức và ảnh thành công!',
            ]);
        } catch (\Exception $e) {
            Log::error('Lỗi khi xóa tin tức (destroy): ' . $e->getMessage(), [
                'id' => $id,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Lấy dữ liệu để sửa
     */
    public function loadDuLieuSua(Request $request)
    {
        try {
            $id = $request->input('id');
            $tinTuc = TinTuc::where('_id', $id)->first();

            if (!$tinTuc) {
                return response()->json([
                    'Err' => true,
                    'Msg' => 'Không tìm thấy tin tức'
                ]);
            }

            $result = [[
                'tinTucId' => $tinTuc->_id,
                'tieuDe' => $tinTuc->TieuDe,
                'loaiTinTucID' => $tinTuc->LoaiTinTucID,
                'hinhAnh' => $tinTuc->HinhAnh,
                'tuKhoa' => $tinTuc->TuKhoa,
                'noiDungTomTat' => $tinTuc->NoiDungTomTat,
                'noiDung' => $tinTuc->NoiDung,
                'dangSD' => $tinTuc->DangSD,
                'laNoiBat' => $tinTuc->LaNoiBat,
                'hienThiHDSD' => $tinTuc->HienThiHDSD,
                'donViXem' => $tinTuc->DonViXem,
                'ngayTao' => $tinTuc->NgayTao,
                'nguoiTao' => $tinTuc->NguoiTao,
                'dinhDanh' => $tinTuc->DinhDanh,
                'monHocIDs' => $tinTuc->MonHocIDs ?? [],
                'hinhThucDaoTaoIDs' => $tinTuc->HinhThucDaoTaoIDs ?? [],
                'kyThiIDs' => $tinTuc->KyThiIDs ?? [],
                'xepLoaiIDs' => $tinTuc->XepLoaiIDs ?? [],
                'capHocIDs' => $tinTuc->CapHocIDs ?? [],
            ]];

            return response()->json([
                'Err' => false,
                'Result' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Thêm mới hoặc cập nhật thông tin tin tức
     */
    public function luuThongTin(Request $request)
    {
        try {
            $data = $request->only([
                'tinTucId',
                'tieuDe',
                'loaiTinTucID',
                'hinhAnh',
                'tuKhoa',
                'noiDungTomTat',
                'noiDung',
                'dangSD',
                'laNoiBat',
                'hienThiHDSD',
                'donViXem',
                'ngayTao',
                'nguoiTao',
                'dinhDanh',
                'monHocIDs',
                'hinhThucDaoTaoIDs',
                'kyThiIDs',
                'xepLoaiIDs',
                'capHocIDs'
            ]);

            $isCreate = empty($data['tinTucId']);

            if ($isCreate) {
                $createData = [
                    'TieuDe' => $data['tieuDe'],
                    'LoaiTinTucID' => $data['loaiTinTucID'],
                    'HinhAnh' => $data['hinhAnh'],
                    'TuKhoa' => $data['tuKhoa'],
                    'NoiDungTomTat' => $data['noiDungTomTat'],
                    'NoiDung' => $data['noiDung'],
                    'DangSD' => $data['dangSD'] ?? false,
                    'LaNoiBat' => $data['laNoiBat'] ?? false,
                    'HienThiHDSD' => $data['hienThiHDSD'] ?? false,
                    'DonViXem' => $data['donViXem'] ?? false,
                    'MonHocIDs' => $data['monHocIDs'] ?? [],
                    'HinhThucDaoTaoIDs' => $data['hinhThucDaoTaoIDs'] ?? [],
                    'KyThiIDs' => $data['kyThiIDs'] ?? [],
                    'XepLoaiIDs' => $data['xepLoaiIDs'] ?? [],
                    'CapHocIDs' => $data['capHocIDs'] ?? [],
                    'DinhDanh' => $data['dinhDanh'] ?: (Str::slug($data['tieuDe']) . '-' . time()),
                    'LuotXem' => 0,
                    'NgayTao' => $data['ngayTao'] ? Carbon::parse($data['ngayTao']) : now(),
                    'NguoiTao' => $data['nguoiTao'] ?: 'Admin', // auth()->user()->name ?? 'Admin',
                    'UserID_NguoiTao' => null, // auth()->id(),
                    'DonViID' => null, // auth()->user()->DonViID ?? null,
                    'created_at' => now(),
                    'updated_at' => now()
                ];

                TinTuc::create($createData);
                $message = 'Thêm tin tức thành công!';
            } else {
                // Update existing record
                $tinTuc = TinTuc::findOrFail($data['tinTucId']);

                // Xóa ảnh cũ nếu có ảnh mới và khác ảnh cũ
                if (
                    !empty($data['hinhAnh']) &&
                    !empty($tinTuc->HinhAnh) &&
                    $data['hinhAnh'] !== $tinTuc->HinhAnh
                ) {
                    $this->deleteImageFile($tinTuc->HinhAnh);
                }

                $updateData = [
                    'TieuDe' => $data['tieuDe'],
                    'LoaiTinTucID' => $data['loaiTinTucID'],
                    'HinhAnh' => $data['hinhAnh'],
                    'TuKhoa' => $data['tuKhoa'],
                    'NoiDungTomTat' => $data['noiDungTomTat'],
                    'NoiDung' => $data['noiDung'],
                    'DangSD' => $data['dangSD'] ?? false,
                    'LaNoiBat' => $data['laNoiBat'] ?? false,
                    'HienThiHDSD' => $data['hienThiHDSD'] ?? false,
                    'DonViXem' => $data['donViXem'] ?? false,
                    'MonHocIDs' => $data['monHocIDs'] ?? [],
                    'HinhThucDaoTaoIDs' => $data['hinhThucDaoTaoIDs'] ?? [],
                    'KyThiIDs' => $data['kyThiIDs'] ?? [],
                    'XepLoaiIDs' => $data['xepLoaiIDs'] ?? [],
                    'CapHocIDs' => $data['capHocIDs'] ?? [],
                    'DinhDanh' => $data['dinhDanh'] ?: ($tinTuc->DinhDanh ?: (Str::slug($data['tieuDe']) . '-' . time())),
                    'updated_at' => now()
                ];

                $tinTuc->update($updateData);
                $message = 'Cập nhật tin tức thành công!';
            }

            return response()->json([
                'err' => false,
                'msg' => $message,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'err' => true,
                'msg' => 'Có lỗi xảy ra: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Xóa tin tức
     */
    public function xoa(Request $request)
    {
        try {
            $id = $request->input('ma');
            $tinTuc = TinTuc::findOrFail($id);

            // Xóa ảnh liên quan nếu có
            if (!empty($tinTuc->HinhAnh)) {
                $this->deleteImageFile($tinTuc->HinhAnh);
            }

            $tinTuc->delete();

            return response()->json([
                'err' => false,
                'msg' => 'Xóa tin tức và ảnh thành công!',
            ]);
        } catch (\Exception $e) {
            Log::error('Lỗi khi xóa tin tức: ' . $e->getMessage(), [
                'id' => $request->input('ma'),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'err' => true,
                'msg' => 'Có lỗi xảy ra: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Xóa nhiều tin tức
     */
    public function bulkDelete(Request $request)
    {
        try {
            $ids = $request->input('ids', []);

            if (empty($ids)) {
                return response()->json([
                    'err' => true,
                    'msg' => 'Không có dữ liệu để xóa',
                ]);
            }

            $deletedCount = 0;
            $errors = [];

            foreach ($ids as $id) {
                try {
                    $tinTuc = TinTuc::findOrFail($id);

                    // Xóa ảnh liên quan nếu có
                    if (!empty($tinTuc->HinhAnh)) {
                        $this->deleteImageFile($tinTuc->HinhAnh);
                    }

                    $tinTuc->delete();
                    $deletedCount++;
                } catch (\Exception $e) {
                    $errors[] = "Lỗi khi xóa ID {$id}: " . $e->getMessage();
                }
            }

            $message = "Đã xóa thành công {$deletedCount} tin tức và ảnh liên quan";
            if (!empty($errors)) {
                $message .= ". Lỗi: " . implode(', ', $errors);
            }

            return response()->json([
                'err' => false,
                'msg' => $message,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'err' => true,
                'msg' => 'Có lỗi xảy ra: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get combo data for dropdowns
     */
    public function getComboData()
    {
        try {
            $data = [
                'loaiTinTucs' => LoaiTinTuc::where('DangSD', true)->get(['_id', 'TenLoaiTinTuc']),
            ];

            return response()->json([
                'Err' => false,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra: ' . $e->getMessage(),
            ], 500);
        }
    }



    /**
     * Toggle status of tin tuc
     */
    public function toggleStatus(Request $request, $id)
    {
        try {
            $tinTuc = TinTuc::findOrFail($id);
            $field = $request->input('field', 'DangSD');

            $tinTuc->update([
                $field => !$tinTuc->$field,
                'updated_at' => now()
            ]);

            $statusText = $tinTuc->$field ? 'bật' : 'tắt';
            $fieldText = $field === 'DangSD' ? 'trạng thái sử dụng' : 'trạng thái nổi bật';

            return response()->json([
                'Err' => false,
                'Msg' => "Đã {$statusText} {$fieldText} cho tin tức thành công!",
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * API để xóa ảnh cũ khi chọn ảnh mới trong form biên tập
     */
    public function deleteOldImage(Request $request)
    {
        try {
            $request->validate([
                'tinTucId' => 'required|string',
            ]);

            $tinTuc = TinTuc::findOrFail($request->tinTucId);

            if (!empty($tinTuc->HinhAnh)) {
                $this->deleteImageFile($tinTuc->HinhAnh);

                return response()->json([
                    'success' => true,
                    'message' => 'Đã xóa ảnh cũ thành công',
                    'oldImagePath' => $tinTuc->HinhAnh
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Không có ảnh cũ để xóa'
            ]);
        } catch (\Exception $e) {
            Log::error('Lỗi khi xóa ảnh cũ: ' . $e->getMessage(), [
                'tinTucId' => $request->tinTucId,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi xóa ảnh cũ: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Xóa file ảnh từ storage
     */
    private function deleteImageFile($imagePath)
    {
        try {
            if (empty($imagePath)) {
                return;
            }

            // Chuyển đổi URL thành đường dẫn storage tương đối
            $relativePath = $this->convertUrlToStoragePath($imagePath);

            if ($relativePath && Storage::disk('public')->exists($relativePath)) {
                Storage::disk('public')->delete($relativePath);
                Log::info('Đã xóa file ảnh: ' . $relativePath);
            } else {
                Log::warning('Không tìm thấy file ảnh để xóa: ' . $imagePath);
            }
        } catch (\Exception $e) {
            Log::error('Lỗi khi xóa file ảnh: ' . $e->getMessage(), [
                'imagePath' => $imagePath
            ]);
        }
    }

    /**
     * Chuyển đổi URL ảnh thành đường dẫn storage tương đối
     */
    private function convertUrlToStoragePath($imageUrl)
    {
        if (empty($imageUrl)) {
            return null;
        }

        // Loại bỏ domain và /storage/ prefix
        $cleanPath = str_replace('\\', '/', trim($imageUrl));

        // Nếu là URL đầy đủ, lấy phần path
        if (filter_var($cleanPath, FILTER_VALIDATE_URL)) {
            $cleanPath = parse_url($cleanPath, PHP_URL_PATH);
        }

        // Loại bỏ '/storage/' prefix để có đường dẫn tương đối
        $relativePath = str_replace('/storage/', '', $cleanPath);
        $relativePath = ltrim($relativePath, '/');

        return $relativePath;
    }
}
